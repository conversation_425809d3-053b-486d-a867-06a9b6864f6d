import exp from "constants";
import { request } from "../request";
interface IaddressformData {
	testaddressName: string;
	phone: string;
	defaultAddress: false;
	fullAddress: string;
	testid: string | number | null;
}

//mockjs测试数据
export function list1(data: any) {
	return request.get("/api/user/list", data, {
		contentType: "application/json; charset=utf-8",
	});
}

// 登录
// export function login(data:{username:string,password:string}){
//   return request.post('/api/user/login',data,{
//     contentType:'application/json; charset=utf-8'
//   })
// }
export function login(data: {
	username: string;
	password: string;
	db: string;
}) {
	console.log(data, "data");

	return request.post("/zx/api/v1/login", data, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//获取用户信息
// export function getUserInfo(data:{token:string}){
//   console.log('getUserInfo',data);

//   return request.get('/api/user/userInfo?token='+data.token,data,{
//     contentType:'application/json; charset=utf-8'
//   })
// }
export function getUserInfo(data: { token: string }) {
	console.log("getUserInfo", data);

	return request.get("/zx/api/v1/user", data, {
		contentType: "application/x-www-form-urlencoded",
	});
}
/**修改密码 */
export function updatePassword(data: {
	userId: number | null;
	old_password: string;
	password: string;
}) {
	return request.patch("/api/admin/updatePassword", data, {
		contentType: "application/json; charset=utf-8",
	});
}

//获取用户收货地址
export function getAddress() {
	return request.get(`/zx/api/v1/address/`, null, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//获取用户默认收货地址
export function getDefaultAddress() {
	return request.get(`/zx/api/v1/address/`, null, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//新增用户收货地址
export function addAddress(data: {
	name: string;
	phone: string;
	address: string;
}) {
	return request.post("/zx/api/v1/address/", data, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//修改用户收货地址
export function updateAddress(
	body: {
		name: string;
		phone: string;
		address: string;
	},
	params: { id: string },
) {
	return request.put(`/zx/api/v1/address/${params.id}`, body, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//删除用户收货地址
export function deleteAddress(body: { id: string },params: { id: string },) {
	return request.delete(`/zx/api/v1/address/${params.id}`, body, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//获取热门产品
export function hotProduct(){
	return request.get(`/zx/api/v1/hot_product/`,null,{
		contentType:'application/x-www-form-urlencoded'
	})
}
//批量更新首页类别
export function putHomeCategory(body:{home_category:Array<object>}){
	return request.put(`/zx/api/v1/home_category`,body,{
		contentType:'application/x-www-form-urlencoded'
	})
}
//删除首页类别
export function deleteHomeCategory(params:{id:number}){
	return request.delete(`/zx/api/v1/home_category/${params.id}`,null,{
		contentType:'application/json'
	})
}
//创建首页类别
export function createHomeCategory(body:{text:string,sort:string}){
	return request.post(`/zx/api/v1/home_category`,body,{
		contentType:'application/x-www-form-urlencoded'
	})
}
// 查看库存详情
/**
 * query:查询条件 默认为全部
 * limit:每页显示条数  默认为0-10条
 */
export function getStock(
	data: { query?: string; limit?: string } = { query: "*", limit: `[0,10]` },
) {
	return request.get(
		`/api/stock/get?query=${data.query}&limit=${data.limit}`,
		null,
		{
			contentType: "application/json; charset=utf-8",
		},
	);
}
//查询库存条数
export function getStockCount(data: { query?: string } = { query: "*" }) {
	return request.get(`/api/stock/getCount?query=${data.query}`, null, {
		contentType: "application/json; charset=utf-8",
	});
}
//库存分类列表
export function getStockList() {
	return request.get("/api/stock/list", null, {
		contentType: "application/json; charset=utf-8",
	});
}
//查看用户历史订单
export function getOrder(params: {
	state: string;
	page: string | number;
	per_page?: string | number;
	name?: string;
	project?: string;
}) {
	return request.get("/zx/api/v1/orders", params, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//修改要货订单
export function updateOrder(
	data: {
		address?: string;
		note?: string;
		special_note?: string;
		project?:string;
		state?: string;
		add_lines?: JSON | string;
		mod_lines?: JSON | string;
		del_lines?: JSON | string;
	},
	params: { id: string | number },
) {
	return request.put(`/zx/api/v1/orders/${params.id}`, data, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//获取要货订单
export function getOrderMessage(params: { id: string | number }) {
	return request.get(`/zx/api/v1/orders/${params.id}`, null, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//删除用户待发货订单
/**
 *
 * @param data userId 用户id ordersId 订单id token 用户登录态
 */
// export function deleteOrder(data:{userId:string,ordersId:string,token:string}){
//   return request.delete('/api/order/delete',data,{
//     contentType:'application/json; charset=utf-8'
//   })
// }
//创建要货订单
interface IorderLines {
	product_id: number;
	product_qty: number;
	price_unit: number;
	fanhao_id?: number | string | undefined;
	zx_line_notes?: string;
	zx_mfplx?: string;
	project?: string;
}
export function submitOrder(data: {
	address?: string|undefined;
	note: string;
	special_note?: string;
	order_lines: Array<IorderLines> | string;
	project?: string;
}) {
	return request.post("/zx/api/v1/orders", data, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//获取用户购物车信息
export function getCart(data: { userId: string }) {
	return request.get("/api/cart/get", data, {
		contentType: "application/json; charset=utf-8",
	});
}
//获取产品分类
export function getCategory(params: { name?: string; model?: string }) {
	return request.get("/zx/api/v1/category", params, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//获取产品分类下属产品
export function getProduct(params: {
	name?: string;
	model?: string;
	page?: string;
	per_page?: string|number;
	findOne?: boolean;
	code?: string;
	ids?: string | Array<number>;
	category?:string
}) {
	return request.get("/zx/api/v1/products", params, {
		contentType: "application/x-www-form-urlencoded",
	});
}
//获取产品详情
export function getProductDetail(data: { productId: string }) {
	return request.get("/api/product/detail", data, {
		contentType: "application/json; charset=utf-8",
	});
}

//获取用户分类
export function getUserDefinedClass() {
	return request.get(`/zx/api/v1/home_category`, null, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//查询楼层
export function getProductByNumber(params: {
	name?: string;
	page?: string;
	per_page?: string;
}) {
	return request.get(`/zx/api/v1/seibans`, params, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//更新购物车
export function updateFindCart(body: {
	product_ids?: Array<any> | string;
	product_id?: string | number;
	product_qty?: string | number;
}) {
	return request.post(`/zx/api/v1/cart`, body, {
		contentType: "application/x-www-form-urlencoded",
	});
}

//获取购物车

export function getFindCart() {
	return request.get(`/zx/api/v1/cart`, null, {
		contentType: "application/x-www-form-urlencoded",
	});
}
