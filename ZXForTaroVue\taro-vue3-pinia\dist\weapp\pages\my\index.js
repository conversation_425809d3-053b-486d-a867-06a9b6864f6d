"use strict";(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[825],{9098:function(e,n,t){var r=t(1065),a=t(6944),l=t(5331),o=(t(1614),t(3031)),i=(t(670),t(3191)),u=t(6821),s=t(3221),c=t(7011),d=t(139),f=t(4884),v=Symbol("nut-avatar"),p=(0,d.c)("avatar"),g=p.create,w=g({props:{size:{type:[String,Number],default:"normal"},shape:{type:String,default:"round"},bgColor:{type:String,default:"#eee"},color:{type:String,default:"#666"}},setup:function(e){var n=(0,u.BK)(e),t=n.size,r=n.shape,a=n.bgColor,l=n.color,o=["large","normal","small"],c=(0,s.f3)(v,null),d=(0,u.iH)(null),f=(0,s.Fl)((function(){var e,n,a="nut-avatar";return(0,i.Z)((0,i.Z)((0,i.Z)({},a,!0),"nut-avatar-".concat(t.value||(null==(e=null==c?void 0:c.props)?void 0:e.size)||"normal"),!0),"nut-avatar-".concat(r.value||(null==(n=null==c?void 0:c.props)?void 0:n.shape)||"round"),!0)})),p=(0,s.Fl)((function(){var e,n;return{width:t.value in o?"":"".concat(t.value,"px"),height:t.value in o?"":"".concat(t.value,"px"),backgroundColor:"".concat(a.value),color:"".concat(l.value),marginLeft:(null==(e=null==c?void 0:c.props)?void 0:e.span)?"".concat(null==(n=null==c?void 0:c.props)?void 0:n.span,"px"):""}}));return{classes:f,styles:p,avatarRef:d}}});function m(e,n,t,r,a,l){return(0,s.wg)(),(0,s.iD)("view",{ref:"avatarRef",style:(0,c.j5)(e.styles),class:(0,c.C_)(e.classes)},[(0,s.WI)(e.$slots,"default")],6)}var x=(0,f._)(w,[["render",m]]),y=(t(3939),t(2419)),b=t(2810),h=t(9886),k=t(5969),S=t(1959),_=t.n(S),W=t(6144),j=t(4081),C={class:"my-box relative"},Z={class:"user-card flex p-8"},z=["src"],T={key:1,class:"ml-8 user-card-text"},I=(0,s.aZ)({__name:"index",setup:function(e){var n=(0,h.t)(),t=((0,s.Fl)((function(){return n.isLogin})),function(){_().redirectTo({url:"/pages/login/index"})}),r=(0,u.iH)(""),i=(0,u.iH)(""),d=function(){var e=(0,b.Z)((0,y.Z)().mark((function e(t){var a,l,o,u;return(0,y.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,W.bG)({token:t});case 2:u=e.sent,console.log("loginRes",u),_().setStorageSync("userInfo",null===u||void 0===u||null===(a=u.success)||void 0===a?void 0:a.user),r.value=null===(l=n.userInfo())||void 0===l?void 0:l.avator,i.value=null===(o=n.userInfo())||void 0===o?void 0:o.nickName;case 7:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,s.bv)((function(){d(_().getStorageSync("token"))}));var f=function(){n.resetAuthStore(),""==_().getStorageSync("token")&&_().redirectTo({url:"/pages/login/index"})},v=function(){_().navigateTo({url:"/package/package-a/harvestAddress/index?id=\u4f20\u9012\u8fc7\u53bb\u7528\u6237id",fail:function(e){console.log(e)}})},p=(0,j.qr)(),g=function(){p.setActiveTab("/pages/orders/index"),_().switchTab({url:"/pages/orders/index"})},w=((0,u.iH)({list:[{text:"\u5f85\u53d1\u8d27",icon:"i-bx-archive-in"},{text:"\u5df2\u53d1\u8d27",icon:"i-bx-archive"}]}),function(){_().showToast({title:"\u6682\u505c\u4f7f\u7528",icon:"error",duration:2e3})});return function(e,n){var d=x,p=o.Z,m=l.Z,y=a.Z;return(0,s.wg)(),(0,s.j4)(y,{"show-tab-bar":""},{default:(0,s.w5)((function(){return[(0,s._)("div",C,[(0,s._)("div",Z,[(0,s.Wm)(d,{style:{border:"1px solid #fff"},size:"large"},{default:(0,s.w5)((function(){return[r.value?((0,s.wg)(),(0,s.iD)("img",{key:1,src:r.value,style:{overflow:"hidden","border-radius":"50%"}},null,8,z)):((0,s.wg)(),(0,s.j4)((0,u.SU)(k.My),{key:0}))]})),_:1}),i.value?((0,s.wg)(),(0,s.iD)("view",T,(0,c.zw)(i.value),1)):((0,s.wg)(),(0,s.iD)("view",{key:0,class:"ml-8",onClick:t},n[0]||(n[0]=[(0,s._)("view",{class:"user-card-text font-600"},(0,c.zw)("\u70b9\u51fb\u767b\u5f55"),-1),(0,s._)("view",{class:"user-card-subtitle"},(0,c.zw)("\u53ef\u89e3\u9501\u5168\u90e8\u4fe1\u606f"),-1)])))]),(0,s.Wm)(m,{class:"ml-2 mr-2 justify-left border-rounded","column-num":1,direction:"horizontal"},{default:(0,s.w5)((function(){return[(0,s.Wm)(p,{text:"\u5386\u53f2\u8ba2\u5355",onClick:g,class:"justify-left"},{default:(0,s.w5)((function(){return[(0,s.Wm)((0,u.SU)(k.KM))]})),_:1}),(0,s.Wm)(p,{text:"\u4fee\u6539\u5bc6\u7801",onClick:w,class:"justify-left",disabled:""},{default:(0,s.w5)((function(){return[(0,s.Wm)((0,u.SU)(k.I8))]})),_:1}),(0,s.Wm)(p,{text:"\u8054\u7cfb\u5730\u5740",onClick:v,class:"justify-left",disabled:""},{default:(0,s.w5)((function(){return[(0,s.Wm)((0,u.SU)(k.Ej))]})),_:1}),(0,s.Wm)(p,{text:"\u9000\u51fa\u767b\u5f55",onClick:f,disabled:"false",class:"justify-left"},{default:(0,s.w5)((function(){return[(0,s.Wm)((0,u.SU)(k.tj))]})),_:1})]})),_:1}),n[1]||(n[1]=(0,s._)("div",{class:"user-button-text"},[(0,s._)("span",null,(0,c.zw)("\u7248\u6743\u6240\u6709 \xa9 \u5e7f\u4e1c\u5de6\u5411\u79d1\u6280\u6709\u9650\u516c\u53f8"))],-1))])]})),_:1})}}});const U=I;var D=U,H={navigationBarTitleText:"\u4e2a\u4eba\u4e2d\u5fc3"};Page((0,r.createPageConfig)(D,"pages/my/index",{root:{cn:[]}},H||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[107,216,592],(function(){return n(9098)}));e.O()}]);