<template>
    <BasicLayout left>
        <nut-form class="m-3" :model-value="updateFormData" ref="updateFormRef">
            <nut-form-item prop="password" required :rules="[{ required: true, message: '请输入新密码' }]">
                <nut-input v-model="updateFormData.password" placeholder="请输入新密码" type="password">
                    <template #left>
                        <Ask></Ask>
                    </template>
                </nut-input>
            </nut-form-item>
            <nut-form-item prop="repeatPassword" required
                :rules="[{ required: true, message: '请再次输入进行确认' }, { message: '两次输入密码不一致', validator: passwordValidator }]">
                <nut-input v-model="updateFormData.repeatPassword" placeholder="请再次输入进行确认" type="password">
                    <template #left>
                        <Ask></Ask>
                    </template>
                </nut-input>
            </nut-form-item>
            <nut-form-item prop="old_password" :rules="[{ required: true, message: '输入旧密码' }]">
                <nut-input v-model="updateFormData.old_password" placeholder="请输入旧密码" type="password" @blur="">
                    <template #left>
                        <Ask2></Ask2>
                    </template>
                </nut-input>
            </nut-form-item>
        </nut-form>
        <nut-button type="primary" @click="updatePasswordF" class="m-3" size="large">{{ '提交' }}</nut-button>

    </BasicLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Ask, Ask2 } from '@nutui/icons-vue-taro'
import { updatePassword } from '@/service/index';
import { useAuthStore } from '@/store/index';
import Taro from '@tarojs/taro';
const auth = useAuthStore()
definePageConfig({
    navigationBarTitleText: '修改密码'
});
const passwordValidator = (val: string) => {
    if (val === updateFormData.value.password) {
        return Promise.resolve()
    } else {
        return Promise.reject('两次输入密码不一致')
    }
}
const updateFormData = ref({
    password: '',
    repeatPassword: '',
    old_password: '',
})
const updateFormRef = ref(null)
const updatePasswordF = async () => {
    updateFormRef.value?.validate().then(async ({ valid, errors }: { valid: string, errors: any }) => {
        if (valid) {
            console.log('success:', valid)
            const res = await updatePassword({ userId: auth.userInfo.userId, password: updateFormData.value.password, old_password: updateFormData.value.old_password })
            if (res.error === null) {
                Taro.showToast({
                    title: '修改成功',
                    icon: 'success',
                    duration: 2000
                })
            }
            
        }
    })
}
</script>

<style >
.nut-input{
	color:#000000;
}
</style>