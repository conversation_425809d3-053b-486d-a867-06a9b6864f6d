"use strict";(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[257],{2270:function(e,n,t){var a=t(1065),o=t(6944),c=t(889),i=(t(9932),t(8701)),r=(t(959),t(7369)),u=(t(9251),t(9841)),l=(t(30),t(8751)),s=(t(2148),t(2344)),d=(t(2240),t(9328)),p=(t(8277),t(2018)),v=t(2419),f=t(2810),m=t(3221),_=t(7011),h=t(2e3),w=t(6821),x=t(1959),b=t.n(x),g=t(4733),k=t(4081),y={class:"header-container"},Z={class:"header-content"},C={class:"header-title"},T={class:"cart-count"},V={class:"action-text"},H={class:"cart-container"},z=["scroll-top"],W={class:"product-card"},U=["onClick"],B={class:"product-content"},S={class:"product-left"},F={class:"product-image-container"},j=["src"],E={key:0,class:"image-overlay"},I={class:"product-info"},O={class:"product-title-area"},q={class:"model-tag"},A={class:"name-tag"},D={class:"product-spec"},P={class:"spec-text"},R={class:"price-code-section"},J={class:"price-area"},Y={class:"price"},K={class:"code-area"},M={class:"code-tag"},N={class:"product-spec"},G={key:1,class:"empty-cart-state"},L={class:"empty-content"},Q={class:"bottom-action-bar"},X={class:"action-bar-content"},$={class:"price-section"},ee={class:"total-price"},ne={class:"button-section"},te={class:"bottom-action-bar"},ae={class:"action-bar-content"},oe={class:"manage-buttons"},ce=(0,m.aZ)({__name:"index",setup:function(e){var n=(0,w.iH)(!1),t=(0,w.iH)(!1),a=(0,w.iH)([]),ce=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(){var n,t,a,o,c;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,g.hT)();case 2:if(n=e.sent,t=n.error,a=n.success,null!==t||!a){e.next=14;break}if(console.log("getFindCartF",a),c=a,Te.value=(null===(o=c.items)||void 0===o?void 0:o.length)||0,!c.items){e.next=12;break}return e.next=12,We(c.items.map((function(e){return e.product_id})),c.items);case 12:e.next=15;break;case 14:b().showToast({title:"\u83b7\u53d6\u8d2d\u7269\u8f66\u5931\u8d25",icon:"error",duration:2e3});case 15:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=(0,k.qr)();(0,m.bv)((function(){x.eventCenter.on((0,x.getCurrentInstance)().router.onShow,(function(){ie.setActiveTab("/pages/shoppingCart/index"),ce(),a.value.forEach((function(e){e["checkbox"]=!1})),de()}))})),(0,m.Ah)((function(){x.eventCenter.off((0,x.getCurrentInstance)().router.onShow)}));var re=(0,w.iH)(0),ue=(0,w.iH)([]),le=function(){be.value=!0},se=-1,de=function(){re.value=a.value.filter((function(e){return e.checkbox})).reduce((function(e,n){return e+n.price_unit*n.count}),0)},pe=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(){var n,t;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.value.splice(a.value.findIndex((function(e){return e.id===se})),1),e.next=3,(0,g.tB)({product_id:-se});case 3:n=e.sent,t=n.error,null===t?b().showToast({title:"\u5220\u9664\u6210\u529f",icon:"success",duration:2e3}):b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ve=function(){var e;null===(e=ue.value[a.value.findIndex((function(e){return e.id===se}))])||void 0===e||e.close()},fe=function(){var e=a.value.map((function(e){return e.checkbox})).filter((function(e){return e})).length;n.value=e===a.value.length,de()},me=(0,w.iH)([]);function _e(e,n){console.log("array",e,n);var t=e.findIndex((function(e){return e[0]===n[0]}));console.log("index",t),-1!==t?e[t]=[e[t][0],e[t][1]+n[1]]:e.push(n)}var he=function(e){a.value.forEach((function(n){e===n.id&&(1!==n.count?(_e(me.value,[n.id,-1]),n.count-=1):b().showToast({title:"\u6700\u5c11\u8d2d\u4e70\u4e00\u4e2a\u5546\u54c1~",icon:"none",duration:2e3}))})),de()},we=function(e){a.value.forEach((function(n){e===n.id&&(n.count!==n.maxBuy?(_e(me.value,[n.id,1]),n.count=Number(n.count)+1):b().showToast({title:"\u5f53\u524d\u6700\u591a\u4ec5\u80fd\u8d2d\u4e70"+n.maxBuy+"\u4efd\u8be5\u5546\u54c1~",icon:"none",duration:2e3}))})),de()},xe=function(){n.value?a.value.forEach((function(e){e.checkbox=!0})):a.value.forEach((function(e){e.checkbox=!1}))},be=(0,w.iH)(!1),ge=function(){var e=a.value.filter((function(e){return e.checkbox}));console.log("\u8ba2\u5355\u4fe1\u606f",e),b().navigateTo({url:"/package/package-a/confirmOrder/index"}),b().preload({message:e,fromSCartPage:!0})},ke=(0,w.iH)(0),ye=function(e){console.log("upper:",e)},Ze=function(e){console.log("lower:",e)},Ce=function(e){console.log("scroll:",e)},Te=(0,w.iH)(0),Ve=(0,w.iH)(!0),He=(0,w.iH)(new Map([[!0,"\u7ba1\u7406"],[!1,"\u9000\u51fa"]])),ze=function(){console.log("manage"),Ve.value=!Ve.value},We=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(n,t){var o,c,i,r;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,g.wv)({ids:n});case 2:o=e.sent,c=o.success,i=c,r=null===i||void 0===i?void 0:i.items,console.log(c,r),c&&r&&(a.value=r.map((function(e){return(0,p.Z)((0,p.Z)({},e),{},{checkbox:!1,maxBuy:1e4,count:1})})),t.forEach((function(e){var n=a.value.find((function(n){return n.id===e.product_id}));n&&(n.count=e.product_qty)})),console.log("cartCommodities.value!!!!",a.value));case 8:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),Ue=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(){var n,t,o,c,i;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=a.value.filter((function(e){return e.checkbox})),console.log(n,"~~~"),t=n.map((function(e){return[-e.id,e.count]})),o="["+t.map((function(e){return"("+e.join(",")+")"})).join(",")+"]",e.next=6,(0,g.tB)({product_ids:o});case 6:if(c=e.sent,i=c.error,null!==i){e.next=15;break}return b().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),e.next=12,ce();case 12:de(),e.next=16;break;case 15:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Be=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(){var n,t,a;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==me.value.length){e.next=3;break}return b().showToast({title:"\u6ca1\u6709\u9700\u8981\u4fdd\u5b58\u7684\u4fee\u6539",icon:"none",duration:2e3}),e.abrupt("return");case 3:return n="["+me.value.map((function(e){return"("+e.join(",")+")"})).join(",")+"]",e.next=6,(0,g.tB)({product_ids:n});case 6:if(t=e.sent,a=t.error,null!==a){e.next=15;break}return b().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),me.value=[],e.next=13,ce();case 13:e.next=16;break;case 15:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Se=function(){var e=(0,f.Z)((0,v.Z)().mark((function e(n,t){var a,o;return(0,v.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,g.tB)({product_id:-n,product_qty:t});case 2:if(a=e.sent,o=a.error,null!==o){e.next=11;break}return b().showToast({title:"\u5220\u9664\u6210\u529f",icon:"success",duration:2e3}),e.next=8,ce();case 8:de(),e.next=12;break;case 11:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 12:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),Fe=function(){b().switchTab({url:"/pages/index/index"})};return function(e,p){var v=d.Z,f=s.Z,w=l.Z,x=u.Z,b=r.Z,g=i.Z,k=c.Z,ce=o.Z;return(0,m.wg)(),(0,m.iD)(m.HY,null,[(0,m.Wm)(v,{top:"0",class:"header-sticky"},{default:(0,m.w5)((function(){return[(0,m._)("div",y,[(0,m._)("div",Z,[(0,m._)("div",C,[p[5]||(p[5]=(0,m._)("i",{class:"i-bx-cart-alt header-icon"},null,-1)),p[6]||(p[6]=(0,m._)("span",{class:"title-text"},"\u8d2d\u7269\u8f66",-1)),(0,m._)("span",T,"("+(0,_.zw)(Te.value)+")",1)]),(0,m._)("div",{class:"header-action",onClick:ze},[p[7]||(p[7]=(0,m._)("i",{class:"i-bx-cog action-icon"},null,-1)),(0,m._)("span",V,(0,_.zw)(He.value.get(Ve.value)),1)])])])]})),_:1}),(0,m.Wm)(ce,{"show-tab-bar":""},{default:(0,m.w5)((function(){return[(0,m.Wm)(f,{title:"\u63d0\u4ea4\u8ba2\u5355",content:"\u786e\u8ba4\u63d0\u4ea4\u8ba2\u8d27\u4fe1\u606f\u5e76\u751f\u6210\u8ba2\u5355\u5417\uff1f",visible:be.value,"onUpdate:visible":p[0]||(p[0]=function(e){return be.value=e}),onOk:ge},null,8,["visible"]),(0,m._)("div",H,[a.value.length>0?((0,m.wg)(),(0,m.j4)(g,{key:0,lock:"",style:{width:"100vw"}},{default:(0,m.w5)((function(){return[(0,m._)("scroll-view",{"scroll-y":!0,style:{height:"calc(100%-58rpx)","margin-bottom":"60rpx"},onScrolltoupper:ye,onScrolltolower:Ze,onScroll:Ce,"scroll-top":ke.value},[((0,m.wg)(!0),(0,m.iD)(m.HY,null,(0,m.Ko)(a.value,(function(e){return(0,m.wg)(),(0,m.j4)(b,{ref_for:!0,ref_key:"swipeRefs",ref:ue,name:e.id.toString(),key:e.id,class:"product-swipe"},{default:(0,m.w5)((function(){return[(0,m._)("div",W,[(0,m.wy)((0,m._)("div",{class:"delete-btn",onClick:function(n){return Se(e.id,e.count)}},p[8]||(p[8]=[(0,m._)("i",{class:"i-bx-trash"},null,-1)]),8,U),[[h.F8,!Ve.value]]),(0,m._)("div",B,[(0,m._)("div",S,[(0,m.Wm)(w,{onChange:fe,modelValue:e.checkbox,"onUpdate:modelValue":function(n){return e.checkbox=n},"icon-size":"18",class:"product-checkbox"},null,8,["modelValue","onUpdate:modelValue"]),(0,m._)("div",F,[(0,m._)("image",{src:e.image,class:"product-image"},null,8,j),e.image?(0,m.kq)("",!0):((0,m.wg)(),(0,m.iD)("div",E,p[9]||(p[9]=[(0,m._)("i",{class:"i-bx-image-alt"},null,-1)])))])]),(0,m._)("div",I,[(0,m._)("div",O,[(0,m._)("div",q,[p[10]||(p[10]=(0,m._)("i",{class:"i-bx-crown"},null,-1)),(0,m._)("span",null,(0,_.zw)(e.model),1)]),(0,m._)("div",A,(0,_.zw)(e.name.split("/")[0]),1),(0,m.wy)((0,m._)("div",{class:"sub-name-tag"},(0,_.zw)(e.name.split("/")[1]),513),[[h.F8,e.name.split("/")[1]]])]),(0,m._)("div",D,[(0,m._)("span",P,(0,_.zw)(e.spec),1)]),(0,m._)("div",R,[(0,m._)("div",J,[p[11]||(p[11]=(0,m._)("span",{class:"currency"},"\xa5",-1)),(0,m._)("span",Y,(0,_.zw)(e.price_unit),1),p[12]||(p[12]=(0,m._)("span",{class:"unit"},"/PCS",-1))]),(0,m._)("div",K,[(0,m._)("span",M,(0,_.zw)(e.code),1)])]),(0,m._)("div",N,[(0,m.Wm)(x,{modelValue:e.count,"onUpdate:modelValue":function(n){return e.count=n},min:1,max:2e5,onBlur:p[1]||(p[1]=function(e){return de()}),onAdd:function(){return we(e.id)},onReduce:function(){return he(e.id)},class:"custom-input-number"},null,8,["modelValue","onUpdate:modelValue","onAdd","onReduce"])])])])])]})),_:2},1032,["name"])})),128)),p[13]||(p[13]=(0,m._)("div",{class:"footer-info"},[(0,m._)("div",{class:"company-info"},[(0,m._)("i",{class:"i-bx-copyright"}),(0,m._)("span",null,"\u6280\u672f\u652f\u6301 \xa9 \u5e7f\u4e1c\u5de6\u5411\u79d1\u6280\u6709\u9650\u516c\u53f8")])],-1))],40,z)]})),_:1})):((0,m.wg)(),(0,m.iD)("div",G,[(0,m._)("div",L,[p[15]||(p[15]=(0,m._)("div",{class:"empty-icon"},[(0,m._)("i",{class:"i-bx-cart-alt"})],-1)),p[16]||(p[16]=(0,m._)("div",{class:"empty-title"},"\u8d2d\u7269\u8f66\u7a7a\u7a7a\u5982\u4e5f",-1)),p[17]||(p[17]=(0,m._)("div",{class:"empty-description"},"\u5feb\u53bb\u6311\u9009\u5fc3\u4eea\u7684\u5546\u54c1\u5427",-1)),(0,m.Wm)(k,{class:"go-shopping-btn",type:"primary",onClick:Fe},{default:(0,m.w5)((function(){return p[14]||(p[14]=[(0,m._)("i",{class:"i-bx-shopping-bag"},null,-1),(0,m._)("span",null,"\u53bb\u8d2d\u7269",-1)])})),_:1})])]))]),(0,m.wy)((0,m._)("div",Q,[(0,m._)("div",X,[(0,m._)("div",{class:"select-all-section",onClick:xe},[(0,m.Wm)(w,{modelValue:n.value,"onUpdate:modelValue":p[2]||(p[2]=function(e){return n.value=e}),"icon-size":"22"},null,8,["modelValue"]),p[18]||(p[18]=(0,m._)("span",{class:"select-all-text"},"\u5168\u9009",-1))]),(0,m._)("div",$,[p[19]||(p[19]=(0,m._)("span",{class:"total-label"},"\u5408\u8ba1:",-1)),(0,m._)("span",ee,"\xa5"+(0,_.zw)(re.value.toFixed(2)),1)]),(0,m._)("div",ne,[(0,m.Wm)(k,{class:"create-order-btn",type:"primary",onClick:le,disabled:0===a.value.length||0===a.value.filter((function(e){return e.checkbox})).length},{default:(0,m.w5)((function(){return p[20]||(p[20]=[(0,m.Uk)(" \u521b\u5efa\u8ba2\u5355 ")])})),_:1},8,["disabled"])])])],512),[[h.F8,Ve.value]]),(0,m.wy)((0,m._)("div",te,[(0,m._)("div",ae,[(0,m._)("div",{class:"select-all-section",onClick:xe},[(0,m.Wm)(w,{modelValue:n.value,"onUpdate:modelValue":p[3]||(p[3]=function(e){return n.value=e}),"icon-size":"16"},null,8,["modelValue"]),p[21]||(p[21]=(0,m._)("span",{class:"select-all-text"},"\u5168\u9009",-1))]),(0,m._)("div",oe,[(0,m.Wm)(k,{class:"save-btn",onClick:Be},{default:(0,m.w5)((function(){return p[22]||(p[22]=[(0,m.Uk)(" \u4fdd\u5b58\u4fee\u6539 ")])})),_:1}),(0,m.Wm)(k,{class:"remove-btn",type:"primary",onClick:Ue},{default:(0,m.w5)((function(){return p[23]||(p[23]=[(0,m.Uk)(" \u79fb\u9664\u8d2d\u7269\u8f66 ")])})),_:1})])])],512),[[h.F8,!Ve.value]]),(0,m.Wm)(f,{content:"\u786e\u5b9a\u5c06\u5546\u54c1\u4ece\u8d2d\u7269\u8f66\u79fb\u9664\u5417\uff1f",visible:t.value,"onUpdate:visible":p[4]||(p[4]=function(e){return t.value=e}),onCancel:ve,onOk:pe},null,8,["visible"])]})),_:1})],64)}}});const ie=ce;var re=ie,ue={navigationBarTitleText:"\u8d2d\u7269\u8f66"};Page((0,a.createPageConfig)(re,"pages/shoppingCart/index",{root:{cn:[]}},ue||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[107,216,592],(function(){return n(2270)}));e.O()}]);