<script setup>
import { onBeforeMount, ref } from "vue";
import { updateOrder, getProduct, getProductByNumber } from '@/service/index'
import Taro, { showToast, getCurrentPages } from '@tarojs/taro';
import { Search2, IconFont } from '@nutui/icons-vue-taro'
const val = ref('')
/** 设置页面属性 */
definePageConfig({
  navigationBarTitleText: '新增查询',
});
const CurrentPage = ref(1)
const DefaultTotalPage = ref(1)
const onSearch = async (again = false) => {
  if (again) {
    SearchList.value = []
  }
  Taro.showLoading({
    title: '加载中...',
    mask: true
  })
  const { error, success } = await getProduct({ name: val.value, page: CurrentPage.value })
  if (error === null) {
    DefaultTotalPage.value = success.psize
    CurrentPage.value = success.cur_page
    SearchList.value.push(...success.items)
    SearchList.value = SearchList.value.map(item => {
      item.product_qty = 1
      item.zx_line_notes = ''
      item.design = ''
      item.fanhao_id = ''
      item.icon = 'new'

      return item
    })
    // val.value = ''
  }
  Taro.hideLoading()
  console.log('SearchList', SearchList.value);

}
//查询结果列表
const SearchList = ref([])
//滚动加载
const scrollTop = ref(0)
const cCheckboxChange = (state, label) => {
  const l = SearchList.value.map(i => i.checkbox).filter(x => x).length
  allSelected.value = l === SearchList.value.length;
  countTotalPrice()


}
//子组件返回值(动作面板)
let ClickshowDialogItem = null
const showDialog = (msg) => {
  console.log('子组件返回值:id', msg.id);
  console.log('子组件返回值:index', msg.index);
  ClickshowDialogItem = msg.id
  click()

}
const show = ref(false)
const click = () => {
  show.value = true
}
const countTotalPrice = () => {
  totalPrice.value = SearchList.value
    .filter(x => x.checkbox)
    .reduce((acc, curr) => acc + curr.price_unit * curr.product_qty, 0);
}
const minus = (id) => {
  console.log('minus', id);

  SearchList.value.forEach(
    item => {
      if (id === item.id) {
        if (item.product_qty !== 1) {
          item.product_qty -= 1
        } else {
          Taro.showToast({
            title: '最少购买一个商品~',
            icon: 'none',
            duration: 2000
          })
        }
      }
    }
  )
  countTotalPrice()
}
const plus = (id) => {
  console.log('plus', id);
  SearchList.value.forEach(
    item => {
      if (id === item.id) {
        if (item.product_qty !== 999) {
          item.product_qty = Number(item.product_qty) + 1
        } else {
          Taro.showToast({
            title: '当前最多仅能购买' + 999 + '份该商品~',
            icon: 'none',
            duration: 2000
          })
        }
      }
    }
  )
  countTotalPrice()
}

//搜索框相关事件
const searchValue = ref('')
const showSelect = ref(false)
const searchFHList = ref([])
const searchFHListIndex = ref(1)
const DQsearchListIndex = ref(1)

const getProductByNumberF = async (item) => {
  const { error, success } = await getProductByNumber({ name: item, per_page: '20' })
  console.log('success                  ~~~', success);
  const { items, psize } = success
  if (success && items) {
    searchFHList.value = items
    DQsearchListIndex.value = psize
  }
}
const onScrollBottom = async () => {
  console.log('触底了');
  console.log('searchListIndex', searchFHListIndex.value);
  console.log('DQsearchListIndex', DQsearchListIndex.value);


  if (Number(searchFHListIndex.value) < Number(DQsearchListIndex.value)) {
    searchFHListIndex.value++
    const { error, success } = await getProductByNumber({ name: searchFHList.value, page: searchFHListIndex.value.toString(), per_page: '20' })
    if (error === null) {

      if (success && items) {
        searchFHList.value = searchFHList.value.concat(items)
        DQsearchListIndex.value = psize
      }
    }
  } else {
    Taro.showToast({
      title: '加载完毕了~',
      icon: 'error',
      duration: 2000
    })
  }

}
const GetInputFocus = () => {
  if (searchFHList.value.length >= 0) {
    show.value = true
    if (searchValue.value.length > 0) {
      getProductByNumberF(searchValue.value)
    } else {
      searchFHList.value = []
      DQsearchListIndex.value = 1
    }

  } else {
    show.value = false
  }

}
const clickItem = (item) => {

  // console.log("item----",item._relatedInfo.anchorRelatedText);
  const { name, id } = item
  if (ClickshowDialogItem) {
    SearchList.value.find(item => {
      if (item.id === ClickshowDialogItem) {
        item.design = name
        item.fanhao_id = id
      }
    })
    ClickshowDialogItem = null
    searchValue.value = ''
    console.log('cartCommodities', SearchList.value);
    show.value = false
    searchFHList.value = []


  } else {
    Taro.showToast({
      title: '操作失败',
      icon: 'error',
      duration: 2000
    })
  }

}
const allSelected = ref(false)
const totalPrice = ref(0)

//对话框
const onCancel = () => {
  console.log('event cancel');
};


const onOk = async () => {
  console.log('event ok');
  let checkList = SearchList.value.filter(i => i.checkbox === true)
  console.log('checkList', checkList);

  let designConfirm = checkList.find(el =>
    el.name.includes('特殊图案') && el.design.length <= 0
  )


  if (designConfirm) {
    Taro.showModal({
      title: '提示',
      content: '特殊图案产品需选择楼层图案后才可下单',
      success: function (res) {
      }
    })

  } else {
    checkList=checkList.map(item =>{
    return {
      product_id:item.id,
      product_qty:item.product_qty,
      price_unit:item.price_unit,
      zx_line_notes:item.zx_line_notes,
      fanhao_id:item.fanhao_id,
      
    }}
   )
    const { error, success } = await updateOrder({

      add_lines: JSON.stringify(checkList),

    }, {
      id: Taro.getCurrentInstance().preloadData.OrderId

    })
    if (error === null && success) {
      console.log('success---', success);
      Taro.showToast({
        title: '提交成功',
        success: () => {
          setTimeout(() => {
            let pages = Taro.getCurrentPages()
            let prevPage = pages[pages.length - 2]
            prevPage.setData({
              checkList
            })
            Taro.navigateBack({
              delta: 1
            })

          }, 2000);

        },
        duration: 2000
      })

    } else {
      Taro.showToast({
        title: error.message,
        icon: 'error',
        duration: 2000
      })
    }
  }


  // Taro.showToast({
  //   title: '暂存成功',
  //   icon: 'success'
  // })

  // setTimeout(() => {
  //   let pages = Taro.getCurrentPages()
  //   let prevPage = pages[pages.length - 2]
  //   prevPage.setData({
  //     checkList
  //   })
  //   Taro.navigateBack({
  //     delta: 1
  //   })

  // }, 2000);

}


const visible1 = ref(false);
const visible3 = ref(false);
Taro.useReachBottom(async () => {
  // console.log('触底了');
  onScrollBottomPage()

})
const onScrollBottomPage = () => {
  console.log(CurrentPage.value, DefaultTotalPage.value);

  if (CurrentPage.value < DefaultTotalPage.value) {
    CurrentPage.value = Number(CurrentPage.value) + 1
    onSearch()
  } else {
    Taro.showToast({
      title: '全部加载完毕~',
      icon: 'success',
      duration: 2000
    })
  }

}
Taro.usePageScroll((res) => {


  if (res.scrollTop >= 528) {
    // console.log('true');
    PageScrollTop.value = true
  } else {
    // console.log('false');
    PageScrollTop.value = false

  }
});
const PageScrollTop = ref(false)
const checkItem = (id) => {

  // console.log('item', id);
  let preloadValue = Taro.getCurrentInstance().preloadData.existingProductList
  let Exist = preloadValue.find(item =>
    item.id === id
  ) || null
  console.log(Exist, 'exist');

  if (Exist != null) {
    visible3.value = true

  }

}

</script>

<template>
  <div class="search-page">
    <!-- 返回顶部按钮 -->
    <nut-fixed-nav type="left" :position="{ top: '140px' }" v-show="PageScrollTop" @click="Taro.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })">
      <template #btn>
        <span class="text">{{ "返回顶部" }}</span>
      </template>
    </nut-fixed-nav>

    <!-- 优化后的搜索栏 -->
    <div class="search-header">
      <nut-sticky top="0">
        <div class="search-container">
          <div class="search-wrapper">
            <nut-searchbar
              v-model="val"
              confirm-type="search"
              placeholder="请输入产品型号/名称/规格/料号"
              class="custom-searchbar"
            >
              <template #rightout>
                <div class="search-btn" @click="onSearch(true)">
                  <i class="i-bx-search"></i>
                  <span>查询</span>
                </div>
              </template>
              <template #rightin>
                <Search2 />
              </template>
            </nut-searchbar>
          </div>
        </div>
      </nut-sticky>
    </div>
    <!-- 优化后的楼层搜索弹窗 -->
    <nut-action-sheet v-model:visible="show" class="floor-search-sheet">
      <div class="search-container">
        <div class="search-header">
          <h3 class="search-title">选择楼层图案</h3>
          <p class="search-subtitle">请输入楼层编号进行搜索</p>
        </div>
        <nut-searchbar
          v-model="searchValue"
          shape="round"
          placeholder="请输入楼层以搜索"
          input-background="#f8f9fa"
          @change="GetInputFocus"
          id="pop-target"
          class="custom-searchbar"
        >
          <template #leftin>
            <Search2 />
          </template>
        </nut-searchbar>

        <div class="search-results-container">
          <div class="search-results">
            <div v-if="searchFHList.length > 0" class="results-list">
              <nut-list :list-data="searchFHList" :container-height="280" @scroll-bottom="onScrollBottom">
                <template #default="{ item }">
                  <div class="floor-item" @click="clickItem(item)">
                    <div class="floor-info">
                      <i class="i-bx-building-house floor-icon"></i>
                      <span class="floor-name">{{ item.name }}</span>
                    </div>
                    <i class="i-bx-chevron-right select-icon"></i>
                  </div>
                </template>
              </nut-list>
            </div>

            <div v-else class="empty-search">
              <div class="empty-icon">
                <i class="i-bx-search-alt"></i>
              </div>
              <p class="empty-text">请输入相应正确楼层编号后进行查询</p>
            </div>
          </div>
        </div>
      </div>
    </nut-action-sheet>
    <!-- 优化后的商品列表区域 -->
    <div class="products-section">
      <div class="section-header" v-if="SearchList.length > 0">
        <i class="i-bx-package section-icon"></i>
        <span class="section-title">搜索结果</span>
        <span class="product-count">({{ SearchList.length }}件)</span>
      </div>

      <div class="products-content">
        <template v-if="SearchList.length > 0">
          <nut-swipe-group lock class="product-swipe-group">
            <nut-swipe
              v-for="(item, index) in SearchList"
              ref="swipeRefs"
              class="product-swipe"
              :name="item.id.toString()"
              :key="item.id"
            >
              <div class="product-card">
                <!-- 商品主要信息区域 -->
                <div class="product-main-info">
                  <div class="product-checkbox-section">
                    <nut-checkbox
                      @change="cCheckboxChange"
                      v-model="item.checkbox"
                      icon-size="18"
                      @click.once="checkItem(item.id)"
                      class="custom-checkbox"
                    />
                  </div>

                  <div class="product-image-section">
                    <div class="image-container">
                      <image
                        :src="item.image || 'https://tse2-mm.cn.bing.net/th/id/OIP-C.exoIucrWcex80_QKk3z5DAAAAA?w=181&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7'"
                        class="product-image"
                      />
                      <div class="image-overlay" v-if="!item.image">
                        <i class="i-bx-image-alt"></i>
                      </div>
                    </div>
                  </div>

                  <div class="product-info-section">
                    <div class="product-title-area">
                      <!-- 产品型号标签 -->
                      <div class="model-tag">
                        <i class="i-bx-crown"></i>
                        <span>{{ item.model }}</span>
                      </div>

                      <!-- 产品名称标签 -->
                      <div class="name-tag">
                        {{ item.name.split('/')[0] }}
                      </div>

                      <!-- 产品子名称 -->
                      <div class="sub-name-tag" v-show="item.name.split('/')[1]">
                        {{ item.name.split('/')[1] }}
                      </div>
                    </div>

                    <!-- 产品规格 -->
                    <div class="product-spec">
                      <span class="spec-text">{{ item.spec }}</span>
                    </div>

                    <!-- 价格和编号区域 -->
                    <div class="price-code-section">
                      <div class="price-area">
                        <span class="currency">¥</span>
                        <span class="price">{{ item.price_unit }}</span>
                        <span class="unit">/PCS</span>
                      </div>
                      <div class="code-area">
                        <span class="code-tag">{{ item.code }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 设计选择组件 -->
                <DesignCell
                  :design="item.design"
                  :id="item.id"
                  :index="index"
                  :name="item.name.split('/')[0]"
                  :class="{ noShow: !item.name.split('/')[0].includes('特殊图案') }"
                  @update:design="showDialog"
                  class="design-cell"
                />

                <!-- 商品备注区域 -->
                <div class="product-note-section">
                  <div class="note-label">
                    <i class="i-bx-message-square-detail"></i>
                    <span>商品备注</span>
                  </div>
                  <nut-input
                    v-model="item.zx_line_notes"
                    :max-length="50"
                    placeholder="请输入商品备注（下单后无法追加）"
                    class="note-input"
                  >
                    <template #right>
                      <i class="i-bx-edit-alt note-edit-icon"></i>
                    </template>
                  </nut-input>
                </div>

                <!-- 数量控制区域 -->
                <div class="quantity-section">
                  <div class="quantity-label">数量</div>
                  <div class="quantity-control">
                    <nut-input-number
                      v-model="item.product_qty"
                      :min="1"
                      :max="200000"
                      @blur="countTotalPrice()"
                      @add="() => plus(item.id)"
                      @reduce="() => minus(item.id)"
                      class="custom-input-number"
                    />
                  </div>
                </div>
              </div>
            </nut-swipe>

          </nut-swipe-group>
        </template>

        <!-- 空状态 -->
        <template v-else>
          <div class="empty-products">
            <div class="empty-icon">
              <i class="i-bx-package"></i>
            </div>
            <p class="empty-text">暂无查询结果</p>
          </div>
        </template>
      </div>
    </div>

    <!-- 底部间距 -->
    <div class="bottom-spacer"></div>

    <!-- 优化后的底部操作栏 -->
    <div class="bottom-action-bar">
      <div class="action-bar-content">
        <!-- 价格统计区域 -->
        <div class="price-summary">
          <div class="total-price-section">
            <span class="total-label">合计:</span>
            <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
          </div>
        </div>

        <!-- 新增订单按钮 -->
        <div class="submit-section">
          <nut-button
            type="primary"
            @click="visible1 = true"
            :disabled="(SearchList.length === 0) || (SearchList.filter(x => x.checkbox).length === 0)"
            class="submit-btn"
          >
            <i class="i-bx-plus"></i>
            <span>新增订单</span>
          </nut-button>
        </div>
      </div>
    </div>

    <!-- 对话框 -->
    <nut-dialog
      title="新增订单"
      content="请确定是否新增相关产品到订单"
      v-model:visible="visible1"
      @cancel="onCancel"
      @ok="onOk"
      class="custom-dialog"
    />

    <nut-dialog
      no-cancel-btn
      title="温馨提示"
      v-model:visible="visible3"
      ok-text="我知道了"
      content="您所勾选的产品在当前订单中已已存在，请确认核实订单？"
      class="custom-dialog"
    />
  </div>
</template>

<style lang="scss">
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$info-color: #2196F3;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 页面基础样式
page {
    background: $background-light !important;
}

.search-page {
    min-height: 100vh;
    background: $background-light;
    padding-bottom: 80px;
}

// 返回顶部按钮 - 保持原版样式

// 搜索栏样式
.search-header {
    .search-container {
        background: #fff;
        box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);

        .search-wrapper {
            padding: 12px;

            .custom-searchbar {
                --nutui-searchbar-background: #{$primary-light};
                --nutui-searchbar-input-background: #fff;
                --nutui-searchbar-input-text-color: #{$text-primary};
                --nutui-searchbar-border-radius: 20px;

                .search-btn {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    padding: 8px 16px;
                    background: $primary-color;
                    color: #fff;
                    border-radius: 16px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:active {
                        background: $primary-dark;
                        transform: scale(0.95);
                    }

                    i {
                        font-size: 16px;
                    }
                }
            }
        }
    }
}

// 楼层搜索弹窗样式
.floor-search-sheet {
    --nutui-actionsheet-border-radius: 16px 16px 0 0;

    .search-container {
        padding: 20px;
        background: #fff;

        .search-header {
            text-align: center;
            margin-bottom: 20px;

            .search-title {
                font-size: 18px;
                font-weight: 600;
                color: $text-primary;
                margin: 0 0 4px 0;
            }

            .search-subtitle {
                font-size: 14px;
                color: $text-secondary;
                margin: 0;
            }
        }

        .custom-searchbar {
            --nutui-searchbar-background: #{$primary-light};
            --nutui-searchbar-input-background: #fff;
            --nutui-searchbar-input-text-color: #{$text-primary};
            margin-bottom: 16px;
        }

        .search-results-container {
            .search-results {
                .results-list {
                    max-height: 280px;
                    overflow-y: auto;

                    .floor-item {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 12px 16px;
                        background: #fff;
                        border-radius: 8px;
                        margin-bottom: 8px;
                        border: 1px solid $border-color;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover, &:active {
                            background: $primary-light;
                            border-color: $primary-color;
                        }

                        .floor-info {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .floor-icon {
                                font-size: 16px;
                                color: $primary-color;
                            }

                            .floor-name {
                                font-size: 14px;
                                color: $text-primary;
                                font-weight: 500;
                            }
                        }

                        .select-icon {
                            font-size: 16px;
                            color: $text-light;
                        }
                    }
                }

                .empty-search {
                    text-align: center;
                    padding: 40px 20px;

                    .empty-icon {
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: $primary-light;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;

                        i {
                            font-size: 24px;
                            color: $primary-color;
                        }
                    }

                    .empty-text {
                        font-size: 14px;
                        color: $text-secondary;
                        margin: 0;
                    }
                }
            }
        }
    }
}

// 商品列表区域样式
.products-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .product-count {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: auto;
        }
    }

    .products-content {
        .product-swipe-group {
            .product-swipe {
                margin-bottom: 0;
                border-bottom: 1px solid $border-color;

                &:last-child {
                    border-bottom: none;
                }

                .product-card {
                    display: flex;
                    flex-direction: column;
                    padding: 10px;
                    gap: 6px;

                    // 商品主要信息区域（复选框+图片+基本信息）
                    .product-main-info {
                        display: flex;
                        gap: 8px;

                        .product-checkbox-section {
                            flex-shrink: 0;
                            display: flex;
                            align-items: flex-start;
                            padding-top: 4px;

                            .custom-checkbox {
                                --nutui-checkbox-icon-font-size: 18px;
                                --nutui-checkbox-label-color: #{$text-primary};
                                --nutui-checkbox-icon-active-color: #{$primary-color};
                            }
                        }

                        .product-image-section {
                            flex-shrink: 0;

                            .image-container {
                                position: relative;
                                width: 60px;
                                height: 60px;
                                border-radius: 6px;
                                overflow: hidden;
                                background: $background-light;

                                .product-image {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                }

                                .image-overlay {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    background: $background-light;

                                    i {
                                        font-size: 18px;
                                        color: $text-light;
                                    }
                                }
                            }
                        }

                        .product-info-section {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 4px;
                            min-width: 0;

                            .product-title-area {
                                display: flex;
                                flex-direction: column;
                                gap: 2px;

                                .model-tag {
                                    display: flex;
                                    align-items: center;
                                    gap: 2px;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: $primary-light;
                                    border: 1px solid $primary-color;
                                    width: fit-content;

                                    i {
                                        font-size: 12px;
                                        color: $primary-color;
                                    }

                                    span {
                                        font-size: 12px;
                                        color: $primary-color;
                                        font-weight: 600;
                                        line-height: 1.2;
                                    }
                                }

                                .name-tag {
                                    display: block;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(76, 175, 80, 0.1);
                                    border: 1px solid $success-color;
                                    font-size: 12px;
                                    color: $success-color;
                                    font-weight: 600;
                                    line-height: 1.2;
                                    width: fit-content;
                                }

                                .sub-name-tag {
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(255, 107, 53, 0.1);
                                    font-size: 11px;
                                    color: $accent-color;
                                    font-weight: 500;
                                    line-height: 1.2;
                                    width: fit-content;
                                }
                            }

                            .product-spec {
                                .spec-text {
                                    font-size: 12px;
                                    color: $text-secondary;
                                    background: $background-light;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    display: inline-block;
                                    line-height: 1.3;
                                }
                            }

                            .price-code-section {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .price-area {
                                    display: flex;
                                    align-items: baseline;
                                    gap: 2px;

                                    .currency {
                                        font-size: 14px;
                                        color: $accent-color;
                                        font-weight: 600;
                                    }

                                    .price {
                                        font-size: 18px;
                                        color: $accent-color;
                                        font-weight: 700;
                                    }

                                    .unit {
                                        font-size: 12px;
                                        color: $text-secondary;
                                        font-weight: 500;
                                    }
                                }

                                .code-area {
                                    .code-tag {
                                        background: $primary-color;
                                        color: #fff;
                                        padding: 2px 6px;
                                        border-radius: 3px;
                                        font-size: 11px;
                                        font-weight: 600;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .empty-products {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: $primary-light;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;

                i {
                    font-size: 32px;
                    color: $primary-color;
                }
            }

            .empty-text {
                font-size: 16px;
                color: $text-secondary;
                margin: 0;
            }
        }
    }
}

// 设计选择组件
.design-cell {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid $border-color;

    &.noShow {
        visibility: hidden !important;
    }
}

// 商品备注区域
.product-note-section {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid $border-color;

    .note-label {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;

        i {
            font-size: 14px;
            color: $primary-color;
        }

        span {
            font-size: 14px;
            color: $text-primary;
            font-weight: 500;
        }
    }

    .note-input {
        --nutui-input-border-color: #{$border-color};
        --nutui-input-border-radius: 6px;
        --nutui-input-text-color: #{$text-primary};
        --nutui-input-placeholder-color: #{$text-light};

        .note-edit-icon {
            color: $text-light;
            font-size: 14px;
        }
    }
}

// 数量控制区域
.quantity-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
    padding: 6px 10px;
    background: $background-light;
    border-radius: 6px;

    .quantity-label {
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
    }

    .quantity-control {
        .custom-input-number {
            --nutui-input-number-button-width: 24px;
            --nutui-input-number-button-height: 24px;
            --nutui-input-number-input-width: 40px;
            --nutui-input-number-input-height: 24px;
            --nutui-input-number-input-font-size: 12px;
            --nutui-input-number-button-background: #{$primary-color};
            --nutui-input-number-button-color: #fff;
            --nutui-input-number-input-border-color: #{$border-color};
        }
    }
}

// 底部操作栏样式
.bottom-spacer {
    height: 80px;
}

.bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid $border-color;
    box-shadow: 0 -2px 12px rgba(18, 47, 56, 0.08);
    z-index: 999;

    .action-bar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        gap: 16px;

        .price-summary {
            flex: 1;

            .total-price-section {
                display: flex;
                align-items: baseline;
                gap: 4px;

                .total-label {
                    font-size: 14px;
                    color: $text-secondary;
                    font-weight: 500;
                }

                .total-price {
                    font-size: 20px;
                    color: $accent-color;
                    font-weight: 700;
                }
            }
        }

        .submit-section {
            flex-shrink: 0;

            .submit-btn {
                --nutui-button-primary-background-color: #{$primary-color};
                --nutui-button-primary-border-color: #{$primary-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                --nutui-button-font-size: 16px;
                padding: 0 24px;
                height: 44px;
                display: inline-flex;
                align-items: center;
                gap: 6px;

                i {
                    font-size: 16px;
                }

                &:not(:disabled):active {
                    --nutui-button-primary-background-color: #{$primary-dark};
                }

                &:disabled {
                    opacity: 0.5;
                }
            }
        }
    }
}

// 对话框样式覆盖
.custom-dialog {
    --nutui-dialog-header-font-weight: 600;
    --nutui-dialog-header-color: #{$text-primary};
    --nutui-dialog-content-color: #{$text-secondary};
    --nutui-dialog-ok-color: #{$primary-color};
    --nutui-dialog-cancel-color: #{$text-light};
}

// 响应式设计
@media (max-width: 375px) {
    .search-page {
        padding-bottom: 70px;
    }

    .products-section {
        margin: 8px;
        border-radius: 8px;

        .section-header {
            padding: 12px;

            .section-title {
                font-size: 14px;
            }
        }
    }

    .product-card {
        padding: 8px !important;
        gap: 4px !important;

        .product-main-info {
            gap: 6px;

            .product-image-section .image-container {
                width: 50px;
                height: 50px;
            }

            .product-info-section {
                gap: 3px;

                .product-title-area {
                    gap: 1px;

                    .model-tag, .name-tag {
                        padding: 1px 4px;
                        font-size: 11px;
                    }

                    .sub-name-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }

                .product-spec .spec-text {
                    font-size: 11px;
                    padding: 1px 4px;
                }

                .price-code-section {
                    .price-area {
                        .currency {
                            font-size: 12px;
                        }

                        .price {
                            font-size: 16px;
                        }

                        .unit {
                            font-size: 10px;
                        }
                    }

                    .code-area .code-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }
            }
        }

        .product-note-section {
            margin-top: 3px;
            padding-top: 3px;

            .note-label {
                gap: 4px;
                margin-bottom: 4px;

                i {
                    font-size: 12px;
                }

                span {
                    font-size: 12px;
                }
            }
        }

        .quantity-section {
            margin-top: 3px;
            padding: 4px 8px;

            .quantity-label {
                font-size: 12px;
            }

            .quantity-control .custom-input-number {
                --nutui-input-number-button-width: 20px;
                --nutui-input-number-button-height: 20px;
                --nutui-input-number-input-width: 32px;
                --nutui-input-number-input-height: 20px;
                --nutui-input-number-input-font-size: 11px;
            }
        }

        .design-cell {
            margin-top: 3px;
            padding-top: 3px;
        }
    }

    .bottom-action-bar .action-bar-content {
        padding: 10px 12px;
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
        gap: 12px;

        .price-summary .total-price-section .total-price {
            font-size: 18px;
        }

        .submit-section .submit-btn {
            height: 40px;
            font-size: 14px;
            padding: 0 20px;
        }
    }
}


</style>
