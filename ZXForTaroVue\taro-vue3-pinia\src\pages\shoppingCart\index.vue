<script setup lang="ts">
import { ref, Ref, onMounted, onUnmounted } from 'vue';
import Taro, { eventCenter, getCurrentInstance } from "@tarojs/taro";
import { getFindCart, updateFindCart, getProduct } from '@/service/index'
import { useAppStore } from '@/store';
const allSelected = ref(false)
const isRmCommodityDialogShow = ref(false)
interface IcartCommodities {
    id: number,
    image: string,
    name: string,
    price_unit: number,
    count: number,
    maxBuy: number,
    checkbox: boolean,
    spec: string,
    code: string,
    model: string

}
const cartCommodities: Ref<Array<IcartCommodities>> = ref(
    [])


const getFindCartF = async () => {
    const { error, success } = await getFindCart()
    if (error === null && success) {
        console.log('getFindCartF', success);
        const cartData = success as any
        cartNum.value = cartData.items?.length || 0
        if (cartData.items) {
            await getProductF(cartData.items.map((i: any) => i.product_id), cartData.items)
        }
    } else {
        Taro.showToast({
            title: '获取购物车失败',
            icon: 'error',
            duration: 2000
        })
    }
}
const appStore = useAppStore();
onMounted(() => {
    eventCenter.on(getCurrentInstance().router!.onShow, () => {
        // if(isManage.value===false) isManage.value=true
        appStore.setActiveTab('/pages/shoppingCart/index');
        getFindCartF()
        cartCommodities.value.forEach(item => {
            item['checkbox'] = false
        })
        countTotalPrice()


    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)
})
// onBeforeMount(() => {
//     getFindCartF()
//     cartCommodities.value.forEach(item => {
//         item['checkbox'] = false
//     })
// })
const totalPrice = ref(0)
const swipeRefs = ref<any[]>([])
const goMakeOrder = () => {
    visibleSubmit.value = true
}

let tempId = -1

const countTotalPrice = () => {
    totalPrice.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) => {


            return acc + curr.price_unit * curr.count
        }, 0);
}
const removeCommodity = async () => {
    cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.id === tempId), 1)
    const { error } = await updateFindCart({
        product_id: -tempId
    })
    if (error === null) {
        Taro.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
        })
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}
const reCCard = () => {
    swipeRefs.value[cartCommodities.value.findIndex(item => item.id === tempId)]?.close()
}

const cCheckboxChange = () => {
    const l = cartCommodities.value.map(i => i.checkbox).filter(x => x).length
    allSelected.value = l === cartCommodities.value.length;
    countTotalPrice()
}
//商品变动的数组
const updateCartCommodities = ref<any[][]>([])
function updateOrInsert(array: any[][], target: any[]) {
    console.log('array', array, target);

    // 查找目标子数组的索引
    const index = array.findIndex((item: any[]) => item[0] === target[0]);
    console.log('index', index);

    if (index !== -1) {
        // 更新已存在的子数组
        array[index] = [array[index][0], array[index][1] + target[1]];
    } else {
        // 新增新子数组
        array.push(target);
    }
}
const minus = (id: number) => {

    cartCommodities.value.forEach(
        item => {
            if (id === item.id) {
                if (item.count !== 1) {
                    updateOrInsert(updateCartCommodities.value, [item.id, -1]);
                    item.count -= 1
                } else {
                    Taro.showToast({
                        title: '最少购买一个商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const plus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            if (id === item.id) {
                if (item.count !== item.maxBuy) {
                    updateOrInsert(updateCartCommodities.value, [item.id, 1]);
                    item.count = Number(item.count) + 1
                } else {
                    Taro.showToast({
                        title: '当前最多仅能购买' + item.maxBuy + '份该商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
//全选事件
const checkAll = () => {
    if (allSelected.value) {
        cartCommodities.value.forEach(item => {
            item.checkbox = true
        })
    } else {
        cartCommodities.value.forEach(item => {
            item.checkbox = false
        })
    }
}
//提交订单提示框
const visibleSubmit = ref(false)
const onSubmit = () => {
    const checkCartCommodities = cartCommodities.value
        .filter(x => x.checkbox)
    console.log('订单信息', checkCartCommodities);
    Taro.navigateTo({
        url: '/package/package-a/confirmOrder/index',

    })
    Taro.preload({ message: checkCartCommodities, fromSCartPage: true })
}
//滚动加载
const scrollTop = ref(0)
const upper = (e: any) => {
    console.log('upper:', e)
}

const lower = (e: any) => {
    console.log('lower:', e)
}

const scroll = (e: any) => {
    console.log('scroll:', e)
}

//购物车数量
const cartNum = ref(0)
//管理、退出管理文本切换
const isManage = ref(true)
const manageText = ref(new Map([
    [true, '管理'],
    [false, '退出']
]))
const onManage = () => {
    console.log('manage');

    isManage.value = !isManage.value
}
//查询商品
const getProductF = async (ids: any, countS: any) => {
    const { success } = await getProduct({ ids })
    const productData = success as any
    const items = productData?.items
    console.log(success, items);

    if (success && items) {
        cartCommodities.value = items.map((i: any) => {
            return {
                ...i,
                checkbox: false,
                maxBuy: 10000,
                count: 1
            }
        })
        countS.forEach((i: { product_id: number; product_qty: number; }) => {
            // 在counts数组中寻找匹配的项
            const matchedItem = cartCommodities.value.find(item => item.id === i.product_id);

            // 如果找到了匹配的项，则更新其count属性
            if (matchedItem) {
                matchedItem.count = i.product_qty;
            }
        });
        console.log('cartCommodities.value!!!!', cartCommodities.value);
    }
}
//移除购物车
const onRmCartF = async () => {
    const checkedItems = cartCommodities.value.filter(x => x.checkbox)
    console.log(checkedItems, '~~~');

    const cartItemsArray = checkedItems.map(i => [-i.id, i.count])
    let resultStr = '[' + cartItemsArray.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';

    const { error } = await updateFindCart({
        product_ids: resultStr
    })
    if (error === null) {
        Taro.showToast({
            title: '操作成功',
            icon: 'success',
            duration: 2000
        })
        await getFindCartF()
        countTotalPrice()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}
//更新购物车数量
const onudCartF = async () => {
    if (updateCartCommodities.value.length === 0) {
        Taro.showToast({
            title: '没有需要保存的修改',
            icon: 'none',
            duration: 2000
        })
        return
    }

    let resultStr = '[' + updateCartCommodities.value.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
    const { error } = await updateFindCart({
        product_ids: resultStr
    })
    if (error === null) {
        Taro.showToast({
            title: '操作成功',
            icon: 'success',
            duration: 2000
        })
        //清空变动量
        updateCartCommodities.value = []
        await getFindCartF()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}
const deleteProduct = async (id: number | string, count: number) => {
    const { error } = await updateFindCart({
        product_id: -id,
        product_qty: count
    })
    if (error === null) {
        Taro.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
        })
        await getFindCartF()
        countTotalPrice()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}

// 去购物函数
const goShopping = () => {
    Taro.switchTab({
        url: '/pages/index/index'
    })
}
definePageConfig({
    navigationBarTitleText: '购物车'
});

</script>
<template>
    <!-- 优化后的顶部导航栏 -->
    <nut-sticky top="0" class="header-sticky">
        <div class="header-container">
            <div class="header-content">
                <div class="header-title">
                    <i class="i-bx-cart-alt header-icon"></i>
                    <span class="title-text">购物车</span>
                    <span class="cart-count">({{ cartNum }})</span>
                </div>
                <div class="header-action" @click="onManage">
                    <i class="i-bx-cog action-icon"></i>
                    <span class="action-text">{{ manageText.get(isManage) }}</span>
                </div>
            </div>
        </div>
    </nut-sticky>
    <basic-layout show-tab-bar>
        <!-- <custom-navbar title="购物车" /> -->

        <nut-dialog title="提交订单" content="确认提交订货信息并生成订单吗？" v-model:visible="visibleSubmit" @ok="onSubmit" />
        <div class="cart-container">
            <nut-swipe-group lock style="width: 100vw" v-if="cartCommodities.length > 0">
                <!-- <nut-backtop height="90vh" style="margin-bottom: 60rpx;" :distance="30" :bottom="250">
                    <template #content> -->
                <scroll-view :scroll-y="true" style="height: calc(100%-58rpx);margin-bottom: 60rpx;"
                    @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll" :scroll-top="scrollTop">
                    <nut-swipe v-for="item in cartCommodities" ref="swipeRefs"
                        :name="item.id.toString()" :key="item.id" class="product-swipe">
                        <div class="product-card">
                            <!-- 删除按钮 -->
                            <div class="delete-btn" v-show="!isManage" @click="deleteProduct(item.id, item.count)">
                                <i class="i-bx-trash"></i>
                            </div>

                            <!-- 商品内容 -->
                            <div class="product-content">
                                <!-- 复选框和图片 -->
                                <div class="product-left">
                                    <nut-checkbox @change="cCheckboxChange" v-model="item.checkbox"
                                        icon-size="18" class="product-checkbox">
                                    </nut-checkbox>
                                    <div class="product-image-container">
                                        <image :src="item.image" class="product-image" />
                                        <div class="image-overlay" v-if="!item.image">
                                            <i class="i-bx-image-alt"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- 商品信息 -->
                                <div class="product-info">
                                    <!-- 商品标题区域 -->
                                    <div class="product-title-area">
                                        <!-- 产品型号标签 -->
                                        <div class="model-tag">
                                            <i class="i-bx-crown"></i>
                                            <span>{{ item.model }}</span>
                                        </div>

                                        <!-- 产品名称标签 -->
                                        <div class="name-tag">
                                            {{ item.name.split('/')[0] }}
                                        </div>

                                        <!-- 产品子名称 -->
                                        <div class="sub-name-tag" v-show="item.name.split('/')[1]">
                                            {{ item.name.split('/')[1] }}
                                        </div>
                                    </div>

                                    <!-- 产品规格 -->
                                    <div class="product-spec">
                                        <span class="spec-text">{{ item.spec }}</span>
                                    </div>

                                    <!-- 价格和编号区域 -->
                                    <div class="price-code-section">
                                        <div class="price-area">
                                            <span class="currency">¥</span>
                                            <span class="price">{{ item.price_unit }}</span>
                                            <span class="unit">/PCS</span>
                                        </div>
                                        <div class="code-area">
                                            <span class="code-tag">{{ item.code }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 数量控制器 -->
                                <div class="quantity-control">
                                    <nut-input-number v-model="item.count" :min="1" :max="200000"
                                        input-width="55" @blur="countTotalPrice()"
                                        @add="() => plus(item.id)" @reduce="() => minus(item.id)"
                                        class="custom-input-number" />
                                </div>
                            </div>
                        </div>


                        <!-- <template #right>
                                    <nut-button shape="square" style="height: 100%" type="danger"
                                        @click="() => { showRmDialog(item.id) }">删除</nut-button>
                                </template> -->
                    </nut-swipe>
                    <!-- 底部信息栏 -->
                    <div class="footer-info">
                        <div class="company-info">
                            <i class="i-bx-copyright"></i>
                            <span>技术支持 © 广东左向科技有限公司</span>
                        </div>
                    </div>
                </scroll-view>
                <!-- </template>
                </nut-backtop> -->
            </nut-swipe-group>
            <!-- 优化后的空状态 -->
            <div class="empty-cart-state" v-else>
                <div class="empty-content">
                    <div class="empty-icon">
                        <i class="i-bx-cart-alt"></i>
                    </div>
                    <div class="empty-title">购物车空空如也</div>
                    <div class="empty-description">快去挑选心仪的商品吧</div>
                    <nut-button class="go-shopping-btn" type="primary" @click="goShopping">
                        <i class="i-bx-shopping-bag"></i>
                        <span>去购物</span>
                    </nut-button>
                </div>
            </div>
        </div>
        <!-- 优化后的底部操作栏 - 普通模式 -->
        <div class="bottom-action-bar" v-show="isManage">
            <div class="action-bar-content">
                <!-- 全选区域 -->
                <div class="select-all-section" @click="checkAll">
                    <nut-checkbox v-model="allSelected" icon-size="16" />
                    <span class="select-all-text">全选</span>
                </div>

                <!-- 价格显示 -->
                <div class="price-section">
                    <span class="total-label">合计:</span>
                    <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
                </div>

                <!-- 创建订单按钮 -->
                <div class="button-section">
                    <nut-button
                        class="create-order-btn"
                        type="primary"
                        @click="goMakeOrder"
                        :disabled="(cartCommodities.length === 0) || (cartCommodities.filter(x => x.checkbox).length === 0)"
                    >
                        创建订单
                    </nut-button>
                </div>
            </div>
        </div>

        <!-- 优化后的底部操作栏 - 管理模式 -->
        <div class="bottom-action-bar" v-show="!isManage">
            <div class="action-bar-content">
                <!-- 全选区域 -->
                <div class="select-all-section" @click="checkAll">
                    <nut-checkbox v-model="allSelected" icon-size="16" />
                    <span class="select-all-text">全选</span>
                </div>

                <!-- 管理按钮组 -->
                <div class="manage-buttons">
                    <nut-button
                        class="save-btn"
                        @click="onudCartF"
                    >
                        保存修改
                    </nut-button>
                    <nut-button
                        class="remove-btn"
                        type="primary"
                        @click="onRmCartF"
                    >
                        移除购物车
                    </nut-button>
                </div>
            </div>
        </div>
        <nut-dialog content="确定将商品从购物车移除吗？" v-model:visible="isRmCommodityDialogShow" @cancel="reCCard"
            @ok="removeCommodity" />
    </basic-layout>
</template>


<style lang="scss">
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 顶部导航栏样式
.header-sticky {
    background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.15);
    z-index: 1000;
}

.header-container {
    background: transparent;

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        min-height: 44px;

        .header-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .header-icon {
                font-size: 20px;
                color: #fff;
            }

            .title-text {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
            }

            .cart-count {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 400;
            }
        }

        .header-action {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;

            &:active {
                background: rgba(255, 255, 255, 0.2);
                transform: scale(0.95);
            }

            .action-icon {
                font-size: 16px;
                color: #fff;
            }

            .action-text {
                font-size: 14px;
                color: #fff;
                font-weight: 500;
            }
        }
    }
}

// 商品卡片样式
.product-swipe {
    margin-bottom: 12px;

    .product-card {
        position: relative;
        background: #fff;
        border-radius: 12px;
        margin: 0 12px;
        box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
        overflow: hidden;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
        }

        .delete-btn {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(244, 67, 54, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;

            &:active {
                background: rgba(244, 67, 54, 0.2);
                transform: scale(0.9);
            }

            i {
                font-size: 16px;
                color: $error-color;
            }
        }

        .product-content {
            display: flex;
            padding: 16px;
            gap: 12px;

            .product-left {
                display: flex;
                align-items: flex-start;
                gap: 12px;

                .product-checkbox {
                    margin-top: 4px;

                    --nutui-checkbox-icon-color: $primary-color;
                    --nutui-checkbox-icon-active-color: #fff;
                    --nutui-checkbox-icon-active-background-color: $primary-color;
                }

                .product-image-container {
                    position: relative;
                    width: 80px;
                    height: 80px;
                    border-radius: 8px;
                    overflow: hidden;
                    background: $background-light;

                    .product-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .image-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: $background-light;

                        i {
                            font-size: 24px;
                            color: $text-light;
                        }
                    }
                }
            }

            .product-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
                min-width: 0;

                .product-title-area {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 6px;
                    align-items: center;

                    .model-tag {
                        display: inline-flex;
                        align-items: center;
                        gap: 4px;
                        padding: 4px 8px;
                        border-radius: 4px;
                        background: $primary-light;
                        border: 1px solid $primary-color;

                        i {
                            font-size: 12px;
                            color: $primary-color;
                        }

                        span {
                            font-size: 12px;
                            color: $primary-color;
                            font-weight: 600;
                        }
                    }

                    .name-tag {
                        padding: 4px 8px;
                        border-radius: 4px;
                        background: rgba(76, 175, 80, 0.1);
                        border: 1px solid $success-color;
                        font-size: 12px;
                        color: $success-color;
                        font-weight: 600;
                    }

                    .sub-name-tag {
                        padding: 4px 8px;
                        border-radius: 4px;
                        background: rgba(255, 107, 53, 0.1);
                        font-size: 11px;
                        color: $accent-color;
                        font-weight: 500;
                    }
                }

                .product-spec {
                    .spec-text {
                        font-size: 12px;
                        color: $text-secondary;
                        background: $background-light;
                        padding: 4px 8px;
                        border-radius: 4px;
                        display: inline-block;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .price-code-section {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: auto;

                    .price-area {
                        display: flex;
                        align-items: baseline;
                        gap: 2px;

                        .currency {
                            font-size: 14px;
                            color: $accent-color;
                            font-weight: 600;
                        }

                        .price {
                            font-size: 18px;
                            color: $accent-color;
                            font-weight: 700;
                        }

                        .unit {
                            font-size: 12px;
                            color: $text-secondary;
                            font-weight: 500;
                        }
                    }

                    .code-area {
                        .code-tag {
                            background: $primary-color;
                            color: #fff;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 11px;
                            font-weight: 600;
                        }
                    }
                }
            }

            .quantity-control {
                display: flex;
                align-items: flex-end;
                margin-left: auto;

                .custom-input-number {
                    --nutui-input-number-button-background-color: $primary-light;
                    --nutui-input-number-button-color: $primary-color;
                    --nutui-input-number-input-background-color: #fff;
                    --nutui-input-number-input-color: $text-primary;
                    --nutui-input-number-border-color: $border-color;
                }
            }
        }
    }
}

// 底部操作栏样式
.bottom-action-bar {
    position: fixed;
    bottom: var(--nut-tabbar-height, 100rpx);
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid $border-color;
    box-shadow: 0 -2px 12px rgba(18, 47, 56, 0.08);
    z-index: 999;

    .action-bar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        gap: 12px;

        .select-all-section {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: background-color 0.2s;

            &:active {
                background-color: $primary-light;
            }

            .select-all-text {
                font-size: 14px;
                color: $text-primary;
                font-weight: 500;
            }
        }

        .price-section {
            display: flex;
            align-items: baseline;
            gap: 4px;
            flex: 1;
            justify-content: center;

            .total-label {
                font-size: 14px;
                color: $text-secondary;
                font-weight: 500;
            }

            .total-price {
                font-size: 20px;
                color: $accent-color;
                font-weight: 700;
            }
        }

        .button-section {
            .create-order-btn {
                --nutui-button-primary-background-color: #{$primary-color};
                --nutui-button-primary-border-color: #{$primary-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                padding: 0 24px;
                height: 40px;

                &:not(:disabled):active {
                    --nutui-button-primary-background-color: #{$primary-dark};
                }
            }
        }

        .manage-buttons {
            display: flex;
            gap: 8px;

            .save-btn {
                --nutui-button-default-background-color: rgba(255, 152, 0, 0.1);
                --nutui-button-default-color: #{$warning-color};
                --nutui-button-default-border-color: #{$warning-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                padding: 0 16px;
                height: 36px;

                &:active {
                    --nutui-button-default-background-color: rgba(255, 152, 0, 0.2);
                }
            }

            .remove-btn {
                --nutui-button-primary-background-color: #{$error-color};
                --nutui-button-primary-border-color: #{$error-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                padding: 0 16px;
                height: 36px;

                &:not(:disabled):active {
                    --nutui-button-primary-background-color: #d32f2f;
                }
            }
        }
    }
}

// 卡片容器样式
.card {
    margin: 0;
    background: transparent;

    // 滚动视图样式
    scroll-view {
        background: $background-light;
    }
}

// 空状态样式
.empty-cart-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    background: #fff;
    margin: 12px;
    border-radius: 12px;

    .empty-content {
        text-align: center;
        padding: 40px 20px;

        .empty-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: $primary-light;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;

            i {
                font-size: 36px;
                color: $primary-color;
            }
        }

        .empty-title {
            font-size: 18px;
            color: $text-primary;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            color: $text-secondary;
            margin-bottom: 24px;
        }

        .go-shopping-btn {
            --nutui-button-primary-background-color: #{$primary-color};
            --nutui-button-primary-border-color: #{$primary-color};
            --nutui-button-border-radius: 20px;
            --nutui-button-font-weight: 600;
            padding: 0 24px;
            height: 44px;
            display: inline-flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 16px;
            }

            &:not(:disabled):active {
                --nutui-button-primary-background-color: #{$primary-dark};
                transform: scale(0.95);
            }
        }
    }
}

// 底部信息样式
.footer-info {
    background: #fff;
    border-radius: 8px;
    margin: 12px;
    padding: 20px;

    .company-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 12px;
        color: $text-light;

        i {
            font-size: 14px;
            color: $text-light;
        }
    }
}

// 对话框样式覆盖
.nut-dialog {
    --nutui-dialog-header-font-weight: 600;
    --nutui-dialog-header-color: #{$text-primary};
    --nutui-dialog-content-color: #{$text-secondary};
    --nutui-dialog-footer-ok-color: #{$primary-color};
}

// 全局按钮样式调整
.nut-button {
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

// 复选框样式调整
.nut-checkbox {
    --nutui-checkbox-icon-color: #{$primary-color};
    --nutui-checkbox-icon-active-color: #fff;
    --nutui-checkbox-icon-active-background-color: #{$primary-color};
    --nutui-checkbox-label-color: #{$text-primary};
}

// 输入数字组件样式调整
.nut-input-number {
    --nutui-input-number-button-background-color: #{$primary-light};
    --nutui-input-number-button-color: #{$primary-color};
    --nutui-input-number-input-background-color: #fff;
    --nutui-input-number-input-color: #{$text-primary};
    --nutui-input-number-border-color: #{$border-color};
    --nutui-input-number-border-radius: 6px;
}

// 价格组件样式调整
.nut-price {
    --nutui-price-symbol-color: #{$accent-color};
    --nutui-price-integer-color: #{$accent-color};
    --nutui-price-decimal-color: #{$accent-color};
}

// 标签组件样式调整
.nut-tag {
    --nutui-tag-border-radius: 4px;
    --nutui-tag-font-weight: 500;
}

// 滑动组件样式
.nut-swipe-group {
    background: $background-light;
    padding-top: 8px;
}

// 响应式设计
@media (max-width: 375px) {
    .product-card .product-content {
        padding: 12px;

        .product-left .product-image-container {
            width: 70px;
            height: 70px;
        }

        .product-info .price-code-section .price-area .price {
            font-size: 16px;
        }
    }

    .header-content {
        padding: 10px 12px;

        .header-title .title-text {
            font-size: 16px;
        }
    }

    .bottom-action-bar .action-bar-content {
        padding: 10px 12px;
        padding-bottom: calc(10px + env(safe-area-inset-bottom));

        .price-section .total-price {
            font-size: 18px;
        }
    }
}

// 动画效果
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.product-card {
    animation: fadeInUp 0.3s ease-out;
}

.bottom-action-bar {
    animation: slideInRight 0.3s ease-out;
}

// 加载状态样式
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;

    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid $primary-light;
        border-top: 2px solid $primary-color;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// 深色模式支持（可选）
@media (prefers-color-scheme: dark) {
    .header-sticky {
        background: linear-gradient(135deg, #0a1f26 0%, #061a1f 100%);
    }

    .product-card {
        background: #1a1a1a;
        border: 1px solid #333;
    }

    .bottom-action-bar {
        background: #1a1a1a;
        border-top-color: #333;
    }

    .footer-info {
        background: #1a1a1a;
    }

    .empty-cart-state {
        background: #1a1a1a;
    }
}
</style>