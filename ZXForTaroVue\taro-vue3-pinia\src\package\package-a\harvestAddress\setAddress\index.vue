<script setup lang="ts">
import { ref, reactive, Ref, onMounted } from 'vue';
import { Current, getCurrentPages, useRouter } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { Location, IconFont } from '@nutui/icons-vue-taro'
import { addAddress, updateAddress } from '@/service/index'
import { useAuthStore } from '@/store/index';
// import AddressParse from './dist/zh-address-parse.min.js'
import AddressParse from '@/package/package-a/harvestAddress/dist/zh-address-parse.min.js'

interface IaddressformData {
    testaddressName: string,
    phone: string,
    defaultAddress: false,
    fullAddress: string
    testid: string | number | null
}
const auth = useAuthStore()
//路由跳转预加载数据
const preloadData: Record<any, any> | undefined = Taro.getCurrentInstance().preloadData
const state = ref(new Map([
    ['add', '添加'],
    ['update', '编辑']
]))
// options为可选参数，不传默认使用正则查找
const options = {
    type: 0, // 哪种方式解析，0：正则，1：树查找
    textFilter: [], // 预清洗的字段
    nameMaxLength: 4, // 查找最大的中文名字长度
}
const parseValue = ref('')



// console.log(parseResult.value, 'parseResult');


onMounted(() => {
    if (router.params.state === 'update') {
        addressformData.value = {
            testaddressName: preloadData?.item.addressName,
            phone: preloadData?.item.phone,
            defaultAddress: preloadData?.item.defaultAddress,
            fullAddress: preloadData?.item.fullAddress,
            testid: preloadData?.item.id

        }

    }

})
//地区
const selector: Ref<Array<string>> = ref([])
//地区选择器事件
// 选中地区
const selectorChecked: Ref<String> = ref('')
const onChange = (e: { detail: { value: String; }; }) => {
    console.log(e);
    selectorChecked.value = e.detail.value

}
const router = useRouter()
const isdefaultAddress = ref(false)

//调用进行地图定位
const getMap = () => {
    Taro.getLocation({
        type: 'wgs84',
        success: function (res) {
            console.log(res);
            Taro.showToast({
                title: res.errMsg,
                icon: 'success',
                duration: 2000
            })
            const latitude = res.latitude
            const longitude = res.longitude
            const speed = res.speed
            const accuracy = res.accuracy
        },
        fail: function (res) {
            Taro.showToast({
                title: res.errMsg,
                icon: 'none',
                duration: 2000
            })
        }
    })

}
const addressformData: Ref<IaddressformData> = ref({
    testaddressName: '',
    phone: '',
    defaultAddress: false,
    fullAddress: '',
    testid: null
})
//
const setMessage = () => {
    Taro.setStorageSync('addressformData', addressformData.value)
}
//保存事件
const saveButton = async () => {
    console.log(addressformData.value);
    formRef.value?.validate().then(async ({ valid, errors }) => {
        if (valid) {

            if (router.params.state === 'add') {
                const res = await addAddress({ name: addressformData.value.testaddressName, phone: addressformData.value.phone, address: addressformData.value.fullAddress })
                if (res.error == null) {
                    Taro.showToast({
                        title: '添加成功',
                        icon: 'success',
                        duration: 2000,
                        success: () => {
                            setTimeout(() => {
                                Taro.navigateBack({
                                    delta: 1
                                })
                            }, 2000);
                        }
                    })
                }
                //清空表单
                addressformData.value = {
                    testaddressName: '',
                    phone: '',
                    defaultAddress: false,
                    fullAddress: '',
                    testid: null
                }



            }
            if (router.params.state === 'update') {
                const res = await updateAddress({ name: addressformData.value.testaddressName, phone: addressformData.value.phone, address: addressformData.value.fullAddress }, { id: addressformData.value.testid as string })

                if (res.error == null) {
                    // const pages = getCurrentPages()
                    // const current = pages[pages.length - 1]
                    // const eventChannel = current.getOpenerEventChannel()
                    // eventChannel.emit('updateEvent', addressformData.value);
                    Taro.showToast({
                        title: '修改成功',
                        icon: 'success',
                        duration: 2000,
                        success: () => {
                            setTimeout(() => {
                                Taro.navigateBack({
                                    delta: 1
                                })

                            }, 2000)
                        }
                    })
                }

            }
        } else {
            console.warn('error:', errors)
        }
    })

}
console.log(state.value.get(router.params.state as string), '路由传值');

const copyButtonClick = () => {
    console.log('aaa');
    // Taro.setClipboardData({
    //     data: 'data',
    //     success: function (res) {
    Taro.getClipboardData({
        success: function (res) {
            // console.log(res.data) // data
            parseValue.value = res.data
            // type参数0表示使用正则解析，1表示采用树查找, textFilter地址预清洗过滤字段。
            const parseResult = AddressParse(res.data, options)
            console.log(parseResult, 'parseResult');
            addressformData.value.testaddressName = parseResult.name
            addressformData.value.phone = parseResult.phone
            addressformData.value.fullAddress = parseResult.province + parseResult.city + parseResult.area + parseResult.detail






        }
    })
    //     }
    // })
}
const validatorName = () => {
    if (/^([\u4e00-\u9fa5]{2,6})$/gi.test(addressformData.value.testaddressName)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的姓名后重新提交')
    }
}
const validatorPhone = () => {
    if (/^1[3-9]\d{9}$/.test(addressformData.value.phone)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的11位手机号后重新提交')
    }
}
const validatorAddress = () => {
    if (/^[\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~` （）【】、，]{6,100}$/u.test(addressformData.value.fullAddress)) {
        return Promise.resolve()
    } else {
        return Promise.reject('联系地址应尽量详实')
    }
}
const formRef = ref(null)



definePageConfig({
    navigationBarTitleText: '编辑收货信息'
});
</script>
<template>
    <basic-layout>
        <!-- <custom-navbar :title="state.get(router.params.state as string) + '收货地址'" left-show /> -->
        <div class="setAddress-box">
            <!-- <view class="box-header flex justify-between">
                <span class="font-600 box-header-left">{{ '地址信息' }}</span>
                <nut-checkbox v-model="addressformData.defaultAddress">{{ '默认收货地址' }}</nut-checkbox>
            </view> -->
            <view class="box-content text-center">
                <nut-form star-position="right" ref="formRef">
                    <nut-form-item label="联系人:" required prop="testaddressName" :rules="[
                                {
                                    validator: validatorName,
                                    message: '请输入正确的人名'
                                }
                            ]">
                        <nut-input :border="false" v-model="addressformData.testaddressName" placeholder="联系人名字"
                            required prop="testaddressName"  type="text" />

                    </nut-form-item>
                    <nut-form-item label="手机号:" required prop="phone" :rules="[
                        {
                            validator: validatorPhone,
                            message: '请输入正确的手机号'

                        }
                    ]">
                        <nut-input :border="false" v-model="addressformData.phone" placeholder="手机号" type="number" />
                    </nut-form-item>
                    <nut-form-item label="详细地址:" required prop="fullAddress" :rules="[
                        {
                            validator: validatorAddress,
                            message: '地址应尽量详细'

                        }

                    ]">
                        <nut-textarea v-model="addressformData.fullAddress" placeholder="小区、写字楼、门牌号等" type="text"
                            class="textarea-style" limit-show :max-length="100">
                        </nut-textarea>
                    </nut-form-item>

                </nut-form>
                <!-- <button @click="getMap">{{ '测试定位服务' }}</button> -->
            </view>
        </div>
        <!-- <div class="setAddress-box">
            <nut-cell-group>
                <nut-cell title="设置默认地址">
                    <template #link>
                        <nut-switch sub-title="Subtitle" v-model="addressformData.defaultAddress" />
                    </template>
</nut-cell>
</nut-cell-group>
</div> -->
        <div class="setAddress-box">
            <nut-cell-group>
                <nut-cell class="flex flex-col">

                    <nut-textarea v-model="parseValue" placeholder="试试一键粘贴收件人姓名/手机号/收货地址，可快速识别您的收货地址" type="text"
                        class="textarea-style relative!" disabled />

                    <nut-button size="mini" type="success" class="copyButton"
                        style="right:13% ;bottom: 28%;z-index: 999;" @click="copyButtonClick()">{{ "粘贴"
                        }}</nut-button>
                    <span class="text-center mt-5%">{{ '地址粘贴板' }}<i class="i-bx-arrow-to-top"></i></span>
                </nut-cell>
            </nut-cell-group>
        </div>

        <nut-button @click="saveButton" class="text-center m-auto flex justify-center" style="width: 80%;  left: 50%;

transform: translateX(-50%);margin: 5% 0 5% 0;" size="large" color="linear-gradient(to right, #ff6034, #ee0a24)">{{
    '保存' }}</nut-button>
    </basic-layout>
</template>


<style lang="scss">
.nut-textarea--disabled .nut-textarea__limit,
.nut-textarea--disabled .nut-textarea__textarea {
    position: relative;
}

.copyButton {
    position: absolute;
}

.layout-screen {
    background-color: #F7F8FA !important;
}

.textarea-style {
    padding: 3%;
    background-color: #F7F7F7 !important;
    border-radius: 3%;
}

.nut-input-box {
    padding: 6px !important;
    background-color: #F7f7f7 !important;
    border-radius: 3% !important;
}

.setAddress-box {
    margin: 2%;

    .box-header {
        margin: 3%;
    }

    .nut-input {
        color: #000
    }

    .box-header-left {
        font-size: 1rem;
    }
}

.nut-form-item__label {
    font-size: 16px !important;
}

.nut-input .input-text {
    font-size: 16px !important;
}

.nut-input .input-text {
    font-size: 16px !important;
}
</style>