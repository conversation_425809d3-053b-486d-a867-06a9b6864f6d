<script setup lang="ts">
import Taro from '@tarojs/taro';
import { useRouter } from '@tarojs/taro';
import { onMounted, Ref, ref } from 'vue';
definePageConfig({
	navigationBarTitleText: '核验信息'
});
onMounted(() => {
	//请求数据
	//根据数据结果确定状态
	// if(){
	// 	state.value=res.data.state
	// }
})
const state = ref(1)
const stateMap: Ref<Map<Number, {
	title: string;
	desc: string;
}>> = ref(new Map([
	[1, {
		title: '信息填写中…',
		desc: '请填写核验信息,感谢您的支持'
	}
	],
	[
		2, {
			title: '资质审核中…',
			desc: '您所提交的经销商资质正在审核中，请耐心等待'
		}
	],
	[
		3, {
			title: '资质审核通过',
			desc: '恭喜您，您的经销商资质审核通过'
		}
	]
]))
const formData = ref({
	corporateName: '',
	contactName: '',
	contactPhone: '',
	Qualifications: ''
})

const submit = () => {
	Taro.showLoading({
		title: '提交中'
	})
	//定时器模拟提交成功的情况
	setTimeout(() => {
		Taro.hideLoading()
		Taro.showToast({
			title: '提交成功',
			icon: 'success',
			duration: 2000,
		});
		state.value = 2
	}, 2000);
}
</script>
<template>
	<basic-layout>
		<custom-navbar title="核验信息" left-show />
		<div class="verifi-box">
			<div class="verifi-titleBox relative">
				<view class="verifi-title ml-2 mr-2">{{ stateMap.get(state)!.title }}</view>
				<view class="verifi-desc ml-2 mr-2">{{ stateMap.get(state)!.desc }}</view>
			</div>
			<div class="verifi-form ml-2 mr-2">
				<nut-form>
					<nut-form-item label="经销商全称">
						<nut-input v-model="formData.corporateName" placeholder="请输入全称" type="text" :disabled="state != 1" />
					</nut-form-item>
					<nut-form-item label="联系人">
						<nut-input v-model="formData.contactName" placeholder="请输入联系人" type="text" :disabled="state != 1" />
					</nut-form-item>
					<nut-form-item label="联系电话">
						<nut-input v-model="formData.contactPhone" placeholder="请输入联系电话" type="text" :disabled="state != 1" />
					</nut-form-item>
					<nut-form-item label="联系地址">
						<nut-input v-model="formData.Qualifications" placeholder="请输入地址" type="text" :disabled="state != 1" />
					</nut-form-item>
					<nut-button type="info" size="large" class="verifi-button" block @click="submit" :disabled="state != 1">{{
						'提交' }}</nut-button>
				</nut-form>
			</div>
		</div>

	</basic-layout>
</template>

<style lang="scss">
.verifi-box {
	// css 背景色从上到下渐变 浅蓝到无颜色 过度缓慢点

	height: 100vh;
	background: linear-gradient(to bottom, #1987F0, #CADFF3, transparent);

	.verifi-titleBox {
		padding-top: 5%;

		.verifi-title {
			font-size: 1.5rem;
			font-weight: 400;
			color: #EFF6FE;
		}

		.verifi-desc {
			font-size: 0.75rem;
			color: #D1E6FC;

		}
	}

	.verifi-form {
		.verifi-button {
			width: 80%;
			margin-left: auto;
			margin-right: auto;
			margin-top: 3%;
			margin-bottom: 5%;
		}
	}
}
</style>
