"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[466],{2245:function(e,n,t){var r=t(1065),a=t(3221),u=t(7011),o={__name:"index",props:["style"],setup:function(e){var n=e;return function(e,t){return(0,a.wg)(),(0,a.iD)("view",{class:"sdf-card",style:(0,u.j5)(n.style)},[(0,a.WI)(e.$slots,"default")],4)}}};const i=o;var c=i,l=t(1845),s=(t(3505),t(1630)),v=(t(3926),t(3193)),p=(t(9157),t(2419)),f=t(8140),h=t(2810),d=t(6821),g=t(4733),w=t(1959),m=t.n(w),Z=t(5969),H=t(2740),k={key:0,lock:"",style:{width:"100vw"}},x={__name:"index",setup:function(e){var n=(0,d.iH)("");(0,a.bv)((function(){w.eventCenter.on((0,w.getCurrentInstance)().router.onShow,(function(){var e=m().getCurrentInstance().preloadData.value;console.log(e),e&&o(10,e)}))})),(0,a.Ah)((function(){w.eventCenter.off((0,w.getCurrentInstance)().router.onShow)}));var t=(0,d.iH)(1),r=(0,d.iH)(1),u=((0,d.iH)([]),(0,d.iH)([])),o=function(){var e=(0,h.Z)((0,p.Z)().mark((function e(){var n,a,o,i,c,l,s=arguments;return(0,p.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=s.length>0&&void 0!==s[0]?s[0]:20,a=s.length>1?s[1]:void 0,m().showLoading({title:"\u52a0\u8f7d\u4e2d"}),!(t.value<=r.value&&u.value.length%n==0)){e.next=14;break}return console.log(u.value.length,n),e.next=7,(0,g.co)({name:a});case 7:o=e.sent,i=o.error,c=o.success,console.log("----------",i),null==i?((l=u.value).push.apply(l,(0,f.Z)(c.items)),r.value=c.psize,t.value<c.psize?t.value=c.cur_page+1:t.value=c.cur_page,m().hideLoading(),console.log(u.value,r.value)):m().showToast({title:i,icon:"error",duration:2e3}),e.next=16;break;case 14:m().hideLoading(),m().showToast({title:"\u6ca1\u6709\u66f4\u591a\u8ba2\u5355\u4e86",icon:"none",duration:2e3});case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),i=function(){var e=(0,h.Z)((0,p.Z)().mark((function e(){return(0,p.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:u.value=[],r.value=1,t.value=1,o(20,n.value);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),x=((0,d.iH)(""),(0,d.iH)(0),(0,d.iH)(!1),(0,d.iH)(""),(0,d.iH)(!1),(0,d.iH)([]),(0,d.iH)(1),(0,d.iH)(1),function(){var e=(0,h.Z)((0,p.Z)().mark((function e(){return(0,p.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("\u89e6\u5e95\u4e86");case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}());m().useReachBottom((0,h.Z)((0,p.Z)().mark((function e(){return(0,p.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:x();case 1:case"end":return e.stop()}}),e)}))));(0,d.iH)(!1);return function(e,t){var r=v.Z,o=s.Z,p=l.Z,f=c;return(0,a.wg)(),(0,a.iD)(a.HY,null,[(0,a.Wm)(r,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return n.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u8ba2\u5355\u53f7",bottom:40},{rightout:(0,a.w5)((function(){return[(0,a._)("div",{onClick:i},"\u67e5\u8be2")]})),rightin:(0,a.w5)((function(){return[(0,a.Wm)((0,d.SU)(Z.LP))]})),_:1},8,["modelValue"]),(0,a.Wm)(f,{class:"card p-0_el_"},{default:(0,a.w5)((function(){return[u.value.length>0?((0,a.wg)(),(0,a.iD)("div",k,[(0,a.Wm)(o,{height:"100vh",distance:30},{content:(0,a.w5)((function(){return[(0,a.Wm)(H.Z,{values:u.value},null,8,["values"])]})),_:1})])):((0,a.wg)(),(0,a.j4)(p,{key:1,image:"empty",description:"\u6682\u65e0\u67e5\u8be2\u7ed3\u679c"}))]})),_:1})],64)}}};const y=x;var _=y,C={navigationBarTitleText:"\u67e5\u8be2\u5386\u53f2\u8ba2\u5355"};Page((0,r.createPageConfig)(_,"package/package-a/searchOrder/index",{root:{cn:[]}},C||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(2245)}));e.O()}]);