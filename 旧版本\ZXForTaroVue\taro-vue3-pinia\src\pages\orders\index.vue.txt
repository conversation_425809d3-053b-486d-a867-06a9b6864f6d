<script setup lang="ts">
import { navigateTo } from '@tarojs/taro';
import { useThemeStore } from '@/store';
import { ref, Ref } from 'vue';
import { IconFont, Voice, Cart, My, Clock, Location, Order } from '@nutui/icons-vue-taro'
import Taro from '@tarojs/taro';



const list: Ref<Array<string>> = ref([
	'https://img01.71360.com/file/read/www2/M00/81/72/rBwBEmSdQeGASyf5AAsZ29zWS-E349.jpg',
	'https://img01.71360.com/w3/w426o/20240425/dc5f2c5c1f0df04e1678b5eadffab984.jpg',
])

//历史记录列表
const historyList: Ref<Array<{
	title: string;
	content: string;
	desc: string;
	count: number;
	img: string;
}>> = ref([
	{
		title: '2021-09-01',
		content: '供货',
		desc: '产品型号:zx-1410',
		count: 10,
		img: 'https://storage.360buyimg.com/jdc-article/NutUItaro34.jpg'
	}, {
		title: '2021-09-01',
		content: '求购',
		desc: '产品型号:zx-8848',
		count: 20,
		img: 'https://storage.360buyimg.com/jdc-article/NutUItaro34.jpg'
	},
])
//公告栏列表
const noticeList = ref(['左向商城已上线，诚邀您进行体验！', '以前行力量，指引前进方向！'])
//historyList触底事件
const onScrollBottom = () => {
}

//求购事件
const buyClick = () => {
	// navigateTo({
	// 	url: '/package/package-a/fillOut/index?type=buy'
	// });
	buyOrSupply.value = 'buy'
	currentForm.value = formDataBuy.value
	visible1.value = true
}
// 供应事件
const supplyClick = () => {
	// navigateTo({
	// 	url: '/package/package-a/fillOut/index?type=supply'
	// });
	buyOrSupply.value = 'supply'
	currentForm.value = formDataSupply.value
	visible1.value = true
}
const goToFillout = () => {
	Taro.navigateTo({
		url: '/package/package-a/fillOut/index?type=all'
	});
}

//控制弹窗变量
const visible1 = ref(false);
//表单类型
const buyOrSupply = ref('');
const buyOrSupplyMap = new Map([
	['supply', '填写供货订单'],
	['buy', '填写求购订单']
])
//提交和取消事件
const onCancel = () => {
	console.log('event cancel');
};
const onOk = () => {
	console.log('event ok');
	console.log(currentForm.value);
	//发请求

	//请求成功则
	if (true) {
		//提示提交成功
		Taro.showToast({
			title: '提交成功',
			icon: 'none',
			duration: 2000,
			success: () => {
				//跳转页面
				Taro.navigateTo({
					url: "/package/package-a/fillOut/index?userid=zx&type=" + buyOrSupply.value
				});
				Taro.preload(currentForm.value)
			}
		})
	}


};
//供货表单
const formDataSupply = ref({
	'联系人': '',
	'联系电话': '',
	'产品型号': '',
	'供货数量': '',
	'供货时间': '',
	'供货地址': '',
	'备注': '',
})
//求购表单
const formDataBuy = ref({
	'联系人': '',
	'联系电话': '',
	'产品型号': '',
	'求购数量': '',
	'求购时间': '',
	'求购地址': '',
	'备注': '',
})
//历史记录
const dataList = ref([
	{
		'联系人': '微信用户',
		'联系电话': '15279710095',
		'产品型号': 'zx1410G',
		'求购数量': '38',
		'求购时间': '2024-6-30',
		'求购地址': '广东佛山',
		'备注': '',
		'类型': '求购'
	}, {
		'联系人': '微信用户',
		'联系电话': '13217074303',
		'产品型号': 'zx0512',
		'供货数量': '168',
		'供货时间': '2024-6-27',
		'供货地址': '深圳龙华',
		'备注': '',
		'类型': '供货'
	}

])
//当前选中的表单为
const currentForm = ref({})
/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '订单'
});
</script>

<template>
	<basic-layout show-tab-bar>
		<custom-navbar title="订单" />
		<!-- 轮播 -->
		<div class="swiper-box">
			<nut-swiper :auto-play="3000" loop direction="vertical" style="height: 150px">
				<nut-swiper-item v-for="(item, index) in list" :key="index" style="height: 150px">
					<img :src="item" alt="" style="height: 100%; width: 100%" draggable="false" />
				</nut-swiper-item>
			</nut-swiper>
		</div>
		<!-- 公告 -->
		<div class="notice-box m-2">
			<nut-noticebar direction="vertical" :list="noticeList" :speed="10" :stand-time="2000" background="#F5F5F5">
				<template #right-icon>
					<IconFont class="i-bx-bell"></IconFont>
				</template>
			</nut-noticebar>
		</div>
		<!-- 订单按钮 -->
		<div class="button-box flex flex-justify-around m-auto">
			<nut-button color="linear-gradient(to right, #0ebeff,  #36a9f2)" style="width: 47.5%;height: 4.5rem;"
				shape="square" @click="supplyClick">
				<template #icon>
					<IconFont class="i-bx-clipboard"></IconFont>
				</template>
				{{
					'货源供应'
				}}
			</nut-button>
			<nut-dialog :title="buyOrSupplyMap.get(buyOrSupply)" v-model:visible="visible1" @cancel="onCancel" @ok="onOk"
				ok-text="提交" :lock-scroll="false">
				<scroll-view class="scroll-view_H" scroll-Y="true">
					<nut-form>
						<template v-for="(item, index) in currentForm" :key="index">
							<nut-form-item :label="index">
								<nut-input :placeholder="'请输入' + index" type="text" v-model="currentForm[index]" />
							</nut-form-item>
						</template>
					</nut-form>
				</scroll-view>
			</nut-dialog>
			<nut-button color="linear-gradient(to right,  #8780d8,  #af6bcb)" style="width: 47.5%;height: 4.5rem;"
				shape="square" @click="buyClick">
				<template #icon>
					<IconFont class="i-bx-cart-download"></IconFont>
				</template>
				{{
					'求购货源'
				}}
			</nut-button>
		</div>
		<!-- 查看订单池 -->
		<div class="history-box pb-4 pt-4 flex justify-center">
			<nut-button color="linear-gradient(to right,  #f968bf,  #f486c8)" style="width: 98%;height: 4.5rem;"
				shape="square" @click="goToFillout">
				<template #icon>
					<IconFont class="i-bx-book-content"></IconFont>
				</template>
				{{
					'查看供求订单池'
				}}
			</nut-button>
		</div>
		<!-- 历史记录 -->
		<div class="history-box p-4">
			<div class="history-title">
				<div class="title-text font-900 p-2">
					{{ '历史订单记录' }}
				</div>
				<div class="title-more">
					<!-- 如果有记录，则展示记录列表，负责展示暂无数据 -->
					<template v-if="historyList.length > 0">
						<nut-list v-model:list-data="dataList" :container-height="470" @scroll-bottom="onScrollBottom">
							<template #default="{ item }">
								<div class="order-card list-item">
									<nut-cell :title="'联系人:' + item['联系人']" :desc="item['类型']" is-link>
										<template #icon>
											<My />
										</template>
									</nut-cell>
									<nut-cell :title="'产品型号:' + item['产品型号']" :desc="'数量:' + (item['供货数量'] ?? item['求购数量'])">
										<template #icon>
											<Order />
										</template>
									</nut-cell>
									<nut-cell :title="'时间:' + (item['供货时间'] ?? item['求购时间'])">
										<template #icon>
											<Clock />
										</template>
									</nut-cell>
									<nut-cell :title="'地址:' + (item['供货地址'] ?? item['求购地址'])">
										<template #icon>
											<Location />
										</template>
									</nut-cell>
								</div>
							</template>
						</nut-list>
					</template>
					<template v-else>
						<!-- 暂无数据 -->
						<div class="no-data">
							<nut-empty description="暂无数据" />
						</div>
					</template>
				</div>
			</div>
		</div>
		<!-- <nut-cell title="分包A" @click="handleToA"></nut-cell>
		<nut-cell title="分包B" @click="handleToA"></nut-cell> -->
	</basic-layout>
</template>
<style lang="scss">
.history-box {
	.history-title {
		border-radius: .3125rem;
		padding: .3125rem;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

		.title-text {
			color: #666874;
		}
	}

}

.scroll-view_H {
	white-space: nowrap;
}

.order-card {
	border-radius: 10px;
	//添加边框阴影
	box-shadow: 0 0 10px rgba(94, 92, 92, 0.1);
	border: 2px solid #A7B0C9;
	margin: 3% 0;

	.nut-cell {
		margin: 0 !important;
		border-radius: none !important;
	}
}
</style>
