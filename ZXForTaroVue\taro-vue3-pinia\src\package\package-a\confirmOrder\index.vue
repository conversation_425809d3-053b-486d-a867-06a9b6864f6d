<template>
    <div class="confirm-order-page">
        <!-- 优化后的楼层搜索弹窗 -->
        <nut-action-sheet v-model:visible="show" class="floor-search-sheet">
            <div class="search-container">
                <div class="search-header">
                    <h3 class="search-title">选择楼层图案</h3>
                    <p class="search-subtitle">请输入楼层编号进行搜索</p>
                </div>
                <nut-searchbar v-model="searchValue" shape="round" placeholder="请输入楼层以搜索" input-background="#f8f9fa"
                    @change="GetInputFocus" id="pop-target" class="custom-searchbar">
                    <template #leftin>
                        <Search2 />
                    </template>
                </nut-searchbar>

                <div class="search-results-container">
                    <div class="search-results">
                        <div v-if="searchList.length > 0" class="results-list">
                            <nut-list :list-data="searchList" :container-height="280" @scroll-bottom="onScrollBottom">
                                <template #default="{ item }">
                                    <div class="floor-item" @click="clickItem(item)">
                                        <div class="floor-info">
                                            <i class="i-bx-building-house floor-icon"></i>
                                            <span class="floor-name">{{ item.name }}</span>
                                        </div>
                                        <i class="i-bx-chevron-right select-icon"></i>
                                    </div>
                                </template>
                            </nut-list>
                        </div>

                        <div v-else class="empty-search">
                            <div class="empty-icon">
                                <i class="i-bx-search-alt"></i>
                            </div>
                            <p class="empty-text">请输入相应正确楼层编号后进行查询</p>
                        </div>
                    </div>
                </div>
            </div>
        </nut-action-sheet>
        <!-- 优化后的收货地址区域 -->
        <div class="address-section">
            <div class="section-header">
                <div class="header-left">
                    <i class="i-bx-map section-icon"></i>
                    <span class="section-title">收货地址</span>
                </div>
                <div class="header-right" @click="editClick">
                    <span class="edit-text">编辑</span>
                    <i class="i-bx-edit-alt edit-icon"></i>
                </div>
            </div>
            <div class="address-content">
                <nut-address-list :data="harvestData" :show-bottom-button="false" :data-options="harvestOptions">
                    <template #item-icon>
                        <div class="address-icon">
                            <i class="i-bx-user"></i>
                        </div>
                    </template>
                </nut-address-list>
            </div>
        </div>
        <!-- 优化后的项目信息区域 -->
        <div class="project-section">
            <div class="section-header">
                <i class="i-bx-folder section-icon"></i>
                <span class="section-title">项目信息</span>
            </div>
            <div class="project-content">
                <div class="input-group">
                    <div class="input-label required">
                        <span>项目名称</span>
                        <span class="required-mark">*</span>
                    </div>
                    <nut-textarea v-model="project_Name" limit-show :max-length="50" autosize placeholder="请输入项目名称"
                        class="custom-textarea project-textarea" />
                </div>
                <div class="input-group">
                    <div class="input-label">
                        <span>备注信息</span>
                    </div>
                    <nut-textarea v-model="note" limit-show :max-length="50" autosize placeholder="请输入备注信息（选填）"
                        class="custom-textarea note-textarea" />
                </div>
            </div>
        </div>
        <nut-popup class="flex flex-col justify-evenly" v-model:visible="popupShow" position="bottom" round
            :style="{ height: '65vh' }" :safe-area-inset-bottom="true">
            <Address @click-item="(val) => { popupShow = val.boolean; harvestData[0] = val.item }" :showDefault="true"
                @defauult-emit="() => {
                    popupShow = false; harvestData[0].addressName = '采用U9默认联系地址'; harvestData[0].fullAddress = ' '; harvestData[0].phone = ' '; harvestData[0].defaultAddress = false
                }"></Address>






            <!-- <nut-form ref="formRef">
                <nut-form-item label="联系人" required prop="testaddressName" :rules="[
                    {
                        validator: validatorName,
                        message: '请输入正确的人名'
                    }
                ]">
                    <nut-input v-model="formData.testaddressName" placeholder="请输入联系人" type="text" />
                </nut-form-item>
                <nut-form-item label="联系电话" required prop="phone" :rules="[
                    {
                        validator: validatorPhone,
                        message: '请输入正确的手机号'

                    }
                ]">
                    <nut-input v-model="formData.phone" placeholder="请输入联系电话" type="text" />
                </nut-form-item>
                <nut-form-item label="联系地址" required prop="fullAddress" :rules="[
                    {
                        validator: validatorAddress,
                        message: '地址应尽量详细'

                    }

                ]">
                    <nut-input v-model="formData.fullAddress" placeholder="请输入地址" type="text" />
                </nut-form-item>

            </nut-form>
            <nut-button type="primary" size="large" class="block! verifi-button w-80%! ml-auto! mr-auto!" block
                @click="submitAddress">{{
                    '提交' }}</nut-button> -->

        </nut-popup>
        <!-- 优化后的商品列表区域 -->
        <div class="products-section">
            <div class="section-header">
                <i class="i-bx-package section-icon"></i>
                <span class="section-title">商品清单</span>
                <span class="product-count">({{ cartCommodities.length }}款)</span>
            </div>
            <div class="products-content">
                <div v-if="cartCommodities.length > 0" class="product-list">
                    <div v-for="(item, index) in cartCommodities" :key="item.id" class="product-item">
                        <div class="product-card">
                            <!-- 商品主要信息区域 -->
                            <div class="product-main-info">
                                <div class="product-image-section">
                                    <div class="image-container">
                                        <image :src="item.image" class="product-image" />
                                        <div class="image-overlay" v-if="!item.image">
                                            <i class="i-bx-image-alt"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="product-info-section">
                                    <div class="product-title-area">
                                        <!-- 产品型号标签 -->
                                        <div class="model-tag">
                                            <i class="i-bx-crown"></i>
                                            <span>{{ item.model }}</span>
                                        </div>

                                        <!-- 产品名称标签 -->
                                        <div class="name-tag">
                                            {{ item.name.split('/')[0] }}
                                        </div>

                                        <!-- 产品子名称 -->
                                        <div class="sub-name-tag" v-show="item.name.split('/')[1]">
                                            {{ item.name.split('/')[1] }}
                                        </div>
                                    </div>

                                    <!-- 产品规格 -->
                                    <div class="product-spec">
                                        <span class="spec-text">{{ item.spec }}</span>
                                    </div>
                                    <!-- 订单类型 -->
                                    <div class="name-tag">

                                        
                                        <nut-popup v-model:visible="show" position="bottom">
                                            <nut-picker v-model="['1']" :columns="classColumns" title="订单类型"
                                                @confirm="classConfirm" @cancel="show = false" />
                                        </nut-popup>
                                    </div>

                                    <!-- 价格和编号区域 -->
                                    <div class="price-code-section">
                                        <div class="price-area">
                                            <span class="currency">¥</span>
                                            <span class="price">{{ item.price_unit }}</span>
                                            <span class="unit">/PCS</span>
                                        </div>
                                        <div class="code-area">
                                            <span class="code-tag">{{ item.code }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 设计选择组件 -->
                            <DesignCell :design="item.seiban" :id="item.id" :index="index" :name="item.name"
                                @update:design="showDialog"
                                :class="{ noShow: !item.name.split('/')[0].includes('特殊图案') }" class="design-cell" />

                            <!-- 商品备注区域 -->
                            <div class="product-note-section">
                                <div class="note-label">
                                    <i class="i-bx-message-square-detail"></i>
                                    <span>商品备注</span>
                                </div>
                                <nut-input v-model="item.leaveMsg" :max-length="50" placeholder="请输入商品行内备注（下单后无法追加）"
                                    class="note-input">
                                    <template #right>
                                        <i class="i-bx-edit-alt note-edit-icon"></i>
                                    </template>
                                </nut-input>
                            </div>

                            <!-- 数量显示区域 -->
                            <div class="quantity-section">
                                <div class="quantity-label">数量</div>
                                <div class="quantity-value">{{ item.count }} 件</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="empty-products">
                    <div class="empty-icon">
                        <i class="i-bx-package"></i>
                    </div>
                    <p class="empty-text">暂无商品</p>
                </div>
            </div>
        </div>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>

        <!-- 优化后的底部操作栏 -->
        <div class="bottom-action-bar">
            <div class="action-bar-content">
                <!-- 价格统计区域 -->
                <div class="price-summary">
                    <div class="total-price-section">
                        <span class="total-label">合计:</span>
                        <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
                    </div>
                    <div class="total-count-section">
                        <span class="count-label">共</span>
                        <span class="count-value">{{ countJS }}</span>
                        <span class="count-unit">件</span>
                    </div>
                </div>

                <!-- 确认订单按钮 -->
                <div class="submit-section">
                    <nut-button type="primary" @click="submitOrderF" :disabled="project_Name.length <= 0"
                        class="submit-btn">
                        <i class="i-bx-check-circle"></i>
                        <span>确认订单</span>
                    </nut-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { submitOrder, getProductByNumber, updateFindCart } from '@/service/index'
import Taro from '@tarojs/taro';
import { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { onMounted, onUnmounted, ref, Ref } from 'vue';
import { useAppStore } from '@/store';
import { toRaw } from "@vue/reactivity";
import Address from '@/components/address/index.vue'
const cartCommodities: Ref<Array<{
    id: number,
    code: string,
    name: string,
    spec: string,
    model: string,
    price_unit: number,
    image: string,
    count: number,
    maxBuy: number,
    checkbox: boolean,
    design: string | undefined,
    leaveMsg: string | undefined,
    fanhao_id: string | undefined,
    seiban: string | undefined,
    line_id: number,
    project: string,
    zx_order_type: string | number | undefined
}>> = ref([
])
interface IharvestData {
    addressName: string,
    phone: string,
    defaultAddress: boolean,
    fullAddress: string
    testid: string | number | null,
}
onMounted(() => {
    // 确保传入的数据类型正确，防止字符串拼接问题
    const messageData = preloadMessage!.message.map((item: any) => ({
        ...item,
        price_unit: Number(item.price_unit) || 0,
        count: Number(item.count) || 0,
        maxBuy: Number(item.maxBuy) || 0,
        id: Number(item.id) || 0,
        line_id: Number(item.line_id) || 0
    }));

    cartCommodities.value.push(...messageData);

    console.log('初始化商品数据:', {
        originalData: preloadMessage!.message,
        processedData: messageData,
        dataTypes: messageData.map((item: any) => ({
            name: item.name,
            price_unit: item.price_unit,
            count: item.count,
            price_unit_type: typeof item.price_unit,
            count_type: typeof item.count
        }))
    });

    eventCenter.on(getCurrentInstance().router!.onShow, () => {

        console.log('gotoback' in toRaw(harvestData.value)[0], 'dada');
        // if (!('gotoback' in toRaw(harvestData.value)[0])) {
        //     getDefaultAddressF(Taro.getStorageSync('userInfo').userId)
        // }

        countTotalPrice()
    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)
})

//
const classShow = ref(false)

const classColumns = ref([
  { text: '标准做法', value: '0' },
  { text: '特殊做法', value: '1' },
])

const classConfirm = ({ selectedValue, selectedOptions }:any) => {
  console.log(selectedValue[0], selectedOptions[0])
  show.value = false
}



//备注及项目名称
const note = ref('')
const project_Name = ref('')

//控制弹出层
const popupShow = ref(false)
const editClick = () => {
    console.log('Click To Edit')
    popupShow.value = true
}


// const goToAddress = () => {
//     Taro.navigateTo({
//         url: '/package/package-a/harvestAddress/index',
//         events: {
//             defaultEvent: (data: any) => {
//                 console.log(harvestData.value, 'harsetDataBBBBBBBBB');
//                 harvestData.value = [JSON.parse(JSON.stringify(data)) as IharvestData]
//                 console.log(harvestData.value, 'harvestData');
//                 console.log(data, 'data');


//             }
//         }
//     })
// }

const appStore = useAppStore();
const submitOrderF = async () => {
    // console.log("====");
    // console.log("address", formData.value)
    // console.log("note", note.value);
    // console.log("special_note", special_note.value);
    // console.log("cartCommodities", cartCommodities.value);
    let designConfirm = cartCommodities.value.find(el =>
        el.name.includes('特殊图案') && !('seiban' in el)
    )
    console.log(designConfirm, 'designConfirm');

    if (designConfirm) {

        Taro.showModal({
            title: '提示',
            content: '特殊图案产品需选择楼层图案后才可下单',
            success: function () {
            }
        })
    }
    else {

        let reduceArray = cartCommodities.value.map(item => {
            let { id: product_id, count: product_qty, price_unit, fanhao_id, leaveMsg: zx_line_notes } = item
            return {
                product_id,
                product_qty,
                price_unit,
                fanhao_id,
                zx_line_notes
            }
        })
        console.log('reduceArray', JSON.stringify(reduceArray));
        console.log("====");
        const { error } = await submitOrder({
            ...(!['请输入姓名', '采用U9默认联系地址'].includes(harvestData.value?.[0]?.addressName) && { address: `${harvestData.value[0].fullAddress}${harvestData.value[0].phone}${harvestData.value[0].addressName}` }),
            note: note.value,
            project: project_Name.value,
            order_lines: JSON.stringify(reduceArray)
        })
        if (error === null) {
            Taro.showToast({
                title: '提交成功',
                success: async () => {
                    console.log(Taro.getCurrentInstance().preloadData?.fromSCartPage);
                    if (Taro.getCurrentInstance().preloadData?.fromSCartPage !== undefined) {

                        let remove = reduceArray.map(i =>
                            [
                                -i.product_id,
                                i.product_qty
                            ]
                        )
                        let resultStr = '[' + remove.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
                        try {
                            await updateFindCart({
                                product_ids: resultStr
                            })
                        } catch (error: any) {
                            Taro.showLoading({
                                title: error
                            })
                        }
                    }

                    setTimeout(() => {
                        Taro.switchTab({
                            url: '/pages/orders/index',
                            success: () => {
                                appStore.setActiveTab('/pages/orders/index');
                            }
                        })
                    }, 2000)

                },
                duration: 2000
            })
        } else {
            Taro.showToast({
                title: '网络异常',
                icon: 'error',
                duration: 2000
            })
        }
    }
}
const preloadMessage = Taro.getCurrentInstance().preloadData
const harvestData: Ref<Array<IharvestData>> = ref([{
    addressName: '请输入姓名',
    phone: '手机号',
    defaultAddress: true,
    fullAddress: '请输入详细地址',
    testid: null
}
])
const harvestOptions = {
    addressName: 'addressName'
}

//获取默认地址
// const getDefaultAddressF = async (userId: string) => {
//     const { error, success } = await getDefaultAddress({ userId })
//     if (error === null) {
//         Taro.setStorageSync('defaultAddress', success)

//         harvestData.value = [success as IharvestData]
//     }
// }


const countTotalPrice = () => {
    // 确保数值计算，防止字符串拼接
    totalPrice.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) => {
            // 强制转换为数值类型，防止字符串拼接
            const priceUnit = Number(curr.price_unit) || 0;
            const count = Number(curr.count) || 0;
            return acc + (priceUnit * count);
        }, 0);

    countJS.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) => {
            // 强制转换为数值类型
            const count = Number(curr.count) || 0;
            return acc + count;
        }, 0);

    console.log('价格计算结果:', {
        totalPrice: totalPrice.value,
        countJS: countJS.value,
        selectedItems: cartCommodities.value.filter(x => x.checkbox).map(item => ({
            name: item.name,
            price_unit: item.price_unit,
            count: item.count,
            price_unit_type: typeof item.price_unit,
            count_type: typeof item.count,
            subtotal: Number(item.price_unit) * Number(item.count)
        }))
    });
}
const totalPrice = ref(0)
const countJS = ref(0)
/** 设置页面属性 */
definePageConfig({
    navigationBarTitleText: '确认订单',
});


//楼层字段变更
const show = ref(false)
const searchValue = ref('')
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)
//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
    console.log('子组件返回值:id', msg.id);
    console.log('子组件返回值:index', msg.index);
    ClickshowDialogItem = msg.id
    click()

}
const click = () => {
    show.value = true
}
const GetInputFocus = () => {
    if (searchValue.value.length >= 0) {
        show.value = true
        if (searchValue.value.length > 0) {
            getProductByNumberF(searchValue)
        } else {
            searchList.value = []
            DQsearchListIndex.value = 1
        }

    } else {
        show.value = false
    }

}

const searchList: Ref<any[]> = ref([])
const clickItem = (item: any) => {

    console.log(item, ClickshowDialogItem, 'test');


    const { name, id } = item
    if (ClickshowDialogItem) {
        cartCommodities.value.find(item => {
            if (item.id === ClickshowDialogItem) {
                item.seiban = name
                item.fanhao_id = id
            }
        })
        ClickshowDialogItem = null
        searchValue.value = ''
        console.log('cartCommodities', cartCommodities.value);
        show.value = false
        searchList.value = []


    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'error',
            duration: 2000
        })
    }

}
const onScrollBottom = async () => {
    console.log('触底了');
    console.log('searchListIndex', searchListIndex.value);
    console.log('DQsearchListIndex', DQsearchListIndex.value);


    if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
        searchListIndex.value++
        const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
        if (error === null) {
            const { items, psize } = success as { items: Array<any>, psize: number }
            if (success && items) {
                searchList.value = searchList.value.concat(items)
                DQsearchListIndex.value = psize
            }
        }
    } else {
        Taro.showToast({
            title: '加载完毕了~',
            icon: 'error',
            duration: 2000
        })
    }

}
const getProductByNumberF = async (item: Ref<string>) => {
    const { success } = await getProductByNumber({ name: item.value, per_page: '20' })
    console.log('success                  ~~~', success);
    const { items, psize } = success as { items: Array<any>, psize: number }
    if (success && items) {
        searchList.value = items
        DQsearchListIndex.value = psize
    }
}

</script>

<style lang="scss">
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 页面基础样式
page {
    background: $background-light;
}

.confirm-order-page {
    min-height: 100vh;
    background: $background-light;
    padding-bottom: 80px;
}

// 楼层搜索弹窗样式
.floor-search-sheet {
    --nutui-actionsheet-border-radius: 16px 16px 0 0;

    .search-container {
        padding: 20px;
        background: #fff;

        .search-header {
            text-align: center;
            margin-bottom: 20px;

            .search-title {
                font-size: 18px;
                font-weight: 600;
                color: $text-primary;
                margin: 0 0 4px 0;
            }

            .search-subtitle {
                font-size: 14px;
                color: $text-secondary;
                margin: 0;
            }
        }

        .custom-searchbar {
            --nutui-searchbar-background: #{$primary-light};
            --nutui-searchbar-input-background: #fff;
            --nutui-searchbar-input-text-color: #{$text-primary};
            margin-bottom: 16px;
        }

        .search-results-container {
            .search-results {
                .results-list {
                    max-height: 280px;
                    overflow-y: auto;

                    .floor-item {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 12px 16px;
                        background: #fff;
                        border-radius: 8px;
                        margin-bottom: 8px;
                        border: 1px solid $border-color;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover,
                        &:active {
                            background: $primary-light;
                            border-color: $primary-color;
                        }

                        .floor-info {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .floor-icon {
                                font-size: 16px;
                                color: $primary-color;
                            }

                            .floor-name {
                                font-size: 14px;
                                color: $text-primary;
                                font-weight: 500;
                            }
                        }

                        .select-icon {
                            font-size: 16px;
                            color: $text-light;
                        }
                    }
                }

                .empty-search {
                    text-align: center;
                    padding: 40px 20px;

                    .empty-icon {
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: $primary-light;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;

                        i {
                            font-size: 24px;
                            color: $primary-color;
                        }
                    }

                    .empty-text {
                        font-size: 14px;
                        color: $text-secondary;
                        margin: 0;
                    }
                }
            }
        }
    }
}

// 收货地址区域样式
.address-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .section-icon {
                font-size: 18px;
                color: #fff;
            }

            .section-title {
                font-size: 16px;
                font-weight: 600;
                color: #fff;
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;

            &:active {
                background: rgba(255, 255, 255, 0.2);
            }

            .edit-text {
                font-size: 14px;
                color: #fff;
                font-weight: 500;
            }

            .edit-icon {
                font-size: 14px;
                color: #fff;
            }
        }
    }

    .address-content {
        padding: 16px;

        .address-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: $primary-light;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                font-size: 16px;
                color: $primary-color;
            }
        }
    }
}

// 项目信息区域样式
.project-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }
    }

    .project-content {
        padding: 16px;

        .input-group {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            .input-label {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 8px;

                span {
                    font-size: 14px;
                    color: $text-primary;
                    font-weight: 500;
                }

                .required-mark {
                    color: $error-color;
                    font-weight: 600;
                }
            }

            .custom-textarea {
                --nutui-textarea-border-color: #{$border-color};
                --nutui-textarea-border-radius: 8px;
                --nutui-textarea-text-color: #{$text-primary};
                --nutui-textarea-placeholder-color: #{$text-light};

                &.project-textarea {
                    --nutui-textarea-border-color: #{$error-color};
                }
            }
        }
    }
}

// 商品列表区域样式
.products-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .product-count {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: auto;
        }
    }

    .products-content {
        .product-list {
            .product-item {
                border-bottom: 1px solid $border-color;

                &:last-child {
                    border-bottom: none;
                }

                .product-card {
                    display: flex;
                    flex-direction: column;
                    padding: 10px;
                    gap: 6px;

                    // 商品主要信息区域（图片+基本信息）
                    .product-main-info {
                        display: flex;
                        gap: 8px;

                        .product-image-section {
                            flex-shrink: 0;

                            .image-container {
                                position: relative;
                                width: 60px;
                                height: 60px;
                                border-radius: 6px;
                                overflow: hidden;
                                background: $background-light;

                                .product-image {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                }

                                .image-overlay {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    background: $background-light;

                                    i {
                                        font-size: 18px;
                                        color: $text-light;
                                    }
                                }
                            }
                        }

                        .product-info-section {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 4px;
                            min-width: 0;

                            .product-title-area {
                                display: flex;
                                flex-direction: column;
                                gap: 2px;

                                .model-tag {
                                    display: flex;
                                    align-items: center;
                                    gap: 2px;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: $primary-light;
                                    border: 1px solid $primary-color;
                                    width: fit-content;

                                    i {
                                        font-size: 12px;
                                        color: $primary-color;
                                    }

                                    span {
                                        font-size: 12px;
                                        color: $primary-color;
                                        font-weight: 600;
                                        line-height: 1.2;
                                    }
                                }

                                .name-tag {
                                    display: block;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(76, 175, 80, 0.1);
                                    border: 1px solid $success-color;
                                    font-size: 12px;
                                    color: $success-color;
                                    font-weight: 600;
                                    line-height: 1.2;
                                    width: fit-content;
                                }

                                .sub-name-tag {
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(255, 107, 53, 0.1);
                                    font-size: 11px;
                                    color: $accent-color;
                                    font-weight: 500;
                                    line-height: 1.2;
                                    width: fit-content;
                                }
                            }

                            .product-spec {
                                .spec-text {
                                    font-size: 12px;
                                    color: $text-secondary;
                                    background: $background-light;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    display: inline-block;
                                    line-height: 1.3;
                                }
                            }

                            .price-code-section {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .price-area {
                                    display: flex;
                                    align-items: baseline;
                                    gap: 2px;

                                    .currency {
                                        font-size: 14px;
                                        color: $accent-color;
                                        font-weight: 600;
                                    }

                                    .price {
                                        font-size: 18px;
                                        color: $accent-color;
                                        font-weight: 700;
                                    }

                                    .unit {
                                        font-size: 12px;
                                        color: $text-secondary;
                                        font-weight: 500;
                                    }
                                }

                                .code-area {
                                    .code-tag {
                                        background: $primary-color;
                                        color: #fff;
                                        padding: 2px 6px;
                                        border-radius: 3px;
                                        font-size: 11px;
                                        font-weight: 600;
                                    }
                                }
                            }
                        }
                    }

                    // 设计选择组件
                    .design-cell {
                        margin-top: 4px;
                        padding-top: 4px;
                        border-top: 1px solid $border-color;
                    }

                    // 商品备注区域
                    .product-note-section {
                        margin-top: 4px;
                        padding-top: 4px;
                        border-top: 1px solid $border-color;

                        .note-label {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            margin-bottom: 6px;

                            i {
                                font-size: 14px;
                                color: $primary-color;
                            }

                            span {
                                font-size: 14px;
                                color: $text-primary;
                                font-weight: 500;
                            }
                        }

                        .note-input {
                            --nutui-input-border-color: #{$border-color};
                            --nutui-input-border-radius: 6px;
                            --nutui-input-text-color: #{$text-primary};
                            --nutui-input-placeholder-color: #{$text-light};

                            .note-edit-icon {
                                color: $text-light;
                                font-size: 14px;
                            }
                        }
                    }

                    // 数量显示区域
                    .quantity-section {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-top: 4px;
                        padding: 6px 10px;
                        background: $background-light;
                        border-radius: 6px;

                        .quantity-label {
                            font-size: 14px;
                            color: $text-secondary;
                            font-weight: 500;
                        }

                        .quantity-value {
                            font-size: 16px;
                            color: $primary-color;
                            font-weight: 600;
                        }
                    }
                }
            }
        }

        .empty-products {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: $primary-light;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;

                i {
                    font-size: 32px;
                    color: $primary-color;
                }
            }

            .empty-text {
                font-size: 16px;
                color: $text-secondary;
                margin: 0;
            }
        }
    }
}

// 底部操作栏样式
.bottom-spacer {
    height: 80px;
}

.bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid $border-color;
    box-shadow: 0 -2px 12px rgba(18, 47, 56, 0.08);
    z-index: 999;

    .action-bar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        gap: 16px;

        .price-summary {
            flex: 1;

            .total-price-section {
                display: flex;
                align-items: baseline;
                gap: 4px;
                margin-bottom: 4px;

                .total-label {
                    font-size: 14px;
                    color: $text-secondary;
                    font-weight: 500;
                }

                .total-price {
                    font-size: 20px;
                    color: $accent-color;
                    font-weight: 700;
                }
            }

            .total-count-section {
                display: flex;
                align-items: baseline;
                gap: 2px;

                .count-label {
                    font-size: 12px;
                    color: $text-secondary;
                }

                .count-value {
                    font-size: 16px;
                    color: $primary-color;
                    font-weight: 600;
                }

                .count-unit {
                    font-size: 12px;
                    color: $text-secondary;
                }
            }
        }

        .submit-section {
            flex-shrink: 0;

            .submit-btn {
                --nutui-button-primary-background-color: #{$primary-color};
                --nutui-button-primary-border-color: #{$primary-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                --nutui-button-font-size: 16px;
                padding: 0 24px;
                height: 44px;
                display: inline-flex;
                align-items: center;
                gap: 6px;

                i {
                    font-size: 16px;
                }

                &:not(:disabled):active {
                    --nutui-button-primary-background-color: #{$primary-dark};
                }

                &:disabled {
                    --nutui-button-primary-background-color: #{$text-light};
                    --nutui-button-primary-border-color: #{$text-light};
                }
            }
        }
    }
}

// 弹窗样式覆盖
.nut-popup {
    --nutui-popup-border-radius: 16px 16px 0 0;
}

// 地址列表样式覆盖
.nut-address-list {
    width: 100% !important;

    &:last-child {
        padding-bottom: 0 !important;
    }
}

// 输入框样式覆盖
.nut-input .input-text {
    color: $text-primary !important;
}

// 隐藏元素
.noShow {
    visibility: hidden !important;
}

// 全局组件样式调整
.nut-cell-group__wrap {
    box-shadow: none;
}

// 价格组件样式调整
.nut-price {
    --nutui-price-symbol-color: #{$accent-color};
    --nutui-price-integer-color: #{$accent-color};
    --nutui-price-decimal-color: #{$accent-color};
}

// 标签组件样式调整
.nut-tag {
    --nutui-tag-border-radius: 4px;
    --nutui-tag-font-weight: 500;
}

// 按钮组件样式调整
.nut-button {
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

// 文本域样式调整
.nut-textarea {
    --nutui-textarea-border-color: #{$border-color};
    --nutui-textarea-border-radius: 8px;
    --nutui-textarea-text-color: #{$text-primary};
    --nutui-textarea-placeholder-color: #{$text-light};
}

// 响应式设计
@media (max-width: 375px) {
    .confirm-order-page {
        padding-bottom: 70px;
    }

    .address-section,
    .project-section,
    .products-section {
        margin: 8px;
        border-radius: 8px;

        .section-header {
            padding: 12px;

            .section-title {
                font-size: 14px;
            }
        }
    }

    .product-card {
        padding: 8px !important;
        gap: 4px !important;

        .product-main-info {
            gap: 6px;

            .product-image-section .image-container {
                width: 50px;
                height: 50px;
            }

            .product-info-section {
                gap: 3px;

                .product-title-area {
                    gap: 1px;

                    .model-tag,
                    .name-tag {
                        padding: 1px 4px;
                        font-size: 11px;
                    }

                    .sub-name-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }

                .product-spec .spec-text {
                    font-size: 11px;
                    padding: 1px 4px;
                }

                .price-code-section {
                    .price-area {
                        .currency {
                            font-size: 12px;
                        }

                        .price {
                            font-size: 16px;
                        }

                        .unit {
                            font-size: 10px;
                        }
                    }

                    .code-area .code-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }
            }
        }

        .product-note-section {
            margin-top: 3px;
            padding-top: 3px;

            .note-label {
                gap: 4px;
                margin-bottom: 4px;

                i {
                    font-size: 12px;
                }

                span {
                    font-size: 12px;
                }
            }
        }

        .quantity-section {
            margin-top: 3px;
            padding: 4px 8px;

            .quantity-label {
                font-size: 12px;
            }

            .quantity-value {
                font-size: 14px;
            }
        }

        .design-cell {
            margin-top: 3px;
            padding-top: 3px;
        }
    }

    .bottom-action-bar .action-bar-content {
        padding: 10px 12px;
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
        gap: 12px;

        .price-summary .total-price-section .total-price {
            font-size: 18px;
        }

        .submit-section .submit-btn {
            height: 40px;
            font-size: 14px;
            padding: 0 20px;
        }
    }
}
</style>