<template>
    <template v-for="state in searchList" :key="state.itemNumber">
        <div class="product-card">
            <div class="card-content">
                <!-- 产品图片 -->
                <div class="image-container">
                    <div class="image-wrapper">
                        <image :src="state.imgUrl" mode="aspectFit" class="product-image" />
                        <!-- 加载状态 -->
                        <div class="image-placeholder" v-if="!state.imgUrl">
                            <i class="i-bx-image-alt"></i>
                        </div>
                    </div>

                    <!-- 销量标签 -->
                    <div class="rank-badge">
                        <i class="i-bx-crown"></i>
                        <span>{{ state.shopDesc }}</span>
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="product-info">
                    <!-- 产品标题区域 -->
                    <div class="title-section">
                        <div class="product-title">
                            <span class="model-name">{{ state.title.split('/')[0] }}</span>
                        </div>
                    </div>
                    <!-- 产品型号 -->
                    <div class="item-number">
                        <div class="code-wrapper">
                            <span class="code-text" v-if="state.title.split('/')[1]">
                                {{ state.title.split('/')[1] }}
                            </span>
                        </div>
                    </div>
                    <!-- 产品编号 -->
                    <div class="item-number">
                        <div class="code-wrapper">
                            <span class="code-text">{{ state.itemNumber }}</span>
                        </div>
                    </div>

                    <!-- 价格和操作区域 -->
                    <div class="bottom-section">
                        <div class="price-container">
                            <span class="currency">¥</span>
                            <span class="price">{{ state.price.replace('￥', '') }}</span>
                            <span class="unit">/PCS</span>
                        </div>

                        <div class="add-to-cart-btn" @click.stop="gotoFind(state.itemNumber, state.itemNumber)">
                            <i class="i-bx-plus"></i>
                        </div>
                    </div>
                </div>

                <!-- 悬浮效果装饰 -->
                <div class="card-decoration"></div>
            </div>
        </div>
    </template>
</template>
<script setup lang="ts">
import { onMounted, ref, watch, watchEffect } from 'vue'
import { defineProps } from 'vue';
import { getProduct, hotProduct } from '@/service/index'
import Taro from '@tarojs/taro';
const props = defineProps({
    state: Array
})
const gotoFind = async (i: any, name: string) => {
    //发起网络请求
    const { error, success }: { error: any, success: any } = await getProduct({ code: i })
    console.log(error, success);
    if (error == null) {
        //跳转产品详情页
        Taro.preload({ model: name })
        Taro.navigateTo({
            url: '/package/package-a/Details/index?model=' + name,
            fail: (res: any) => {
                console.log(res)
            }
        })
        console.log('当前分类数据')
    } else {
        Taro.showToast({
            title: '暂无数据',
            icon: 'error',
            duration: 2000
        })
    }
}

// 切换收藏状态
const toggleFavorite = (item: any) => {
    // 这里可以添加收藏逻辑
    Taro.showToast({
        title: '收藏功能开发中',
        icon: 'none',
        duration: 1500
    })
}

// 添加到购物车
const addToCart = (item: any) => {
    // 这里可以添加购物车逻辑
    Taro.showToast({
        title: '已添加到购物车',
        icon: 'success',
        duration: 1500
    })
}
const name = ref('')
const searchList = ref([])
watch([props], async () => {
    console.log('变化了');

    await getProductF()
})
const getProductF = async () => {
    const { error, success } = await hotProduct()
    if (error == null && success) {
        console.log('success~~~~~~', success.items);

        searchList.value = success.items.map((i: { image: any; model: string; name: string; price_unit: string; code: any; }, index: number) => {
            return {
                imgUrl: i.image,
                title: i.model + '/' + i.name,
                price: '￥' + i.price_unit,
                name: i.name,
                state: i.code,
                shopDesc: `销量TOP` + Number(index + 1),
                tag: '',
                itemNumber: i.code
            }

        })
        // console.log('shopcard', success);
        // i.image=success.items[0].image
        // state.value.imgUrl = success.items[0].image
        // state.value.title = success.items[0].model + '/' + success.items[0].name
        // state.value.price = '￥' + success.items[0].price_unit
        // name.value = success.items[0].name
        // state.value.itemNumber = success.items[0].code

        // i.product_id=success.items[0].id
    }

}
onMounted(async () => {
    await getProductF()
})


const state = ref({
    imgUrl:
        'https://img01.71360.com/file/read/www/M00/41/57/rBwBHmTWAw-AFdtTAAEdAOpPUAo680.png?w=600',
    title: 'ZX1650A/单左/集电集控型消防应急标志灯具',
    price: '￥ 388',
    shopDesc: '销量TOP1',
    tag: '促销中',
    itemNumber: ''

})
</script>
<style lang="scss">
.product-card {
    width: 45%;
    padding: 0;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 16px rgba(18, 47, 56, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(18, 47, 56, 0.05);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(18, 47, 56, 0.12);
        border-color: rgba(18, 47, 56, 0.1);
    }

    &:active {
        transform: translateY(-1px) scale(0.98);
    }
}

.card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

.image-container {
    position: relative;
    width: 100%;
    margin-bottom: 12px;

    .image-wrapper {
        position: relative;
        width: 100%;
        height: 140px;
        background: #f8f9fa;
        border-radius: 12px 12px 0 0;
        overflow: hidden;

        .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .image-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            color: #ccc;

            i {
                font-size: 32px;
            }
        }
    }

    .rank-badge {
        position: absolute;
        top: 8px;
        left: 8px;
        background: linear-gradient(135deg, #122F38 0%, #1a4249 100%);
        color: #fff;
        font-size: 10px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 3px;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(18, 47, 56, 0.2);

        i {
            font-size: 11px;
            color: #FFD700;
        }

        span {
            font-size: 9px;
        }
    }

    .quick-actions {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .action-btn {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            i {
                font-size: 14px;
                color: #122F38;
            }

            &:hover {
                background: #122F38;
                transform: scale(1.1);

                i {
                    color: #fff;
                }
            }
        }
    }
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 12px 16px 16px;
    gap: 8px;
}

.title-section {
    .product-title {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .model-name {
            font-size: 13px;
            font-weight: 600;
            color: #122F38;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-name {
            font-size: 11px;
            font-weight: 400;
            color: #666;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
}

.item-number {
    .code-wrapper {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        background: rgba(255, 107, 53, 0.08);
        border-radius: 6px;
        border: 1px solid rgba(255, 107, 53, 0.2);
        width: fit-content;

        i {
            font-size: 12px;
            color: #FF6B35;
        }

        .code-text {
            font-size: 12px;
            font-weight: 600;
            color: #FF6B35;
            font-family: 'Courier New', monospace;
        }
    }
}

.bottom-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 8px;

    .price-container {
        display: flex;
        align-items: baseline;
        gap: 1px;

        .currency {
            font-size: 12px;
            color: #122F38;
            font-weight: 500;
        }

        .price {
            color: #122F38;
            font-weight: 700;
            font-size: 18px;
            line-height: 1;
        }

        .unit {
            color: #FF6B35;
            font-size: 9px;
            font-weight: 600;
            margin-left: 2px;
        }
    }

    .add-to-cart-btn {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: linear-gradient(135deg, #122F38 0%, #1a4249 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(18, 47, 56, 0.2);

        i {
            font-size: 16px;
            color: #fff;
        }

        &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(18, 47, 56, 0.3);
        }

        &:active {
            transform: scale(0.95);
        }
    }
}

.card-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #122F38 0%, #FF6B35 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .card-decoration {
    opacity: 1;
}

// 响应式调整
@media (max-width: 750px) {
    .product-card {
        width: 48%;
        margin-bottom: 12px;

        .image-container .image-wrapper {
            height: 120px;
        }

        .product-info {
            padding: 10px 12px 12px;
        }

        .bottom-section .price-container .price {
            font-size: 16px;
        }
    }
}

@media (max-width: 480px) {
    .product-card {
        width: 100%;
        margin: 0 0 16px 0;
        border-radius: 8px;

        .image-container .image-wrapper {
            height: 160px;
            border-radius: 8px 8px 0 0;
        }

        .product-info {
            padding: 12px 16px 16px;
        }

        .title-section .product-title {
            .model-name {
                font-size: 14px;
            }

            .product-name {
                font-size: 12px;
            }
        }

        .bottom-section {
            .price-container .price {
                font-size: 20px;
            }

            .add-to-cart-btn {
                width: 36px;
                height: 36px;

                i {
                    font-size: 18px;
                }
            }
        }
    }
}

// 加载动画
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

// 悬浮效果增强
.product-card:hover {
    .product-image {
        transform: scale(1.05);
    }

    .rank-badge {
        transform: scale(1.05);
    }

    .quick-actions .action-btn {
        transform: translateY(-2px);
    }
}
</style>