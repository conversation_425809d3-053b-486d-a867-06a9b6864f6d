"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[642],{404:function(e,n,t){var o=t(1065),a=t(6944),r=t(889),l=(t(9932),t(7643)),i=(t(3939),t(1845)),c=(t(3505),t(9841)),u=(t(30),t(5841)),s=t(3191),p=t(3091),d=t(3221),v=t(7011),f=t(6821),g=t(2e3),m=t(5969),h=t(6249),w=Object.defineProperty,b=Object.defineProperties,y=Object.getOwnPropertyDescriptors,k=Object.getOwnPropertySymbols,x=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable,Z=function(e,n,t){return n in e?w(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},P=function(e,n){for(var t in n||(n={}))x.call(n,t)&&Z(e,t,n[t]);if(k){var o,a=(0,p.Z)(k(n));try{for(a.s();!(o=a.n()).done;){t=o.value;_.call(n,t)&&Z(e,t,n[t])}}catch(e){a.e(e)}finally{a.f()}}return e},C=function(e,n){return b(e,y(n))},S=(0,d.aZ)(C(P({},{name:"NutTag"}),{__name:"tag.taro",props:{color:{default:""},textColor:{default:""},type:{default:"default"},plain:{type:Boolean,default:!1},round:{type:Boolean,default:!1},mark:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1}},emits:["close","click"],setup:function(e,n){var t=n.emit,o=e,a=t,r=(0,d.Fl)((function(){var e="nut-tag";return(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},e,!0),"".concat(e,"--").concat(o.type),o.type),"".concat(e,"--plain"),o.plain),"".concat(e,"--round"),o.round),"".concat(e,"--mark"),o.mark)})),l=(0,d.Fl)((function(){var e={};return o.textColor?e.color=o.textColor:o.color&&o.plain&&(e.color=o.color),o.plain?(e.background="#fff",e.borderColor=o.color):o.color&&(e.background=o.color),e})),i=function(e){a("close",e)},c=function(e){a("click",e)};return function(e,n){return(0,d.wg)(),(0,d.iD)("view",{class:(0,v.C_)(r.value),style:(0,v.j5)(l.value),onClick:c},[(0,d.WI)(e.$slots,"default"),(0,d.Uk)(),e.closeable?((0,d.wg)(),(0,d.j4)((0,f.SU)(m.x8),{key:0,class:"nut-tag--close",size:"12px",onClick:(0,g.iM)(i,["stop"])})):(0,d.kq)("",!0)],6)}}}));(0,h.w)(S);var O=t(8751),T=(t(2148),t(1959)),D=t.n(T),j=t(139),H=t(8140),U=t(2739),B=function(e,n){var t=n.args,o=void 0===t?[]:t,a=n.done,r=n.canceled;if(e){var l=e.apply(void 0,(0,H.Z)(o));(0,U.d)(l)?l.then((function(e){e?a(e):r&&r()})).catch((function(){})):l?a():r&&r()}else a()},F=t(9046),N=t(7866),W=t(8978),z=t(4884),I=t(1065)["document"],E=Object.defineProperty,V=Object.defineProperties,q=Object.getOwnPropertyDescriptors,L=Object.getOwnPropertySymbols,M=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable,A=function(e,n,t){return n in e?E(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},R=function(e,n){for(var t in n||(n={}))M.call(n,t)&&A(e,t,n[t]);if(L){var o,a=(0,p.Z)(L(n));try{for(a.s();!(o=a.n()).done;){t=o.value;Y.call(n,t)&&A(e,t,n[t])}}catch(e){a.e(e)}finally{a.f()}}return e},X=function(e,n){return V(e,q(n))},K=(0,j.c)("image-preview"),J=K.create,$=J({props:{show:{type:Boolean,default:!1},images:{type:Array,default:function(){return[]}},contentClose:{type:Boolean,default:!0},initNo:{type:Number,default:0},paginationVisible:{type:Boolean,default:!1},paginationColor:{type:String,default:"#fff"},autoplay:{type:[Number,String],default:3e3},showIndex:{type:Boolean,default:!0},closeable:{type:Boolean,default:!1},closeIconPosition:{type:String,default:"top-right"},beforeClose:Function,isLoop:{type:Boolean,default:!0}},emits:["close","change","longPress"],components:{NutPopup:F.N,NutSwiper:N.Z,NutSwiperItem:W.Z,CircleClose:m.K4},setup:function(e,n){var t=n.emit,o=(0,f.qj)({showPop:!1,active:0,options:{muted:!0,controls:!0},eleImg:null,store:{scale:1,moveable:!1,originScale:1,oriDistance:1},lastTouchEndTime:0,ENV:D().getEnv(),ENV_TYPE:D().ENV_TYPE}),a=(0,d.Fl)((function(){var n={};return"top-right"==e.closeIconPosition?n.right="10px":n.left="10px",n})),r=function(e){e!==o.active&&(o.active=e,t("change",o.active))},l=function(){e.contentClose&&i()},i=function(){B(e.beforeClose,{args:[o.active],done:function(){return c()}})},c=function(){o.showPop=!1,o.store.scale=1,s(),t("close")},u=function(e,n){return Math.hypot(Math.abs(n.x-e.x),Math.abs(n.y-e.y))},s=function(){null!=o.eleImg&&(o.eleImg.style.transform="scale("+o.store.scale+")")},p=function(e){var n=(new Date).getTime(),t=e.touches,a=t[0],r=t[1],l=o.store;n-o.lastTouchEndTime<300&&(l.scale>1?l.scale=1:1==l.scale&&(l.scale=2),s()),l.moveable=!0,r&&(l.oriDistance=u({x:a.pageX,y:a.pageY},{x:r.pageX,y:r.pageY})),l.originScale=l.scale||1},v=function(e){if(o.store.moveable){var n=o.store,t=e.touches,a=t[0],r=t[1];if(r){var l=u({x:a.pageX,y:a.pageY},{x:r.pageX,y:r.pageY}),i=l/n.oriDistance;n.scale=n.originScale*i,o.store.scale=Math.min(o.store.scale,3),s()}}},g=function(){o.lastTouchEndTime=(new Date).getTime();var e=o.store;e.moveable=!1,(e.scale<1.1&&e.scale>1||e.scale<1)&&(e.scale=1,s())},m=function(e){t("longPress",e)},h=function(){o.eleImg=I.querySelector(".nut-image-preview"),I.addEventListener("touchmove",v),I.addEventListener("touchend",g),I.addEventListener("touchcancel",g)};return(0,d.YP)((function(){return e.show}),(function(n){o.showPop=n,n&&(r(e.initNo),h())})),(0,d.YP)((function(){return e.initNo}),(function(e){e!=o.active&&r(e)})),(0,d.bv)((function(){r(e.initNo)})),X(R({},(0,f.BK)(o)),{setActive:r,onClose:i,closeOnImg:l,onTouchStart:p,onTouchMove:v,onTouchEnd:g,getDistance:u,scaleNow:s,longPress:m,styles:a})}}),G=["src","on:longPress","on:longTap"],Q={key:0,class:"nut-image-preview-index"};function ee(e,n,t,o,a,r){var l=(0,d.up)("nut-swiper-item"),i=(0,d.up)("nut-swiper"),c=(0,d.up)("CircleClose"),u=(0,d.up)("nut-popup");return(0,d.wg)(),(0,d.j4)(u,{visible:e.showPop,"onUpdate:visible":n[3]||(n[3]=function(n){return e.showPop=n}),"pop-class":"nut-image-preview-custom-pop"},{default:(0,d.w5)((function(){return[(0,d._)("view",{class:"nut-image-preview",onTouchstartCapture:n[1]||(n[1]=function(){return e.onTouchStart&&e.onTouchStart.apply(e,arguments)})},[e.showPop?((0,d.wg)(),(0,d.j4)(i,{key:0,"auto-play":e.autoplay,class:"nut-image-preview-swiper",loop:e.isLoop,"is-prevent-default":!1,direction:"horizontal","init-page":e.initNo,"pagination-visible":e.paginationVisible,"pagination-color":e.paginationColor,onChange:e.setActive},{default:(0,d.w5)((function(){return[((0,d.wg)(!0),(0,d.iD)(d.HY,null,(0,d.Ko)(e.images,(function(t,o){return(0,d.wg)(),(0,d.j4)(l,{key:o},{default:(0,d.w5)((function(){return[(0,d._)("img",{src:t.src,mode:"aspectFit",class:"nut-image-preview-img","on:longPress":function(n){return e.longPress(t)},"on:longTap":function(n){return e.longPress(t)},onClick:n[0]||(n[0]=(0,g.iM)((function(){return e.closeOnImg&&e.closeOnImg.apply(e,arguments)}),["stop"]))},null,40,G)]})),_:2},1024)})),128))]})),_:1},8,["auto-play","loop","init-page","pagination-visible","pagination-color","onChange"])):(0,d.kq)("",!0)],32),(0,d.Uk)(),e.showIndex?((0,d.wg)(),(0,d.iD)("view",Q,(0,v.zw)(e.active+1)+" / "+(0,v.zw)(e.images.length),1)):(0,d.kq)("",!0),(0,d.Uk)(),e.closeable?((0,d.wg)(),(0,d.iD)("view",{key:1,class:"nut-image-preview-close-icon",style:(0,v.j5)(e.styles),onClick:n[2]||(n[2]=function(){return e.onClose&&e.onClose.apply(e,arguments)})},[(0,d.Wm)(c,{color:"#ffffff"})],4)):(0,d.kq)("",!0)]})),_:1},8,["visible"])}var ne=(0,z._)($,[["render",ee]]),te=t(2344),oe=(t(2240),t(8187)),ae=(t(3796),t(8400)),re=(t(6e3),Object.defineProperty),le=Object.defineProperties,ie=Object.getOwnPropertyDescriptors,ce=Object.getOwnPropertySymbols,ue=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable,pe=function(e,n,t){return n in e?re(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},de=function(e,n){for(var t in n||(n={}))ue.call(n,t)&&pe(e,t,n[t]);if(ce){var o,a=(0,p.Z)(ce(n));try{for(a.s();!(o=a.n()).done;){t=o.value;se.call(n,t)&&pe(e,t,n[t])}}catch(e){a.e(e)}finally{a.f()}}return e},ve=function(e,n){return le(e,ie(n))},fe=(0,d.aZ)(ve(de({},{name:"NutDivider"}),{__name:"divider.taro",props:{contentPosition:{default:"center"},dashed:{type:Boolean,default:!1},hairline:{type:Boolean,default:!0},direction:{default:"horizontal"}},setup:function(e){var n=e,t=(0,d.Rr)(),o=(0,d.Fl)((function(){var e="nut-divider";return"horizontal"===n.direction?(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},e,!0),"".concat(e,"-center"),t.default),"".concat(e,"-left"),"left"===n.contentPosition),"".concat(e,"-right"),"right"===n.contentPosition),"".concat(e,"-dashed"),n.dashed),"".concat(e,"-hairline"),n.hairline):(0,s.Z)((0,s.Z)({},e,!0),"".concat(e,"-vertical"),"vertical"===n.direction)}));return function(e,n){return(0,d.wg)(),(0,d.iD)("view",{class:(0,v.C_)(o.value)},["horizontal"===e.direction?(0,d.WI)(e.$slots,"default",{key:0}):(0,d.kq)("",!0)],2)}}}));(0,h.w)(fe);var ge=t(9909),me=(t(3362),t(9328)),he=(t(8277),t(3193)),we=(t(9157),t(2419)),be=t(2810),ye=t(4081),ke=t(4733),xe=t(7534),_e=t.n(xe),Ze={class:"search-header"},Pe={class:"from-box"},Ce={style:{height:"340px"}},Se={key:0},Oe=["onClick"],Te={key:1,style:{"margin-top":"5%",color:"#333333","font-weight":"900"}},De={key:0,class:"product-section"},je={key:0,class:"product-container"},He={class:"product-list-wrapper"},Ue=["scroll-top"],Be={class:"product-card-wrapper"},Fe={class:"product-checkbox"},Ne={class:"product-image-section"},We={class:"image-container"},ze=["src","onLongpress"],Ie={key:0,class:"image-overlay"},Ee={class:"product-info-section"},Ve={class:"product-title-area"},qe={class:"model-tag"},Le={class:"name-tag"},Me={class:"product-spec"},Ye={class:"price-code-section"},Ae={class:"price-area"},Re={class:"price"},Xe={class:"code-area"},Ke={class:"quantity-control"},Je={key:0,class:"loading-more"},$e={key:1,class:"load-complete"},Ge={key:1,class:"empty-state"},Qe={class:"bottom-action-bar"},en={class:"action-bar-content"},nn={class:"left-section"},tn={class:"price-section"},on={class:"total-price"},an={class:"button-group"},rn=(0,d.aZ)({__name:"index",setup:function(e){(0,T.useRouter)(),(0,ye.tN)();var n=(0,f.iH)(!1),t=_e().throttle((function(e){var t=e.detail||e,o=t.scrollTop,a=t.scrollHeight;console.log("\u6eda\u52a8\u4e8b\u4ef6\u89e6\u53d1:",{scrollTop:o,scrollHeight:a,isLoading:s.value,currentPage:h.value,totalPage:w.value}),n.value=o>=300}),300),o=_e().throttle((function(){console.log("\u89e6\u5e95\u4e8b\u4ef6\u89e6\u53d1",{isLoading:s.value,currentPage:h.value,totalPage:w.value}),!s.value&&h.value<w.value?(console.log("\u5f00\u59cb\u52a0\u8f7d\u66f4\u591a\u6570\u636e"),p()):h.value>=w.value&&console.log("\u5df2\u52a0\u8f7d\u5168\u90e8\u6570\u636e")}),500),s=(0,f.iH)(!1),p=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(){return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("onScrollBottomPage \u88ab\u8c03\u7528",{currentPage:h.value,totalPage:w.value,isLoading:s.value}),!(h.value<w.value)||s.value){e.next=8;break}return h.value=Number(h.value)+1,console.log("\u5f00\u59cb\u52a0\u8f7d\u7b2c",h.value,"\u9875"),e.next=6,b(h.value);case 6:e.next=10;break;case 8:console.log("\u65e0\u6cd5\u52a0\u8f7d\u66f4\u591a:",{reachedEnd:h.value>=w.value,isLoading:s.value}),h.value>=w.value&&D().showToast({title:"\u5168\u90e8\u52a0\u8f7d\u5b8c\u6bd5~",icon:"success",duration:2e3});case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),h=(0,f.iH)(1),w=(0,f.iH)(1),b=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(){var n,t,o,a,r,l,i,c,u,p,d=arguments;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=d.length>0&&void 0!==d[0]?d[0]:1,s.value=!0,e.prev=2,!k.value){e.next=12;break}return e.next=6,(0,ke.wv)({name:k.value,page:Number(n).toString()});case 6:t=e.sent,o=t.error,a=t.success,null===o&&(console.log("\u6570\u636e\u52a0\u8f7d\u7ed3\u679c",a),l=a,i=l.items,c=l.psize,u=l.cur_page,w.value=c,h.value=u,p=[],p=i.map((function(e){return Object.assign(e,{count:1,maxBuy:1e4,checkbox:!1,design:"",leaveMsg:""})})),(r=Z.value).push.apply(r,(0,H.Z)(p)),p=null),e.next=14;break;case 12:return e.next=14,y(k.model);case 14:e.next=20;break;case 16:e.prev=16,e.t0=e["catch"](2),console.error("\u52a0\u8f7d\u4ea7\u54c1\u6570\u636e\u5931\u8d25:",e.t0),D().showToast({title:"\u52a0\u8f7d\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5",icon:"error",duration:2e3});case 20:return e.prev=20,s.value=!1,e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[2,16,20,23]])})));return function(){return e.apply(this,arguments)}}();(0,f.iH)();(0,d.bv)((0,be.Z)((0,we.Z)().mark((function e(){return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("\u9875\u9762\u6302\u8f7d\u5f00\u59cb"),e.next=3,b();case 3:return e.next=5,le();case 5:console.log("\u9875\u9762\u6302\u8f7d\u5b8c\u6210");case 6:case"end":return e.stop()}}),e)})))),(0,d.Ah)((function(){Z.value=[]}));var y=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(n){var t,o,a,r,l,i,c,u,s;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ke.wv)({name:n,page:Number(1).toString()});case 2:t=e.sent,o=t.error,a=t.success,null===o&&(l=a,i=l.items,c=l.psize,u=l.cur_page,J.value=1,$.value=c,w.value=c,h.value=u,console.log("\u6570\u636e\u52a0\u8f7d\u7ed3\u679c~~",c,$.value),s=[],s=i.map((function(e){return Object.assign(e,{count:1,maxBuy:1e4,checkbox:!1,design:"",leaveMsg:"",cartCommodities:!1})})),(r=Z.value).push.apply(r,(0,H.Z)(s)),s=null);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),k=(0,f.qj)(D().getCurrentInstance().preloadData),x=((0,f.iH)(1),function(){D().switchTab({url:"/pages/shoppingCart/index"})}),_=(0,f.iH)(!1),Z=((0,f.iH)(!1),(0,f.iH)([]));(0,d.wF)((function(){}));var P=(0,f.iH)(0),C=((0,f.iH)([]),function(){W.value=!0}),j=function(){try{var e=Z.value.filter((function(e){return!0===e.checkbox})),n=e.reduce((function(e,n){var t=parseFloat(String(n.price_unit))||0,o=parseInt(String(n.count))||0;return e+t*o}),0);P.value=n,console.log("\u4ef7\u683c\u8ba1\u7b97:",{checkedItems:e.length,total:n,items:e.map((function(e){return{name:e.name,price:e.price_unit,count:e.count,subtotal:(parseFloat(String(e.price_unit))||0)*(parseInt(String(e.count))||0)}}))})}catch(e){console.error("\u4ef7\u683c\u8ba1\u7b97\u9519\u8bef:",e),P.value=0}},U=function(e,n){n.checkbox=e,B(),j()},B=function(){var e=Z.value.filter((function(e){return e.checkbox})).length;_.value=e===Z.value.length&&Z.value.length>0},F=function(e){Z.value.forEach((function(n){e===n.id&&(1!==n.count?n.count-=1:D().showToast({title:"\u6700\u5c11\u8d2d\u4e70\u4e00\u4e2a\u5546\u54c1~",icon:"none",duration:2e3}))})),j()},N=function(e){Z.value.forEach((function(n){e===n.id&&(n.count!==n.maxBuy?n.count=Number(n.count)+1:D().showToast({title:"\u5f53\u524d\u6700\u591a\u4ec5\u80fd\u8d2d\u4e70"+n.maxBuy+"\u4efd\u8be5\u5546\u54c1~",icon:"none",duration:2e3}))})),j()},W=((0,f.iH)(0),(0,f.iH)(1),(0,f.iH)(!1)),z=function(){var e=Z.value.filter((function(e){return e.checkbox}));console.log("\u8ba2\u5355\u4fe1\u606f",e),D().navigateTo({url:"/package/package-a/confirmOrder/index"}),D().preload({message:e})},I=(0,f.iH)(0),E=(0,f.iH)(),V=function(){console.log("\u6eda\u52a8\u5230\u9876\u90e8")},q=function(){console.log("\u8fd4\u56de\u9876\u90e8\u6309\u94ae\u70b9\u51fb"),I.value=Math.random(),setTimeout((function(){I.value=0}),50)},L=null,M=function(e){console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:id",e.id),console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:index",e.index),L=e.id,A()},Y=(0,f.iH)(!1),A=function(){Y.value=!0},R=(0,f.iH)(""),X=(0,f.iH)(!1),K=(0,f.iH)([]),J=(0,f.iH)(1),$=(0,f.iH)(1),G=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(n){var t,o,a,r,l;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ke.yS)({name:n.value,per_page:"20"});case 2:t=e.sent,t.error,o=t.success,a=o,r=a.items,l=a.psize,o&&r&&(K.value=r,$.value=l);case 7:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Q=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(){var n,t,o,a,r,l;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("\u89e6\u5e95\u4e86"),!(Number(J.value)<Number($.value))){e.next=11;break}return J.value++,e.next=5,(0,ke.yS)({name:R.value,page:J.value.toString(),per_page:"20"});case 5:n=e.sent,t=n.error,o=n.success,null===t&&(a=o,r=a.items,l=a.psize,o&&r&&(K.value=K.value.concat(r),$.value=l)),e.next=12;break;case 11:D().showToast({title:"\u52a0\u8f7d\u5b8c\u6bd5\u4e86~",icon:"error",duration:2e3});case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ee=function(){R.value.length>=0?(Y.value=!0,R.value.length>0?G(R):(K.value=[],$.value=1)):Y.value=!1},re=function(e){var n=e.name,t=e.id;L?(Z.value.find((function(e){e.id===L&&(e.design=n,e.fanhao_id=t)})),L=null,R.value="",console.log("cartCommodities",Z.value),Y.value=!1,K.value=[]):D().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"error",duration:2e3})},le=function(){var e=(0,be.Z)((0,we.Z)().mark((function e(){var n,t,o,a;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ke.hT)();case 2:n=e.sent,t=n.error,o=n.success,null===t&&o&&(console.log(o),ie.value=(null===(a=o.items)||void 0===a?void 0:a.length)||0);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=(0,f.iH)(0),ce=((0,f.iH)(!1),function(){var e=(0,be.Z)((0,we.Z)().mark((function e(){var n,t,o,a,r,l;return(0,we.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=Z.value.filter((function(e){return e.checkbox})),console.log(n,"~~~"),t=n.map((function(e){return[e.id,e.count]})),!(t.length>0)){e.next=26;break}return o="["+t.map((function(e){return"("+e.join(",")+")"})).join(",")+"]",console.log(o),e.next=8,(0,ke.tB)({product_ids:o});case 8:if(a=e.sent,r=a.error,l=a.success,null!==r){e.next=21;break}return Z.value.forEach((function(e){e.checkbox=!1})),B(),j(),D().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),e.next=18,le();case 18:console.log("\u5df2\u53d6\u6d88\u6240\u6709\u4ea7\u54c1\u7684\u9009\u4e2d\u72b6\u6001\uff0c\u5df2\u66f4\u65b0\u5168\u9009\u72b6\u6001\u548c\u4ef7\u683c"),e.next=22;break;case 21:D().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"error",duration:2e3});case 22:console.log("~~~",r,l),console.log(t),e.next=27;break;case 26:D().showToast({title:"\u8fd8\u672a\u9009\u8d2d\u5546\u54c1",icon:"error",duration:2e3});case 27:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()),ue=((0,f.iH)(""),(0,f.qj)({showPreview:!1,imgData:[]})),se=function(e){ue.showPreview=!0,ue.imgData=[{src:e}]};(0,f.iH)(""),(0,f.iH)("");return function(e,p){var b=he.Z,_=me.Z,T=ge.Z,D=fe,H=ae.Z,B=oe.Z,L=te.Z,A=ne,J=O.Z,$=S,G=u.Z,le=c.Z,pe=i.Z,de=l.Z,ve=r.Z,we=a.Z;return(0,d.wg)(),(0,d.j4)(we,{style:{"background-color":"#f7f7f7"}},{default:(0,d.w5)((function(){return[(0,d.Wm)(_,{top:"0",class:"search-sticky"},{default:(0,d.w5)((function(){return[(0,d._)("div",Ze,[(0,d.Wm)(b,{modelValue:k.value,"onUpdate:modelValue":p[1]||(p[1]=function(e){return k.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u4ea7\u54c1\u578b\u53f7/\u540d\u79f0/\u89c4\u683c/\u6599\u53f7",shape:"round","input-background":"#f5f5f5",class:"custom-searchbar"},{rightout:(0,d.w5)((function(){return[(0,d._)("div",{class:"search-btn",onClick:p[0]||(p[0]=function(){h.value=1,Z.value.length=0,y(k.value)})},p[9]||(p[9]=[(0,d._)("i",{class:"i-bx-search mr-1"},null,-1),(0,d.Uk)(" \u67e5\u8be2 ")]))]})),rightin:(0,d.w5)((function(){return[(0,d.Wm)((0,f.SU)(m.LP))]})),_:1},8,["modelValue"])])]})),_:1}),(0,d._)("div",Pe,[(0,d.wy)((0,d.Wm)(T,{type:"left",position:{top:"140px"},onClick:q,class:"back-to-top"},{btn:(0,d.w5)((function(){return p[10]||(p[10]=[(0,d._)("div",{class:"back-btn"},[(0,d._)("i",{class:"i-bx-up-arrow-alt"}),(0,d._)("span",{class:"text"},"\u9876\u90e8")],-1)])})),_:1},512),[[g.F8,n.value]]),(0,d.Wm)(B,{visible:Y.value,"onUpdate:visible":p[3]||(p[3]=function(e){return Y.value=e}),class:"myAction"},{default:(0,d.w5)((function(){return[(0,d._)("div",Ce,[(0,d.Wm)(b,{modelValue:R.value,"onUpdate:modelValue":p[2]||(p[2]=function(e){return R.value=e}),shape:"square",label:"\u8bf7\u8f93\u5165\u697c\u5c42\u4ee5\u641c\u7d22","input-background":"#F0F0F0",onChange:ee,id:"pop-target"},{leftin:(0,d.w5)((function(){return[(0,d.Wm)((0,f.SU)(m.LP))]})),_:1},8,["modelValue"]),(0,d._)("div",{class:(0,v.C_)({toShowDiv:!X.value}),style:{width:"80%",height:"300px","background-color":"#FFFFFF",position:"absolute",left:"50%",transform:"translateX(-50%)","z-index":"999"}},[(0,d._)("div",null,[K.value.length>0?((0,d.wg)(),(0,d.iD)("div",Se,[(0,d.Wm)(H,{"list-data":K.value,"container-height":300,onScrollBottom:Q},{default:(0,d.w5)((function(e){var n=e.item;return[(0,d._)("div",{class:"list-item",onClick:function(e){return re(n)}},(0,v.zw)(n.name),9,Oe),(0,d.Wm)(D,{dashed:!0})]})),_:1},8,["list-data"])])):((0,d.wg)(),(0,d.iD)("div",Te,(0,v.zw)("\u8bf7\u8f93\u5165\u76f8\u5e94\u6b63\u697c\u5c42\u7f16\u53f7\u540e\u8fdb\u884c\u67e5\u8be2")))])],2)])]})),_:1},8,["visible"]),((0,d.wg)(),(0,d.iD)("div",De,[(0,d.Wm)(L,{title:"\u63d0\u4ea4\u8ba2\u5355",content:"\u786e\u8ba4\u63d0\u4ea4\u8ba2\u8d27\u4fe1\u606f\u5e76\u751f\u6210\u8ba2\u5355\u5417\uff1f",visible:W.value,"onUpdate:visible":p[4]||(p[4]=function(e){return W.value=e}),onOk:z,class:"custom-dialog"},null,8,["visible"]),Z.value.length>0?((0,d.wg)(),(0,d.iD)("div",je,[(0,d.Wm)(A,{show:ue.showPreview,images:ue.imgData,onClose:p[5]||(p[5]=function(e){return ue.showPreview=!1})},null,8,["show","images"]),(0,d._)("div",He,[(0,d._)("scroll-view",{class:"product-list","scroll-y":!0,onScroll:p[7]||(p[7]=function(){return(0,f.SU)(t)&&(0,f.SU)(t).apply(void 0,arguments)}),onScrolltolower:p[8]||(p[8]=function(){return(0,f.SU)(o)&&(0,f.SU)(o).apply(void 0,arguments)}),onScrolltoupper:V,ref_key:"scrollContainer",ref:E,"lower-threshold":100,"upper-threshold":50,"enable-back-to-top":!1,"scroll-top":I.value,"scroll-with-animation":!0},[((0,d.wg)(!0),(0,d.iD)(d.HY,null,(0,d.Ko)(Z.value,(function(e,n){return(0,d.wg)(),(0,d.iD)("div",{key:e.id,class:"product-item"},[(0,d._)("div",Be,[(0,d._)("div",Fe,[(0,d.Wm)(J,{onChange:function(n){return U(n,e)},modelValue:e.checkbox,"onUpdate:modelValue":function(n){return e.checkbox=n},"icon-size":"25"},null,8,["onChange","modelValue","onUpdate:modelValue"])]),(0,d._)("div",Ne,[(0,d._)("div",We,[(0,d._)("image",{src:e.image,class:"product-image",onLongpress:function(n){return se(e.image)}},null,40,ze),e.image?(0,d.kq)("",!0):((0,d.wg)(),(0,d.iD)("div",Ie,p[11]||(p[11]=[(0,d._)("i",{class:"i-bx-image-alt"},null,-1)])))])]),(0,d._)("div",Ee,[(0,d._)("div",Ve,[(0,d._)("div",qe,[p[12]||(p[12]=(0,d._)("i",{class:"i-bx-crown"},null,-1)),(0,d._)("span",null,(0,v.zw)(e.model),1)]),(0,d._)("div",Le,(0,v.zw)(e.name.split("/")[0]),1),(0,d.wy)((0,d.Wm)($,{color:"rgba(18, 47, 56, 0.1)","text-color":"#122F38",class:"sub-name-tag"},{default:(0,d.w5)((function(){return[(0,d.Uk)((0,v.zw)(e.name.split("/")[1]),1)]})),_:2},1536),[[g.F8,e.name.split("/")[1]]])]),(0,d._)("div",Me,[(0,d.Wm)($,{color:"#f8f9fa","text-color":"#666",class:"spec-tag"},{default:(0,d.w5)((function(){return[(0,d.Uk)((0,v.zw)(e.spec),1)]})),_:2},1024)]),(0,d._)("div",Ye,[(0,d._)("div",Ae,[p[13]||(p[13]=(0,d._)("span",{class:"currency"},"\xa5",-1)),(0,d._)("span",Re,(0,v.zw)(e.price_unit),1),p[14]||(p[14]=(0,d._)("span",{class:"unit"},"/PCS",-1))]),(0,d._)("div",Xe,[(0,d.Wm)($,{color:"#FF6B35","text-color":"#fff",class:"code-tag"},{default:(0,d.w5)((function(){return[(0,d.Uk)((0,v.zw)(e.code),1)]})),_:2},1024)])]),(0,d.Wm)(G,{design:e.design,id:e.id,index:n,"onUpdate:design":M,class:"noShow"},null,8,["design","id","index"])]),(0,d._)("div",Ke,[(0,d.Wm)(le,{modelValue:e.count,"onUpdate:modelValue":function(n){return e.count=n},min:1,max:2e5,"input-width":"55",onBlur:p[6]||(p[6]=function(e){return j()}),onAdd:function(){return N(e.id)},onReduce:function(){return F(e.id)},class:"custom-input-number"},null,8,["modelValue","onUpdate:modelValue","onAdd","onReduce"])])])])})),128)),s.value&&Z.value.length>0?((0,d.wg)(),(0,d.iD)("div",Je,p[15]||(p[15]=[(0,d._)("div",{class:"loading-spinner"},null,-1),(0,d._)("span",{class:"loading-text"},"\u52a0\u8f7d\u4e2d...",-1)]))):(0,d.kq)("",!0),!s.value&&h.value>=w.value&&Z.value.length>0?((0,d.wg)(),(0,d.iD)("div",$e,p[16]||(p[16]=[(0,d._)("span",null,"\u5df2\u52a0\u8f7d\u5168\u90e8\u6570\u636e",-1)]))):(0,d.kq)("",!0)],40,Ue)])])):((0,d.wg)(),(0,d.iD)("div",Ge,[(0,d.Wm)(pe,{description:"\u6682\u65e0\u6570\u636e",class:"custom-empty"})]))]))]),p[21]||(p[21]=(0,d._)("div",{class:"user-button-text"},[(0,d._)("span",null,(0,v.zw)("\u6280\u672f\u652f\u6301 \xa9 \u5e7f\u4e1c\u5de6\u5411\u79d1\u6280\u6709\u9650\u516c\u53f8"))],-1)),(0,d._)("div",Qe,[(0,d._)("div",en,[(0,d._)("div",nn,[(0,d._)("div",{class:"cart-section",onClick:x},[(0,d.Wm)(de,{value:ie.value,class:"cart-badge"},{default:(0,d.w5)((function(){return p[17]||(p[17]=[(0,d._)("div",{class:"cart-icon-wrapper"},[(0,d._)("i",{class:"i-bx-cart-alt"}),(0,d._)("span",{class:"cart-text"},"\u8d2d\u7269\u8f66")],-1)])})),_:1},8,["value"])])]),(0,d._)("div",tn,[p[18]||(p[18]=(0,d._)("span",{class:"total-label"},"\u5408\u8ba1:",-1)),(0,d._)("span",on,"\xa5"+(0,v.zw)(P.value.toFixed(2)),1)]),(0,d._)("div",an,[(0,d.Wm)(ve,{class:"add-cart-btn",onClick:ce,disabled:0===Z.value.filter((function(e){return e.checkbox})).length},{default:(0,d.w5)((function(){return p[19]||(p[19]=[(0,d._)("i",{class:"i-bx-cart-add"},null,-1),(0,d._)("span",null,"\u52a0\u5165\u8d2d\u7269\u8f66",-1)])})),_:1},8,["disabled"]),(0,d.Wm)(ve,{class:"buy-now-btn",type:"primary",onClick:C,disabled:0===Z.value.length||0===Z.value.filter((function(e){return e.checkbox})).length},{default:(0,d.w5)((function(){return p[20]||(p[20]=[(0,d.Uk)(" \u7acb\u5373\u8d2d\u4e70 ")])})),_:1},8,["disabled"])])])])]})),_:1})}}});const ln=rn;var cn=ln,un={navigationBarTitleText:"\u4ea7\u54c1\u8be6\u60c5",enablePullDownRefresh:!0,onReachBottomDistance:250};Page((0,o.createPageConfig)(cn,"package/package-a/Details/index",{root:{cn:[]}},un||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(404)}));e.O()}]);