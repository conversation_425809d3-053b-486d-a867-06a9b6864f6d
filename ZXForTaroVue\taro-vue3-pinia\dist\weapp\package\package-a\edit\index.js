"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[185],{8331:function(e,r,n){var a=n(1065),t=n(6944),u=n(889),o=(n(9932),n(5575)),s=(n(3588),n(6538)),l=(n(1808),n(3496)),d=(n(5279),n(2419)),i=n(2810),c=n(3221),p=n(6821),f=n(7011),w=n(5969),m=n(4733),v=n(4081),_=n(1959),g=n.n(_),h=(0,c.aZ)({__name:"index",setup:function(e){var r=(0,v.tN)(),n=function(e){return e===a.value.password?Promise.resolve():Promise.reject("\u4e24\u6b21\u8f93\u5165\u5bc6\u7801\u4e0d\u4e00\u81f4")},a=(0,p.iH)({password:"",repeatPassword:"",old_password:""}),_=(0,p.iH)(null),h=function(){var e=(0,i.Z)((0,d.Z)().mark((function e(){var n;return(0,d.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:null===(n=_.value)||void 0===n||n.validate().then(function(){var e=(0,i.Z)((0,d.Z)().mark((function e(n){var t,u;return(0,d.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=n.valid,n.errors,!t){e.next=7;break}return console.log("success:",t),e.next=5,(0,m.gQ)({userId:r.userInfo.userId,password:a.value.password,old_password:a.value.old_password});case 5:u=e.sent,null===u.error&&g().showToast({title:"\u4fee\u6539\u6210\u529f",icon:"success",duration:2e3});case 7:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,r){var d=l.Z,i=s.Z,m=o.Z,v=u.Z,g=t.Z;return(0,c.wg)(),(0,c.j4)(g,{left:""},{default:(0,c.w5)((function(){return[(0,c.Wm)(m,{class:"m-3","model-value":a.value,ref_key:"updateFormRef",ref:_},{default:(0,c.w5)((function(){return[(0,c.Wm)(i,{prop:"password",required:"",rules:[{required:!0,message:"\u8bf7\u8f93\u5165\u65b0\u5bc6\u7801"}]},{default:(0,c.w5)((function(){return[(0,c.Wm)(d,{modelValue:a.value.password,"onUpdate:modelValue":r[0]||(r[0]=function(e){return a.value.password=e}),placeholder:"\u8bf7\u8f93\u5165\u65b0\u5bc6\u7801",type:"password"},{left:(0,c.w5)((function(){return[(0,c.Wm)((0,p.SU)(w.DD))]})),_:1},8,["modelValue"])]})),_:1}),(0,c.Wm)(i,{prop:"repeatPassword",required:"",rules:[{required:!0,message:"\u8bf7\u518d\u6b21\u8f93\u5165\u8fdb\u884c\u786e\u8ba4"},{message:"\u4e24\u6b21\u8f93\u5165\u5bc6\u7801\u4e0d\u4e00\u81f4",validator:n}]},{default:(0,c.w5)((function(){return[(0,c.Wm)(d,{modelValue:a.value.repeatPassword,"onUpdate:modelValue":r[1]||(r[1]=function(e){return a.value.repeatPassword=e}),placeholder:"\u8bf7\u518d\u6b21\u8f93\u5165\u8fdb\u884c\u786e\u8ba4",type:"password"},{left:(0,c.w5)((function(){return[(0,c.Wm)((0,p.SU)(w.DD))]})),_:1},8,["modelValue"])]})),_:1},8,["rules"]),(0,c.Wm)(i,{prop:"old_password",rules:[{required:!0,message:"\u8f93\u5165\u65e7\u5bc6\u7801"}]},{default:(0,c.w5)((function(){return[(0,c.Wm)(d,{modelValue:a.value.old_password,"onUpdate:modelValue":r[2]||(r[2]=function(e){return a.value.old_password=e}),placeholder:"\u8bf7\u8f93\u5165\u65e7\u5bc6\u7801",type:"password",onBlur:r[3]||(r[3]=function(){})},{left:(0,c.w5)((function(){return[(0,c.Wm)((0,p.SU)(w.sg))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["model-value"]),(0,c.Wm)(v,{type:"primary",onClick:h,class:"m-3",size:"large"},{default:(0,c.w5)((function(){return r[4]||(r[4]=[(0,c.Uk)((0,f.zw)("\u63d0\u4ea4"))])})),_:1})]})),_:1})}}});const Z=h;var W=Z,k={navigationBarTitleText:"\u4fee\u6539\u5bc6\u7801"};Page((0,a.createPageConfig)(W,"package/package-a/edit/index",{root:{cn:[]}},k||{}))}},function(e){var r=function(r){return e(e.s=r)};e.O(0,[107,216,592],(function(){return r(8331)}));e.O()}]);