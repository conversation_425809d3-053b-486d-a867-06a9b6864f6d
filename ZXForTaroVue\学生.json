{"msg": "操作成功", "code": 200, "permissions": ["system:user:resetPwd", "system:post:list", "monitor:operlog:export", "system:menu:query", "system:dept:remove", "system:menu:list", "system:dict:edit", "monitor:logininfor:remove", "system:user:query", "system:plans:list", "system:user:add", "system:notice:remove", "system:plans:edit", "system:user:export", "system:role:remove", "system:plans:import", "system:dept:query", "system:dict:list", "system:plans:add", "system:notice:list", "system:dict:query", "system:notice:query", "system:notice:edit", "system:plans:query", "system:post:edit", "monitor:logininfor:list", "system:dict:export", "system:post:query", "system:post:remove", "system:config:edit", "system:user:remove", "system:config:list", "system:menu:add", "system:role:list", "system:user:import", "system:dict:remove", "system:user:edit", "system:post:export", "system:config:export", "system:role:edit", "system:dept:list", "system:config:query", "system:plans:export", "monitor:operlog:remove", "monitor:operlog:list", "system:role:add", "system:menu:remove", "system:dict:add", "monitor:logininfor:query", "monitor:logininfor:export", "system:plans:remove", "system:dept:edit", "system:post:add", "monitor:operlog:query", "system:user:list", "system:notice:add", "system:role:export", "system:config:add", "monitor:logininfor:unlock", "system:role:query", "system:menu:edit", "system:dept:add", "system:config:remove"], "roles": ["student"], "user": {"createBy": "admin", "createTime": "2025-03-23 06:06:50", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "userId": 523, "deptId": 212, "userName": "刘丽", "nickName": "刘丽", "email": null, "userType": "1", "phonenumber": null, "sex": "0", "avatar": null, "password": "$2a$10$tGs1vSBTmJewfKp/wptGyepgCt2zCQKhvTOp.27BgxoOWR3LDnbS6", "status": "0", "delFlag": "0", "loginIp": "************", "loginDate": "2025-04-12T19:22:48.000+08:00", "dept": {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "deptId": 212, "parentId": 207, "ancestors": "0,100,207", "deptName": "王敏政数英", "orderNum": 1, "leader": null, "phone": null, "email": null, "status": "0", "delFlag": null, "parentName": null, "children": []}, "roles": [{"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "roleId": 102, "roleName": "学生", "roleKey": "student", "roleSort": 0, "dataScope": "1", "menuCheckStrictly": false, "deptCheckStrictly": false, "status": "0", "delFlag": null, "flag": false, "menuIds": null, "deptIds": null, "permissions": ["system:user:resetPwd", "system:post:list", "monitor:operlog:export", "system:menu:query", "system:dept:remove", "system:menu:list", "system:dict:edit", "monitor:logininfor:remove", "system:user:query", "system:plans:list", "system:user:add", "system:notice:remove", "system:plans:edit", "system:user:export", "system:role:remove", "system:plans:import", "system:dept:query", "system:dict:list", "system:plans:add", "system:notice:list", "system:dict:query", "system:notice:query", "system:notice:edit", "system:plans:query", "system:post:edit", "monitor:logininfor:list", "system:dict:export", "system:post:query", "system:post:remove", "system:config:edit", "system:user:remove", "system:config:list", "system:menu:add", "system:role:list", "system:user:import", "system:dict:remove", "system:user:edit", "system:post:export", "system:config:export", "system:role:edit", "system:dept:list", "system:config:query", "system:plans:export", "monitor:operlog:remove", "monitor:operlog:list", "system:role:add", "system:menu:remove", "system:dict:add", "monitor:logininfor:query", "monitor:logininfor:export", "system:plans:remove", "system:dept:edit", "system:post:add", "monitor:operlog:query", "system:user:list", "system:notice:add", "system:role:export", "system:config:add", "monitor:logininfor:unlock", "system:role:query", "system:menu:edit", "system:dept:add", "system:config:remove"], "admin": false}], "roleIds": null, "postIds": null, "roleId": null, "admin": false}}