<script setup lang="ts">
import { ref, reactive, Ref, onMounted } from 'vue';
import { Current, getCurrentPages, useRouter } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { Location, IconFont } from '@nutui/icons-vue-taro'
import { addAddress, updateAddress } from '@/service/index'
import { useAuthStore } from '@/store/index';
// import AddressParse from './dist/zh-address-parse.min.js'
import AddressParse from '@/package/package-a/harvestAddress/dist/zh-address-parse.min.js'

interface IaddressformData {
    testaddressName: string,
    phone: string,
    defaultAddress: false,
    fullAddress: string
    testid: string | number | null
}
const auth = useAuthStore()
//路由跳转预加载数据
const preloadData: Record<any, any> | undefined = Taro.getCurrentInstance().preloadData
const state = ref(new Map([
    ['add', '添加'],
    ['update', '编辑']
]))
// options为可选参数，不传默认使用正则查找
const options = {
    type: 0, // 哪种方式解析，0：正则，1：树查找
    textFilter: [], // 预清洗的字段
    nameMaxLength: 4, // 查找最大的中文名字长度
}
const parseValue = ref('')



// console.log(parseResult.value, 'parseResult');


onMounted(() => {
    if (router.params.state === 'update') {
        addressformData.value = {
            testaddressName: preloadData?.item.addressName,
            phone: preloadData?.item.phone,
            defaultAddress: preloadData?.item.defaultAddress,
            fullAddress: preloadData?.item.fullAddress,
            testid: preloadData?.item.id

        }

    }

})
//地区
const selector: Ref<Array<string>> = ref([])
//地区选择器事件
// 选中地区
const selectorChecked: Ref<String> = ref('')
const onChange = (e: { detail: { value: String; }; }) => {
    console.log(e);
    selectorChecked.value = e.detail.value

}
const router = useRouter()
const isdefaultAddress = ref(false)

//调用进行地图定位
const getMap = () => {
    Taro.getLocation({
        type: 'wgs84',
        success: function (res) {
            console.log(res);
            Taro.showToast({
                title: res.errMsg,
                icon: 'success',
                duration: 2000
            })
            const latitude = res.latitude
            const longitude = res.longitude
            const speed = res.speed
            const accuracy = res.accuracy
        },
        fail: function (res) {
            Taro.showToast({
                title: res.errMsg,
                icon: 'none',
                duration: 2000
            })
        }
    })

}
const addressformData: Ref<IaddressformData> = ref({
    testaddressName: '',
    phone: '',
    defaultAddress: false,
    fullAddress: '',
    testid: null
})
//
const setMessage = () => {
    Taro.setStorageSync('addressformData', addressformData.value)
}
//保存事件
const saveButton = async () => {
    console.log(addressformData.value);
    formRef.value?.validate().then(async ({ valid, errors }) => {
        if (valid) {

            if (router.params.state === 'add') {
                const res = await addAddress({ name: addressformData.value.testaddressName, phone: addressformData.value.phone, address: addressformData.value.fullAddress })
                if (res.error == null) {
                    Taro.showToast({
                        title: '添加成功',
                        icon: 'success',
                        duration: 2000,
                        success: () => {
                            setTimeout(() => {
                                Taro.navigateBack({
                                    delta: 1
                                })
                            }, 2000);
                        }
                    })
                }
                //清空表单
                addressformData.value = {
                    testaddressName: '',
                    phone: '',
                    defaultAddress: false,
                    fullAddress: '',
                    testid: null
                }



            }
            if (router.params.state === 'update') {
                const res = await updateAddress({ name: addressformData.value.testaddressName, phone: addressformData.value.phone, address: addressformData.value.fullAddress }, { id: addressformData.value.testid as string })

                if (res.error == null) {
                    // const pages = getCurrentPages()
                    // const current = pages[pages.length - 1]
                    // const eventChannel = current.getOpenerEventChannel()
                    // eventChannel.emit('updateEvent', addressformData.value);
                    Taro.showToast({
                        title: '修改成功',
                        icon: 'success',
                        duration: 2000,
                        success: () => {
                            setTimeout(() => {
                                Taro.navigateBack({
                                    delta: 1
                                })

                            }, 2000)
                        }
                    })
                }

            }
        } else {
            console.warn('error:', errors)
        }
    })

}
console.log(state.value.get(router.params.state as string), '路由传值');

const copyButtonClick = () => {
    console.log('aaa');
    // Taro.setClipboardData({
    //     data: 'data',
    //     success: function (res) {
    Taro.getClipboardData({
        success: function (res) {
            // console.log(res.data) // data
            parseValue.value = res.data
            // type参数0表示使用正则解析，1表示采用树查找, textFilter地址预清洗过滤字段。
            const parseResult = AddressParse(res.data, options)
            console.log(parseResult, 'parseResult');
            addressformData.value.testaddressName = parseResult.name
            addressformData.value.phone = parseResult.phone
            addressformData.value.fullAddress = parseResult.province + parseResult.city + parseResult.area + parseResult.detail






        }
    })
    //     }
    // })
}
const validatorName = () => {
    if (/^([\u4e00-\u9fa5]{2,6})$/gi.test(addressformData.value.testaddressName)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的姓名后重新提交')
    }
}
const validatorPhone = () => {
    if (/^1[3-9]\d{9}$/.test(addressformData.value.phone)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的11位手机号后重新提交')
    }
}
const validatorAddress = () => {
    if (/^[\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~` （）【】、，]{6,100}$/u.test(addressformData.value.fullAddress)) {
        return Promise.resolve()
    } else {
        return Promise.reject('联系地址应尽量详实')
    }
}
const formRef = ref(null)



definePageConfig({
    navigationBarTitleText: '编辑收货信息'
});
</script>
<template>
    <div class="address-edit-page">
        <basic-layout>
            <!-- 页面标题区域 -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-left">
                        <i class="i-bx-map-pin header-icon"></i>
                        <span class="header-title">{{ state.get(router.params.state as string) }}收货地址</span>
                    </div>
                </div>
            </div>

            <!-- 地址信息表单区域 -->
            <div class="address-form-section">
                <div class="section-header">
                    <i class="i-bx-user section-icon"></i>
                    <span class="section-title">地址信息</span>
                </div>
                <div class="form-content">
                    <nut-form star-position="right" ref="formRef" class="custom-form">
                        <nut-form-item
                            label="联系人"
                            required
                            prop="testaddressName"
                            :rules="[{
                                validator: validatorName,
                                message: '请输入正确的人名'
                            }]"
                            class="custom-form-item"
                        >
                            <div class="input-wrapper">
                                <i class="i-bx-user input-icon"></i>
                                <nut-input
                                    :border="false"
                                    v-model="addressformData.testaddressName"
                                    placeholder="请输入联系人姓名"
                                    type="text"
                                    class="custom-input"
                                />
                            </div>
                        </nut-form-item>

                        <nut-form-item
                            label="手机号"
                            required
                            prop="phone"
                            :rules="[{
                                validator: validatorPhone,
                                message: '请输入正确的手机号'
                            }]"
                            class="custom-form-item"
                        >
                            <div class="input-wrapper">
                                <i class="i-bx-phone input-icon"></i>
                                <nut-input
                                    :border="false"
                                    v-model="addressformData.phone"
                                    placeholder="请输入11位手机号码"
                                    type="number"
                                    class="custom-input"
                                />
                            </div>
                        </nut-form-item>

                        <nut-form-item
                            label="详细地址"
                            required
                            prop="fullAddress"
                            :rules="[{
                                validator: validatorAddress,
                                message: '地址应尽量详细'
                            }]"
                            class="custom-form-item address-item"
                        >
                            <div class="textarea-wrapper">
                                <nut-textarea
                                    v-model="addressformData.fullAddress"
                                    placeholder="请输入详细地址：省市区/县、街道、小区、楼栋号、房间号等"
                                    type="text"
                                    class="custom-textarea"
                                    limit-show
                                    :max-length="100"
                                    style="height: 175px !important"
                                />
                            </div>
                        </nut-form-item>
                    </nut-form>
                </div>
            </div>
            <!-- 地址粘贴板区域 -->
            <div class="paste-section">
                <div class="section-header">
                    <i class="i-bx-clipboard section-icon"></i>
                    <span class="section-title">智能识别</span>
                    <span class="section-subtitle">一键粘贴快速填写</span>
                </div>
                <div class="paste-content">
                    <div class="paste-area">
                        <div class="paste-input-wrapper">
                            <nut-textarea
                                v-model="parseValue"
                                placeholder="试试一键粘贴收件人姓名/手机号/收货地址，可快速识别您的收货地址"
                                type="text"
                                class="paste-textarea"
                                disabled
                                :rows="3"
                            />
                            <nut-button
                                size="small"
                                type="primary"
                                class="paste-btn"
                                @click="copyButtonClick()"
                            >
                                <i class="i-bx-paste"></i>
                                <span>粘贴识别</span>
                            </nut-button>
                        </div>
                        <div class="paste-tip">
                            <i class="i-bx-info-circle tip-icon"></i>
                            <span class="tip-text">支持识别常见快递单、购物订单等地址信息</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部间距 -->
            <div class="bottom-spacer"></div>

            <!-- 底部保存按钮 -->
            <div class="bottom-action">
                <div class="action-content">
                    <nut-button
                        @click="saveButton"
                        class="save-btn"
                        size="large"
                        type="primary"
                    >
                        <i class="i-bx-save"></i>
                        <span>保存地址</span>
                    </nut-button>
                </div>
            </div>
        </basic-layout>
    </div>
</template>


<style lang="scss">
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$info-color: #2196F3;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 页面基础样式
.layout-screen {
    background: $background-light !important;
}

.address-edit-page {
    min-height: 100vh;
    background: $background-light;
    padding-bottom: 80px;
}

// 页面标题区域
.page-header {
    background: #fff;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    margin-bottom: 12px;

    .header-content {
        padding: 16px;

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .header-icon {
                font-size: 20px;
                color: $primary-color;
            }

            .header-title {
                font-size: 18px;
                font-weight: 600;
                color: $text-primary;
            }
        }
    }
}

// 地址信息表单区域
.address-form-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }
    }

    .form-content {
        padding: 20px;

        .custom-form {
            .custom-form-item {
                margin-bottom: 20px;

                &:last-child {
                    margin-bottom: 0;
                }

                &.address-item {
                    margin-bottom: 0;
                }

                // 表单项标签样式 - 移除:deep()语法
                .nut-form-item__label {
                    font-size: 14px !important;
                    font-weight: 600 !important;
                    color: $text-primary !important;
                    margin-bottom: 8px !important;
                    white-space: nowrap !important;
                    min-width: 80px !important;
                    flex-shrink: 0 !important;
                }

                // 表单项整体布局
                .nut-form-item__body {
                    flex: 1 !important;
                    min-width: 0 !important;
                }

                // 输入框包装器
                .input-wrapper {
                    position: relative;
                    display: flex;
                    align-items: center;
                    background: $background-light;
                    border-radius: 8px;
                    border: 1px solid $border-color;
                    transition: all 0.3s ease;
                    width: 100%;
                    min-height: 44px;

                    &:focus-within {
                        border-color: $primary-color;
                        box-shadow: 0 0 0 2px rgba(18, 47, 56, 0.1);
                    }

                    .input-icon {
                        font-size: 16px;
                        color: $text-secondary;
                        margin: 0 12px 0 16px;
                        flex-shrink: 0;
                    }

                    .custom-input {
                        flex: 1;
                        background: transparent !important;
                        border: none !important;
                        min-width: 0;

                        .nut-input-box {
                            background: transparent !important;
                            border: none !important;
                            padding: 12px 16px 12px 0 !important;
                            width: 100% !important;
                        }

                        .input-text {
                            font-size: 14px !important;
                            color: $text-primary !important;
                            width: 100% !important;
                            box-sizing: border-box !important;
                        }

                        .nut-input__placeholder {
                            color: $text-light !important;
                        }
                    }
                }

                // 文本域包装器
                .textarea-wrapper {
                    position: relative;
                    background: $background-light;
                    border-radius: 8px;
                    border: 1px solid $border-color;
                    transition: all 0.3s ease;
                    width: 100%;

                    &:focus-within {
                        border-color: $primary-color;
                        box-shadow: 0 0 0 2px rgba(18, 47, 56, 0.1);
                    }

                    .textarea-icon {
                        position: absolute;
                        top: 16px;
                        left: 16px;
                        font-size: 16px;
                        color: $text-secondary;
                        z-index: 2;
                    }

                    .custom-textarea {
                        background: transparent !important;
                        border: none !important;
                        width: 100%;

                        .nut-textarea {
                            background: transparent !important;
                            border: none !important;
                            padding: 12px 16px 12px 44px !important;
                            width: 100% !important;
                            box-sizing: border-box !important;
                        }

                        .nut-textarea__textarea {
                            font-size: 14px !important;
                            color: $text-primary !important;
                            line-height: 1.5 !important;
                            min-height: 100px !important;
                            width: 100% !important;
                            box-sizing: border-box !important;
                            word-wrap: break-word !important;
                            word-break: break-all !important;
                            resize: none !important;
                        }

                        .nut-textarea__limit {
                            color: $text-light !important;
                            font-size: 12px !important;
                            text-align: right !important;
                            padding: 4px 8px !important;
                        }
                    }
                }
            }
        }
    }
}

// 地址粘贴板区域
.paste-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $info-color 0%, darken($info-color, 10%) 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .section-subtitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: auto;
        }
    }

    .paste-content {
        padding: 20px;

        .paste-area {
            .paste-input-wrapper {
                position: relative;
                background: $background-light;
                border-radius: 8px;
                border: 1px solid $border-color;
                padding: 16px 80px 16px 16px; // 为按钮留出空间
                margin-bottom: 12px;

                .paste-textarea {
                    background: transparent !important;
                    border: none !important;
                    width: 100%;

                    .nut-textarea {
                        background: transparent !important;
                        border: none !important;
                        padding: 0 !important;
                        width: 100% !important;
                    }

                    .nut-textarea__textarea {
                        font-size: 14px !important;
                        color: $text-secondary !important;
                        line-height: 1.5 !important;
                        min-height: 80px !important;
                        resize: none !important;
                        width: 100% !important;
                        box-sizing: border-box !important;
                        word-wrap: break-word !important;
                        word-break: break-all !important;
                        padding-right: 0 !important;
                    }

                    .nut-textarea--disabled .nut-textarea__textarea {
                        background: transparent !important;
                        color: $text-light !important;
                    }
                }

                .paste-btn {
                    position: absolute;
                    top: 12px;
                    right: 12px;
                    --nutui-button-primary-background-color: #{$info-color};
                    --nutui-button-primary-border-color: #{$info-color};
                    --nutui-button-border-radius: 16px;
                    --nutui-button-font-size: 12px;
                    --nutui-button-font-weight: 600;
                    padding: 0 12px;
                    height: 32px;
                    display: inline-flex;
                    align-items: center;
                    gap: 4px;
                    white-space: nowrap;
                    flex-shrink: 0;

                    i {
                        font-size: 14px;
                    }

                    &:active {
                        --nutui-button-primary-background-color: #{darken($info-color, 10%)};
                    }
                }
            }

            .paste-tip {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                background: rgba(33, 150, 243, 0.1);
                border-radius: 6px;

                .tip-icon {
                    font-size: 14px;
                    color: $info-color;
                    flex-shrink: 0;
                }

                .tip-text {
                    font-size: 12px;
                    color: $info-color;
                    line-height: 1.4;
                }
            }
        }
    }
}

// 底部操作区域
.bottom-spacer {
    height: 80px;
}

.bottom-action {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid $border-color;
    box-shadow: 0 -2px 12px rgba(18, 47, 56, 0.08);
    z-index: 999;

    .action-content {
        padding: 16px;
        padding-bottom: calc(16px + env(safe-area-inset-bottom));

        .save-btn {
            --nutui-button-primary-background-color: #{$primary-color};
            --nutui-button-primary-border-color: #{$primary-color};
            --nutui-button-border-radius: 24px;
            --nutui-button-font-weight: 600;
            --nutui-button-font-size: 16px;
            width: 100%;
            height: 48px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            i {
                font-size: 18px;
            }

            &:not(:disabled):active {
                --nutui-button-primary-background-color: #{$primary-dark};
                transform: scale(0.98);
            }
        }
    }
}

// 表单验证错误样式覆盖 - 移除:deep()语法
.nut-form-item--error {
    .input-wrapper,
    .textarea-wrapper {
        border-color: $error-color !important;
        box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1) !important;
    }
}

.nut-form-item__body__tips {
    color: $error-color !important;
    font-size: 12px !important;
    margin-top: 4px !important;
}

// 响应式设计
@media (max-width: 375px) {
    .address-edit-page {
        padding-bottom: 70px;
    }

    .page-header {
        .header-content {
            padding: 12px;

            .header-left {
                .header-title {
                    font-size: 16px;
                }
            }
        }
    }

    .address-form-section,
    .paste-section {
        margin: 8px;
        border-radius: 8px;

        .section-header {
            padding: 12px;

            .section-title {
                font-size: 14px;
            }

            .section-subtitle {
                font-size: 11px;
            }
        }
    }

    .form-content,
    .paste-content {
        padding: 16px !important;

        .custom-form-item {
            margin-bottom: 16px !important;

            .nut-form-item__label {
                font-size: 13px !important;
                min-width: 70px !important;
            }

            .input-wrapper {
                min-height: 40px;

                .input-icon {
                    margin: 0 10px 0 12px;
                    font-size: 14px;
                }

                .custom-input {
                    .nut-input-box {
                        padding: 10px 12px 10px 0 !important;
                    }

                    .input-text {
                        font-size: 13px !important;
                    }
                }
            }

            .textarea-wrapper {
                .textarea-icon {
                    top: 12px;
                    left: 12px;
                    font-size: 14px;
                }

                .custom-textarea {
                    .nut-textarea {
                        padding: 10px 12px 10px 36px !important;
                    }

                    .nut-textarea__textarea {
                        font-size: 13px !important;
                        min-height: 80px !important;
                    }
                }
            }
        }
    }

    .paste-section {
        .paste-content {
            .paste-area {
                .paste-input-wrapper {
                    padding: 12px 70px 12px 12px;

                    .paste-textarea {
                        .nut-textarea__textarea {
                            font-size: 13px !important;
                            min-height: 60px !important;
                        }
                    }

                    .paste-btn {
                        top: 8px;
                        right: 8px;
                        height: 28px;
                        padding: 0 8px;

                        span {
                            display: none; // 小屏幕只显示图标
                        }
                    }
                }

                .paste-tip {
                    padding: 6px 10px;

                    .tip-text {
                        font-size: 11px;
                    }
                }
            }
        }
    }

    .bottom-action .action-content {
        padding: 12px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));

        .save-btn {
            height: 44px;
            font-size: 14px;
        }
    }
}

// 超小屏幕优化
@media (max-width: 320px) {
    .address-form-section,
    .paste-section {
        margin: 4px;

        .section-header {
            padding: 10px;

            .section-title {
                font-size: 13px;
            }
        }
    }

    .form-content,
    .paste-content {
        padding: 12px !important;
    }

    .custom-form-item {
        .nut-form-item__label {
            font-size: 12px !important;
            min-width: 60px !important;
        }

        .input-wrapper,
        .textarea-wrapper {
            .input-icon,
            .textarea-icon {
                font-size: 13px;
            }
        }
    }
}
</style>