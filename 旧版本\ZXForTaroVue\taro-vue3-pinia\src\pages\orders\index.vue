<template>
    <basic-layout show-tab-bar>
        <!-- <custom-navbar title="订单" /> -->
        <nut-tabs v-model="tabValue" swipeable background="#f7f8fa" @change="change">
            <nut-tab-pane title="开立中" pane-key="draft">

                <orders :values="orders"></orders>
            </nut-tab-pane>
            <nut-tab-pane title="核准中" pane-key="sent">
                <orders :values="orders"></orders>
            </nut-tab-pane>
            <nut-tab-pane title="已审核" pane-key="done">
                <orders :values="orders"></orders>
            </nut-tab-pane>
        </nut-tabs>

    </basic-layout>
</template>

<script setup>
import { onBeforeMount, onMounted, reactive, ref, onUnmounted } from 'vue';
import Orders from "./orders.vue";
import Taro, { useReachBottom, getCurrentInstance, eventCenter } from '@tarojs/taro';
import { getOrder } from '@/service/index'
import { useAuthStore } from '@/store/index';
/** 设置页面属性 */
definePageConfig({
    navigationBarTitleText: '订单',
    onReachBottomDistance: 50


});
const change = async ({ title, paneKey }) => {
    console.log('----', title, paneKey);
    //每次点击change重置page页码为1,数组清空
    orderPage.value = 1
    orders.value = []
    Psize.value = 1
    
    await getOrderF(paneKey, orderPage.value)

}
const auth = useAuthStore()
const getOrderF = async (state, page, per_page = 10, name) => {
    Taro.showLoading({
        title: '加载中'
    })
    if (orderPage.value <= Psize.value && orders.value.length % per_page == 0) {
        console.log(orders.value.length, per_page);

        const { error, success } = await getOrder({ state, page })
        console.log("----------", error);
        // console.log("==========",success.items);
        if (error == null) {
            orders.value.push(...success.items)
            Psize.value = success.psize
            orderPage.value < success.psize ? orderPage.value = success.cur_page + 1 : orderPage.value = success.cur_page
            Taro.hideLoading()
        } else {
            Taro.showToast({
                title: error,
                icon: 'error',
                duration: 2000
            })
        }
    } else {
        Taro.hideLoading()
        Taro.showToast({
            title: '没有更多订单了',
            icon: 'none',
            duration: 2000
        })
    }
}
//上拉触底加载更多
useReachBottom(async () => {
    await getOrderF(tabValue.value, orderPage.value)
    console.log('a');


})
//当前页码
const orderPage = ref(1)
//总分页数
const Psize = ref(1)
//每次拿到的订单数据
const ordersList = ref([

])

const orders = ref([

])
// status description
// 待发货 1     已完成 3
let ongoingOrders = ref([])
let unEvaluateOrders = ref([])
let afterSaleOrRefundOrders = ref([])
const weightSet = reactive(new Map([
    ['weight1', '1920px'],
    ['weight2', '1280px']
]))
onMounted(() => {
    // cartCommodities.value.push(...preloadMessage!.message)
    eventCenter.on(getCurrentInstance().router.onShow, async () => {
        orderPage.value = 1
        orders.value = []
        Psize.value = 1
        tabValue.value='draft'
        getOrderF('draft', orderPage.value)

    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router.onShow)
})

const tabValue = ref('draft')

</script>

<style>
.nut-tab-pane {
    background-color: #f7f8fa;
    padding: 20px 20px;
}
</style>