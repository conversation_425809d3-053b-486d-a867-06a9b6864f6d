<script setup>
import { onBeforeMount, ref } from "vue";
import { updateOrder } from '@/service/index'
import Taro, { showToast } from '@tarojs/taro';
// props
const props = defineProps({
    values: {
        type: Array,
        required: true
    },
    type: String
});

// 修订订单状态事件
const updateOrderState = async (order, state, index) => {
    const { error, success, msg, code } = await updateOrder({
        state: state,
    }, {
        id: order
    })
    console.log('success---!!!', error, success, msg, code);
    if (error == null) {
        if (code == 15) {
            Taro.showModal({
                title: 提示,
                content: msg,
                duration: 2000
            })

        } else if (code == 0) {
            Taro.showToast({
                title: '状态修改成功',
                icon: 'success',
                duration: 2000

            });
            console.log(props.values, state);

            // props.values.state = state
            isModify.value = order
            props.values.splice(index, 1)
        } else {
            Taro.showToast({
                title: msg,
                duration: 2000

            });
        }


    } else {
        Taro.showToast({
            title: msg,
            icon: 'none',
            duration: 4000
        });
    }


};
//订单状态是否修改过
const isModify = ref(null)
//修改订单信息
const updateOrderMessage = async (order, state = null) => {
    Taro.preload({ message: order, state })
    Taro.navigateTo({
        url: `/package/package-a/updateOrder/index?id=${order}&state=${state}`,
    })

}
const stateMap = ref(new Map([
    ['draft', '订单开立'],
    ['sent', '核准成功'],
    ['done', '审核通过']
]))
const oncancelOrderOk = () => {
    console.log('取消订单,id为:', cancelOrderId.value);
    //取消订单接口
}
const visibleCancelOrder = ref(false);
const cancelOrderId = ref(0)
// mounted
onBeforeMount(() => {
    console.log(props.values);
    // console.log(props.values)
});
const onLongpress=(order)=>{
    console.log(order);
    
}

</script>

<template>
    <!-- <nut-dialog title="取消订单" content="确认取消订单吗？" v-model:visible="visibleCancelOrder" @cancel="onCancel"
        @ok="oncancelOrderOk" /> -->
    <view v-if="values.length === 0">
        <nut-empty image="empty" description="暂无相关订单！"></nut-empty>
    </view>
    <!-- <scroll-view v-else :scroll-y="true" :scroll-x="false" class="w-full scroll-h"> -->
    <template v-for="(order, index) in values" :key="order.id">
        <transition name="fade">

            <view class="bg-white mb-2  p-2 rounded shadow" style="animationDuration: 1.5s" @longpress="onLongpress(order)">
                <view className="flex flex-col gap-2 pl-2 pr-2">
                    <view>
                        <view className="flex flex-row justify-between align-top">
                            <view className="flex min-w-0 gap-1">
                                <view className="  font-light " style="width: 60vw;">
                                    <view class="flex flex-wrap pt-2 pb-2 flex-col">
                                        <view class="comodity-title"><span class="font-bold">{{ 'U9订单号:' }}</span> {{
                                            order.u9c_no }}</view>
                                        <view class="comodity-unit   mt-1 mb-1"><span class="font-bold">{{ 'ODOO订单号:'
                                                }}</span> {{
                                                    order.order_no }}</view>
                                        <view class="comodity-unit  mt-1 mb-1"><span class="font-bold">{{ '下单日期:'
                                                }}</span>{{ order.order_date }}</view>
                                        <view class="comodity-unit  mt-1 mb-1"><span class="font-bold">{{ '核准日期:'
                                                }}</span>{{ order.date_approve }}</view>
                                        <view class="comodity-unit  mt-1 mb-1"><span class="font-bold">{{ '交期:'
                                                }}</span>{{ order.date_planned }}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="font-bold pt-2 color-#FF7043">

                                {{ stateMap.get(order.state) }}</view>
                        </view>
                        <!-- <view
                            className="border-y border-x-0 border-b-0  color-#A9A9A9 border-neutral-300 border-dashed text-neutral-500 text-sm flex flex-justify-between mt-1 mb-1 pt-1 pb-1">
                            <span className="text-black inline font-bold text-right color-#424242">供应商:<span
                                    class="font-size-12px">{{
                                        order.customer }}</span></span>
                        </view>
                        <view
                            className="border-y border-x-0  color-#A9A9A9 border-neutral-300  text-neutral-500 text-sm flex flex-justify-between mt-1 mb-1 pt-1 pb-1">
                            <span className="text-black inline font-bold text-right color-#424242">买家:<span
                                    class="font-size-12px">{{
                                        order.user }}</span></span>
                        </view> -->
                        <view
                            className="border-y border-x-0  color-#A9A9A9 border-neutral-300  text-neutral-500 text-sm flex flex-justify-between mt-1 mb-1 pt-1 pb-1">
                            <span className="text-black inline font-bold text-left color-#424242">备注:<span
                                    class="font-size-11px">{{
                                        order.notes }}</span></span>
                        </view>
                        <view
                            className="border-y border-x-0  color-#A9A9A9 border-neutral-300  text-neutral-500 text-sm flex flex-justify-between mt-1 mb-1 pt-1 pb-1">
                            <span className="text-black inline font-bold text-left color-#424242">物流单号:<span
                                    class="font-size-11px">{{
                                        order.zx_wl }}</span></span>
                        </view>
                        <view
                            className="border-y border-x-0 border-t-0 color-#A9A9A9 border-neutral-300 border-dashed text-neutral-500 text-sm flex flex-justify-between mt-1 mb-1 pt-1 pb-1">
                            <span className="text-black inline font-bold text-right color-#424242">付款:<span
                                    style="font-size:12px;color:#F36A12">￥</span><span style="color:#F36A12">{{
                                        order.total
                                    }}</span></span>
                        </view>
                        <view class="flex flex-row justify-end gap-2 mr-1">
                            <!-- 功能按钮 -->
                            <nut-button v-if="order.state === 'draft'" class="rounded" plain type="info" shape="square"
                                size="mini" @click="updateOrderMessage(order.id)">{{ '修改订单' }}</nut-button>
                            <nut-button v-if="order.state === 'draft'" class="rounded" plain type="warning"
                                shape="square" size="mini" @click="updateOrderState(order.id, 'done', index)">{{ '审核'
                                }}</nut-button>
                            <nut-button v-if="order.state === 'sent'" class="rounded" plain type="warning"
                                shape="square" size="mini" @click="updateOrderState(order.id, 'draft', index)">{{ '取消'
                                }}</nut-button>
                            <nut-button v-if="order.state === 'sent'" class="rounded" plain type="warning"
                                shape="square" size="mini" @click="updateOrderState(order.id, 'done', index)">{{ '审核'
                                }}</nut-button>
                            <nut-button v-if="order.state === 'sent'" class="rounded" plain type="success"
                                shape="square" size="mini" @click="updateOrderMessage(order.id, 'Done')">{{ '订单详情'
                                }}</nut-button>

                            <nut-button v-if="order.state === 'done'" class="rounded" plain type="warning"
                                shape="square" size="mini" @click="updateOrderState(order.id, 'draft', index)">{{ '弃审'
                                }}</nut-button>
                            <nut-button v-if="order.state === 'done'" class="rounded" plain type="success"
                                shape="square" size="mini" @click="updateOrderMessage(order.id, 'Done')">{{ '订单详情'
                                }}</nut-button>
                        </view>
                    </view>
                </view>
            </view>
        </transition>
    </template>
    <!-- </scroll-view> -->
</template>

<style>
.fade-enter-active,
.fade-leave-active {
    transition: opacity .5s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */
    {
    opacity: 0;
}

.scroll-h {
    height: 89vh;
}

.comodity-title {
    font-size: 1rem
}

.comodity-unit {
    font-size: 0.75rem;
    color: #979797;
}
</style>
