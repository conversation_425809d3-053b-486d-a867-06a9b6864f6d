<template>
    <div class="modern-product-card">
      <!-- 产品图片区域 -->
      <div class="product-image-section">
        <div class="image-container">
          <image
            :src="state.imgUrl || '/static/images/default-product.png'"
            class="product-image"
            mode="aspectFit"
          />
          <!-- 图片加载状态 -->
          <div v-if="!state.imgUrl" class="image-placeholder">
            <i class="i-bx-image placeholder-icon"></i>
          </div>
        </div>
        <!-- 产品标签 -->
        <div class="product-badges">
          <div class="badge self-operated">
            <i class="i-bx-check-circle"></i>
            <span>自营</span>
          </div>
        </div>
      </div>

      <!-- 产品信息区域 -->
      <div class="product-info-section">
        <!-- 产品型号 -->
        <div class="product-header">
          <div class="model-tag">
            <i class="i-bx-cube model-icon"></i>
            <span class="model-text">{{ state.title }}</span>
          </div>
        </div>

        <!-- 产品名称 -->
        <div class="product-name">
          <h3 class="name-text">{{ state.shopDesc }}</h3>
        </div>

        <!-- 产品特性标签 -->
        <div class="product-features">
          <div class="feature-tag quality">
            <i class="i-bx-award"></i>
            <span>品质保证</span>
          </div>
          <div class="feature-tag delivery">
            <i class="i-bx-rocket"></i>
            <span>快速发货</span>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="product-footer">
          <div class="product-status">
            <div class="status-indicator available">
              <i class="i-bx-check-circle"></i>
              <span>现货</span>
            </div>
          </div>
          <div class="view-details">
            <span class="details-text">查看详情</span>
            <i class="i-bx-right-arrow-alt details-arrow"></i>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script setup lang="ts">
  import {defineProps, onMounted, watchEffect} from 'vue';
  const props=defineProps({
    item:{
        type:Object,
        required:true
    }
  })
//   const emit=defineEmits(['click-card'])
//   const clickCard=()=>{
//     emit('click-card',)
//   }
  onMounted(()=>{

  })
 
  import { ref } from 'vue'
  const state = ref({
    imgUrl:'',
    title: '',
    shopDesc: '自营',
  })
  watchEffect(()=>{
    state.value.title=props.item.model,
    state.value.imgUrl=props.item.image
    state.value.shopDesc=props.item.name.split('/')[0]
  })
  </script>
  <style lang="scss">
  // 主色调变量
  $primary-color: #122F38;
  $primary-light: rgba(18, 47, 56, 0.1);
  $primary-dark: #0d252c;
  $accent-color: #FF6B35;
  $success-color: #4CAF50;
  $warning-color: #FF9800;
  $error-color: #F44336;
  $info-color: #2196F3;
  $text-primary: #122F38;
  $text-secondary: #666;
  $text-light: #999;
  $background-light: #f8f9fa;
  $border-color: rgba(18, 47, 56, 0.1);

  // 现代化产品卡片
  .modern-product-card {
    display: flex;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(18, 47, 56, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin: 12px;
    width: calc(100% - 24px);

    // 悬停效果
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 32px rgba(18, 47, 56, 0.15);

      .product-image-section {
        .image-container {
          .product-image {
            transform: scale(1.05);
          }
        }
      }

      .product-info-section {
        .product-footer {
          .view-details {
            .details-arrow {
              transform: translateX(4px);
            }
          }
        }
      }
    }

    // 点击效果
    &:active {
      transform: translateY(-2px);
      box-shadow: 0 6px 24px rgba(18, 47, 56, 0.12);
    }
  }

  // 产品图片区域
  .product-image-section {
    position: relative;
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, $background-light 0%, #fff 100%);

    .image-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;

      .product-image {
        width: 90px;
        height: 90px;
        border-radius: 12px;
        transition: all 0.3s ease;
        object-fit: cover;
      }

      .image-placeholder {
        width: 90px;
        height: 90px;
        background: $primary-light;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-icon {
          font-size: 32px;
          color: $primary-color;
          opacity: 0.6;
        }
      }
    }

    // 产品标签
    .product-badges {
      position: absolute;
      top: 8px;
      left: 8px;

      .badge {
        display: inline-flex;
        align-items: center;
        gap: 3px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;

        &.self-operated {
          background: rgba(76, 175, 80, 0.15);
          color: $success-color;
          border: 1px solid rgba(76, 175, 80, 0.3);

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 产品信息区域
  .product-info-section {
    flex: 1;
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;

    // 产品型号标签
    .product-header {
      margin-bottom: 8px;

      .model-tag {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        background: linear-gradient(135deg, $primary-light 0%, rgba(18, 47, 56, 0.05) 100%);
        color: $primary-color;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        border: 1px solid rgba(18, 47, 56, 0.15);

        .model-icon {
          font-size: 14px;
        }

        .model-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }
      }
    }

    // 产品名称
    .product-name {
      margin-bottom: 12px;

      .name-text {
        font-size: 15px;
        font-weight: 600;
        color: $text-primary;
        line-height: 1.4;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 42px;
      }
    }

    // 产品特性标签
    .product-features {
      display: flex;
      gap: 6px;
      margin-bottom: 12px;
      flex-wrap: wrap;

      .feature-tag {
        display: inline-flex;
        align-items: center;
        gap: 3px;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;

        &.quality {
          background: rgba(255, 107, 53, 0.1);
          color: $accent-color;
          border: 1px solid rgba(255, 107, 53, 0.2);

          i {
            font-size: 11px;
          }
        }

        &.delivery {
          background: rgba(33, 150, 243, 0.1);
          color: $info-color;
          border: 1px solid rgba(33, 150, 243, 0.2);

          i {
            font-size: 11px;
          }
        }
      }
    }

    // 底部操作区域
    .product-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .product-status {
        .status-indicator {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          font-weight: 500;

          &.available {
            color: $success-color;

            i {
              font-size: 12px;
            }
          }
        }
      }

      .view-details {
        display: flex;
        align-items: center;
        gap: 4px;
        color: $primary-color;
        font-size: 12px;
        font-weight: 500;

        .details-text {
          white-space: nowrap;
        }

        .details-arrow {
          font-size: 14px;
          transition: all 0.3s ease;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 375px) {
    .modern-product-card {
      margin: 8px;
      width: calc(100% - 16px);
      border-radius: 12px;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .product-image-section {
      width: 100px;
      height: 100px;

      .image-container {
        .product-image {
          width: 70px;
          height: 70px;
          border-radius: 8px;
        }

        .image-placeholder {
          width: 70px;
          height: 70px;
          border-radius: 8px;

          .placeholder-icon {
            font-size: 24px;
          }
        }
      }

      .product-badges {
        top: 6px;
        left: 6px;

        .badge {
          padding: 3px 6px;
          font-size: 9px;

          &.self-operated {
            i {
              font-size: 10px;
            }
          }
        }
      }
    }

    .product-info-section {
      padding: 12px 16px;

      .product-header {
        margin-bottom: 6px;

        .model-tag {
          padding: 4px 8px;
          font-size: 11px;
          border-radius: 12px;

          .model-icon {
            font-size: 12px;
          }

          .model-text {
            max-width: 80px;
          }
        }
      }

      .product-name {
        margin-bottom: 8px;

        .name-text {
          font-size: 14px;
          min-height: 36px;
        }
      }

      .product-features {
        margin-bottom: 8px;
        gap: 4px;

        .feature-tag {
          padding: 2px 6px;
          font-size: 9px;
          border-radius: 8px;

          &.quality,
          &.delivery {
            i {
              font-size: 10px;
            }
          }
        }
      }

      .product-footer {
        .product-status {
          .status-indicator {
            font-size: 10px;

            &.available {
              i {
                font-size: 11px;
              }
            }
          }
        }

        .view-details {
          font-size: 11px;

          .details-arrow {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 超小屏幕优化
  @media (max-width: 320px) {
    .modern-product-card {
      margin: 6px;
      width: calc(100% - 12px);
    }

    .product-image-section {
      width: 80px;
      height: 80px;

      .image-container {
        .product-image {
          width: 60px;
          height: 60px;
        }

        .image-placeholder {
          width: 60px;
          height: 60px;

          .placeholder-icon {
            font-size: 20px;
          }
        }
      }
    }

    .product-info-section {
      padding: 10px 12px;

      .product-name {
        .name-text {
          font-size: 13px;
          min-height: 32px;
        }
      }
    }
  }
  </style>
  