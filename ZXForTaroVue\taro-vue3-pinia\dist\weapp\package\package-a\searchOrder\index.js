"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[466],{3704:function(e,n,t){var r=t(1065),a=t(4147),u=t(1845),o=(t(3505),t(1630)),i=(t(3926),t(3193)),c=(t(9157),t(2419)),l=t(8140),s=t(2810),v=t(3221),p=t(6821),h=t(4733),f=t(1959),g=t.n(f),w=t(5969),d=t(2740),m={key:0,lock:"",style:{width:"100vw"}},Z={__name:"index",setup:function(e){var n=(0,p.iH)("");(0,v.bv)((function(){f.eventCenter.on((0,f.getCurrentInstance)().router.onShow,(function(){var e=g().getCurrentInstance().preloadData.value;console.log(e),e&&H(10,e)}))})),(0,v.Ah)((function(){f.eventCenter.off((0,f.getCurrentInstance)().router.onShow)}));var t=(0,p.iH)(1),r=(0,p.iH)(1),Z=((0,p.iH)([]),(0,p.iH)([])),H=function(){var e=(0,s.Z)((0,c.Z)().mark((function e(){var n,a,u,o,i,s,v=arguments;return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=v.length>0&&void 0!==v[0]?v[0]:20,a=v.length>1?v[1]:void 0,g().showLoading({title:"\u52a0\u8f7d\u4e2d"}),!(t.value<=r.value&&Z.value.length%n==0)){e.next=14;break}return console.log(Z.value.length,n),e.next=7,(0,h.co)({name:a});case 7:u=e.sent,o=u.error,i=u.success,console.log("----------",o),null==o?((s=Z.value).push.apply(s,(0,l.Z)(i.items)),r.value=i.psize,t.value<i.psize?t.value=i.cur_page+1:t.value=i.cur_page,g().hideLoading(),console.log(Z.value,r.value)):g().showToast({title:o,icon:"error",duration:2e3}),e.next=16;break;case 14:g().hideLoading(),g().showToast({title:"\u6ca1\u6709\u66f4\u591a\u8ba2\u5355\u4e86",icon:"none",duration:2e3});case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),k=function(){var e=(0,s.Z)((0,c.Z)().mark((function e(){return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:Z.value=[],r.value=1,t.value=1,H(20,n.value);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),x=((0,p.iH)(""),(0,p.iH)(0),(0,p.iH)(!1),(0,p.iH)(""),(0,p.iH)(!1),(0,p.iH)([]),(0,p.iH)(1),(0,p.iH)(1),function(){var e=(0,s.Z)((0,c.Z)().mark((function e(){return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("\u89e6\u5e95\u4e86");case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}());g().useReachBottom((0,s.Z)((0,c.Z)().mark((function e(){return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:x();case 1:case"end":return e.stop()}}),e)}))));(0,p.iH)(!1);return function(e,t){var r=i.Z,c=o.Z,l=u.Z,s=a.Z;return(0,v.wg)(),(0,v.iD)(v.HY,null,[(0,v.Wm)(r,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return n.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u8ba2\u5355\u53f7",bottom:40},{rightout:(0,v.w5)((function(){return[(0,v._)("div",{onClick:k},"\u67e5\u8be2")]})),rightin:(0,v.w5)((function(){return[(0,v.Wm)((0,p.SU)(w.LP))]})),_:1},8,["modelValue"]),(0,v.Wm)(s,{class:"card p-0_el_"},{default:(0,v.w5)((function(){return[Z.value.length>0?((0,v.wg)(),(0,v.iD)("div",m,[(0,v.Wm)(c,{height:"100vh",distance:30},{content:(0,v.w5)((function(){return[(0,v.Wm)(d.Z,{values:Z.value},null,8,["values"])]})),_:1})])):((0,v.wg)(),(0,v.j4)(l,{key:1,image:"empty",description:"\u6682\u65e0\u67e5\u8be2\u7ed3\u679c"}))]})),_:1})],64)}}};const H=Z;var k=H,x={navigationBarTitleText:"\u67e5\u8be2\u5386\u53f2\u8ba2\u5355"};Page((0,r.createPageConfig)(k,"package/package-a/searchOrder/index",{root:{cn:[]}},x||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(3704)}));e.O()}]);