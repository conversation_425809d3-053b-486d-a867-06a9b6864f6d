<script setup lang="ts">
import { navigateTo } from '@tarojs/taro';
import { useThemeStore } from '@/store';
import { Search2, RectDown } from '@nutui/icons-vue-taro'
import { h, onMounted, Ref, ref } from 'vue';
import { Button } from '@nutui/nutui-taro'
import Taro from '@tarojs/taro';
import {getStockList,getStock,getStockCount} from '@/service/index'
onMounted(async() => {
	console.log(paginateData(inventoryData.value, page));
	await getStockListF()
	await getStockF('*',`[0,10]`)
	await getStockCountF(`*`)

	

})
const visible = ref(false)
//查询条件与查询类型
const searchValue = ref('')
const searchType = ref('')
//获取库存分类列表函数
const getStockListF=async ()=>{
	const data =await getStockList()
	if(data.error===null){
		console.log(data,'data');
		options.value=data.success as []
		
	}

}
//获取库存信息函数
const getStockF=async (query:string,limit:string)=>{
	const data =await getStock({query,limit})
	if(data.error===null){
		console.log(data,'data');
		inventoryData.value=data.success as []
		//遍历inventoryData.value,统一追加
		inventoryData.value.forEach((item:any)=>{
			item.operation=(item:any) => {
                  return h(
                    Button,
                    {
                      type: "success",
                      style: {
                        background: '#27C631',
                        color: '#fff',
                        fontSize: '12px',
                        padding: '0 8px',
                        borderRadius: '4px',
                        marginLeft: '10px'
                      },
                      onClick: (event) => checkView(item.model)
                    },
                    '查看'
                  )
                }
		})
		showValue.value=inventoryData.value


		
		
	}

}
//获取库存条数函数
const stockCount:Ref<number>=ref(0)
const getStockCountF=async (query?:string)=>{
	const {error,success}=await getStockCount({query})
	if(error===null){

		stockCount.value=success.count as number
		
	}

}

const options = ref([
])
const change =async (...args: any[]) => {
	const list = Object.assign([], ...args)
	const listFilter = list[list.length - 1].text
	console.log(listFilter,'listFilter');
	searchType.value='model'
	await getStockCountF(`[${searchType.value},${listFilter.text}]`)
	showValue.value = inventoryData.value.filter((item) => {
		return item.name == listFilter
	})

}
//级联选择函数
const searchClick =async (value: string) => {
	
	// 点击清除函数

	if (value.length === 0) {
		showValue.value = inventoryData.value
	} else {
		searchType.value = 'model'
		await getStockCountF(`[${searchType.value},${searchValue.value}]`)
		await getStockList()
		showValue.value = inventoryData.value.filter((item) => {
			console.log(item.model.toLowerCase(),value.toLowerCase(),'2222');

			return item.model.toLowerCase() == value.toLowerCase()
		})
	}
}
const pathChange = (...args: any[]) => {
	console.log('pathChange', ...args)
}
const inventoryColumns = ref([
	{
		title: '编号',
		key: 'id',
	},
	{
		title: '产品名称',
		key: 'name',
	},
	{
		title: "产品型号",
		key: 'model',

	},
	{
		title: '数量',
		key: 'num',
	},
	{
		title:'价格',
		key:'price'
	},
	{
		title: '状态',
		key: 'status',
	},
	{
		title: '操作',
		key: 'operation',
	},
])
// 分页函数对showdata进行分页,每10条数据为1页
function paginateData(inventoryData: Array<object>, pageNumber: Ref<number>) {

	const itemsPerPage = 10;
	const startIndex = (pageNumber.value - 1) * itemsPerPage;
	const endIndex = startIndex + itemsPerPage;
	return inventoryData.slice(startIndex, endIndex);
}
// 表单查看按钮
const checkView = (value: {
	id: number,
	name: string,
	model: string,
	num: string,
	status: string,
}) => {
	console.log('value', value);

	Taro.navigateTo({
		url: '/package/package-a/Details/index?catId=' + value.model,
		fail: res => {
			console.log(res)
		},

	})
	Taro.preload({ value })
}

const inventoryData:Ref<Array<{
	id:number,
	name:string,
	model:string,
	num:string,
	price:string,
	status:string,
	operation:Function
}>> = ref([])
//展示用的数组
const showValue=ref([{}])
//page页
const page: Ref<number> = ref(1)
//页码变化时触发
const PaginationChange =async () => {
	console.log('page', page.value);
	if(searchValue.value.length===0){
		await getStockF(`*`,`[${[(page.value-1)*10,page.value*10]}]`)
	}
	else{
		await getStockF(`${searchType.value}=${searchValue.value}`,`[${[(page.value-1)*10,page.value*10]}]`)
	}


}





/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '产品'
});

</script>

<template>
	<basic-layout show-tab-bar>
		<div>
			<!-- <custom-navbar title="库存" /> -->
			<!-- 搜索框 -->
			<div class="search-box">
				<nut-searchbar v-model="searchValue" @clear="searchClick(searchValue)">
					<template #leftout>
						<nut-cell title="产品" @click="visible = true" style="padding: 5px 16px;">
							<template #link>
								<RectDown />
							</template>

						</nut-cell>
						<nut-cascader v-model:visible="visible" v-model="searchValue" title="选择产品型号" :options="options"
							@change="change" @path-change="pathChange"></nut-cascader>
					</template>
					<template #leftin>
						<Search2 />
					</template>
					<template #rightout>
						<view class="font-900" @click="searchClick(searchValue)" >
							搜索
						</view>
					</template>
				</nut-searchbar>

			</div>
			<!-- 库存表单 -->
			<div class="inerntoryTable-box m-2">
				<scroll-view class="scroll-view_H" scroll-x="true" style="width: 100%">
					<nut-table class="inerntoryTable-table" :columns="inventoryColumns" :data="showValue" striped></nut-table>
				</scroll-view>
			</div>
			<!-- 分页按钮 -->
			<nut-pagination v-model="page" :total-items="stockCount"
				:show-page-size="4" force-ellipses class="justify-center" @change="() => { PaginationChange() }" />
		</div>
	</basic-layout>
</template>
<style lang="scss">
.search-box .nut-cell .nut-cell__title {
	min-width: 65rpx;
}
.inerntoryTable-table{
	font-size: 0.65rem;
	line-height: 2.5rem;
}

.scroll-view_H {
	white-space: nowrap;
}
</style>
