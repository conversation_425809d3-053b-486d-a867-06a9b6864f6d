<script setup lang="ts">
import { eventCenter, navigateTo, getCurrentInstance } from '@tarojs/taro';
import { useAppStore, useThemeStore } from '@/store';
import { reactive, toRefs, onMounted, Ref, ref, onUnmounted } from 'vue'
import Taro from '@tarojs/taro';
import { getCategory, getProduct } from '@/service/api/index';
import Card from './components/card.vue';
import _ from 'lodash'; // 引入lodash库


const data = reactive({
	categoryInfo: {
		"category": [
			{
				"catName": "集电集控",
				"children": [
					{
						"catName": "彩钢标志灯(6mm)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "彩钢标志灯(4mm)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "不锈钢标志灯(6mm)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "不锈钢标志灯(4mm)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "不锈钢标志灯(大型)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "水晶标志灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防标志灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "地埋灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "隧道标志灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防应急标志灯配件",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防吸顶灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "方形/贝壳型吸顶灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防壁灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "嵌装筒灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "明装筒灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "双头灯",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "灯管",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "集中电源(锂电)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "集中电源(铅酸)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "应急照明控制器",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "应急照明灯具(B型)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "应急照明集中电源(EPS电源)",
						"childCateList":
						{
							"backImg": "",

						},
					},
				]
			},
			{
				catName: '自电集控',
				children: [
					{
						"catName": "彩钢标志灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "不锈钢标志灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "水晶标志灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防标志灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防应急标志灯配件(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "吸顶灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防吸顶灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "方形/贝壳形壁灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "三防壁灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "嵌装筒灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "明装筒灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "双头灯(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "灯管(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "应急照明配电箱(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "应急照明控制器(自)",
						"childCateList":
						{
							"backImg": "",

						},
					},
				]
			},
			{
				"catName": "火灾报警",
				"children": [
					{
						"catName": "火灾自动报警系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "模块接口产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "壁挂式火灾报警控制器",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "立柜式火灾报警控制器",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "琴台式火灾报警控制器",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防联动控制系统产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防广播通讯系统产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "家用火灾报警产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防电源类产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "配套类产品",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "电气火灾监控系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "防火门监控系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "消防设备电源监控系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "气体灭火控制系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "可燃气体报警系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "余压监控系统",
						"childCateList":
						{
							"backImg": "",

						},
					},

				]
			},
			{
				"catName": "自电非集控",
				"children": [
					{
						"catName": "A类产品(7天)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "B类产品(15天)",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "C类产品(20天)",
						"childCateList":
						{
							"backImg": "",

						},
					},






				]
			},
			{
				"catName": "自电非集控",
				"children": [
					{
						"catName": "集电集控防爆系统",
						"childCateList":
						{
							"backImg": "",

						},
					},
					{
						"catName": "自电集控防爆系统",
						"childCateList":
						{
							"backImg": "",

						},
					},


				]
			}
		]
	},
	category: [
		{
			"catName": "集电集控",
			"children": [
				{
					"catName": "彩钢标志灯(6mm)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "彩钢标志灯(4mm)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "不锈钢标志灯(6mm)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "不锈钢标志灯(4mm)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "不锈钢标志灯(大型)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "水晶标志灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防标志灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "地埋灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "隧道标志灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防应急标志灯配件",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防吸顶灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "方形/贝壳型吸顶灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防壁灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "嵌装筒灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "明装筒灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "双头灯",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "灯管",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "集中电源(锂电)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "集中电源(铅酸)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "应急照明控制器",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "应急照明灯具(B型)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "应急照明集中电源(EPS电源)",
					"childCateList":
					{
						"backImg": "",

					},
				},
			]
		},
		{
			catName: '自电集控',
			children: [
				{
					"catName": "彩钢标志灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "不锈钢标志灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "水晶标志灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防标志灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防应急标志灯配件(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "吸顶灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防吸顶灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "方形/贝壳形壁灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "三防壁灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "嵌装筒灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "明装筒灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "双头灯(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "灯管(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "应急照明配电箱(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "应急照明控制器(自)",
					"childCateList":
					{
						"backImg": "",

					},
				},
			]
		},
		{
			"catName": "火灾报警",
			"children": [
				{
					"catName": "火灾自动报警系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "模块接口产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "壁挂式火灾报警控制器",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "立柜式火灾报警控制器",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "琴台式火灾报警控制器",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防联动控制系统产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防广播通讯系统产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "家用火灾报警产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防电源类产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "配套类产品",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "电气火灾监控系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "防火门监控系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "消防设备电源监控系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "气体灭火控制系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "可燃气体报警系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "余压监控系统",
					"childCateList":
					{
						"backImg": "",

					},
				},

			]
		},
		{
			"catName": "自电非集控",
			"children": [
				{
					"catName": "A类产品(7天)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "B类产品(15天)",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "C类产品(20天)",
					"childCateList":
					{
						"backImg": "",

					},
				},






			]
		},
		{
			"catName": "防爆",
			"children": [
				{
					"catName": "集电集控防爆系统",
					"childCateList":
					{
						"backImg": "",

					},
				},
				{
					"catName": "自电集控防爆系统",
					"childCateList":
					{
						"backImg": "",

					},
				},


			]
		}
	],

	categoryChild: [
		{
			"catName": "彩钢标志灯(6mm)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "彩钢标志灯(4mm)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "不锈钢标志灯(6mm)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "不锈钢标志灯(4mm)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "不锈钢标志灯(大型)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "水晶标志灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "三防标志灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "地埋灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "隧道标志灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "消防应急标志灯配件",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "三防吸顶灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "方形/贝壳型吸顶灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "三防壁灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "嵌装筒灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "明装筒灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "双头灯",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "灯管",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "集中电源(锂电)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "集中电源(铅酸)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "应急照明控制器",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "应急照明灯具(B型)",
			"childCateList":
			{
				"backImg": "",

			},
		},
		{
			"catName": "应急照明集中电源(EPS电源)",
			"childCateList":
			{
				"backImg": "",

			},
		},
	],
})




onMounted(() => {
	eventCenter.on(getCurrentInstance().router!.onShow, async () => {
		console.log('preloadData----', Taro.getStorageSync('tab'));
		if (Taro.getStorageSync('tab')) {
			await getCategoryF(changeItemMap.get(Taro.getStorageSync('tab'))![active.value])
			Taro.removeStorageSync('tab')
		} else {
			await getCategoryF(changeItemMap.get(MainClassItem.value)![active.value])

		}




	})
})
onUnmounted(() => {
	eventCenter.off(getCurrentInstance().router!.onShow)
})
const changeMAp = new Map([
	[0, '集电集控'],
	[1, '自电集控'],
	[2, '火灾报警'],
	[3, '自电非集控'],
	[4, '防爆']
])
const changeItemMap = new Map([
	['集电集控', ['彩钢标志灯(6mm)', '彩钢标志灯(4mm)', '不锈钢标志灯(6mm)', '不锈钢标志灯(4mm)', '不锈钢标志灯(大型)',
		'水晶标志灯', '三防标志灯', '地埋灯', '隧道标志灯', '消防应急标志灯配件', '三防吸顶灯', '方形/贝壳型吸顶灯', '三防壁灯',
		'嵌装筒灯', '明装筒灯', '双头灯', '灯管', '集中电源(锂电)','集中电源(铅酸)', '应急照明控制器', '应急照明灯具(B型)', '应急照明集中电源(EPS电源)']],
	['自电集控', ['彩钢标志灯(自)', '不锈钢标志灯(自)', '水晶标志灯(自)', '三防标志灯(自)', '消防应急标志灯配件(自)', '吸顶灯(自)', '三防吸顶灯(自)',
		'方形/贝壳形壁灯(自)', '三防壁灯(自)', '嵌装筒灯(自)', '明装筒灯(自)', '双头灯(自)', '灯管(自)', '应急照明配电箱(自)', '应急照明控制器(自)']],
	['火灾报警', ['火灾自动报警系统', '模块接口产品', '壁挂式火灾报警控制器', '立柜式火灾报警控制器', '琴台式火灾报警控制器', '消防联动控制系统产品', '消防广播通讯系统产品',
		'家用火灾报警产品', '消防电源类产品', '配套类产品', '电气火灾监控系统', '防火门监控系统', '消防设备电源监控系统', '气体灭火控制系统', '可燃气体报警系统', '余压监控系统', '环境/车库监控报警系统']],
	['自电非集控', ['A类产品(7天)', 'B类产品(15天)', 'C类产品(20天)']],
	['防爆', ['集电集控防爆系统', '自电集控防爆系统']]
])
//162333,162334
const getIdMap = new Map([
	['彩钢标志灯(6mm)', [161226, 161227, 161229, 161228, 161225, 161230, 161234, 161236, 161235, 161233, 161237]],
	['彩钢标志灯(4mm)', [161784, 161785, 161787, 161786, 161783, 162112, 162382, 161792, 161789, 161791, 161790, 161788, 161793, 162385]],
	['不锈钢标志灯(6mm)', [161353, 161256, 161244, 161248, 162340, 161241, 161239, 161301]],
	['不锈钢标志灯(4mm)', [161771, 161772, 161773, 161775, 161774, 162370, 161780, 161776, 161777, 161779, 161778, 161793]],
	['不锈钢标志灯(大型)', [162006, 162004, 162005, 162007, 162003, 161550, 161551, 161754, 161753]],
	['水晶标志灯', [161770, 161769, 161957, 161961, 161927, 161527, 161294, 161924]],
	['三防标志灯', [161943, 161944, 161945, 161947, 161946, 161951, 161952, 161954, 161953]],
	['地埋灯', [161755, 161756, 161705]],
	['隧道标志灯', [161253, 161260, 161257, 161245, 161249, 161907, 161289]],
	['消防应急标志灯配件', [161701, 161767, 161702, 161818, 161967, 161817, 161926]],
	['吸顶灯', [162142, 162271, 161564, 161397, 161276, 161808, 161265, 161388, 161971, 161972, 161963, 161973]],
	['三防吸顶灯', [161941, 162147, 161797, 162138]],
	['方形/贝壳型吸顶灯', [161275, 161798, 161280]],
	['三防壁灯', [161287, 161399, 161799, 162124, 161800, 161809]],
	['嵌装筒灯', [161271, 161401, 161273, 161402, 161274, 161405]],
	['明装筒灯', [161557, 162144, 161558, 162137, 161559, 162141]],
	['双头灯', [162301, 162049, 162050]],
	['灯管', [161567, 161801, 161568, 161263]],
	['应急照明集中电源电（锂电）', [162515, 161742, 162516, 161741, 162517, 162024, 162518, 162023, 162519, 162022]],
	['应急照明集中电源电(铅酸）', [162182, 161746, 162183, 161747, 162184, 161751]],
	['应急照明控制器', [161381, 161382, 161383]],
	['应急照明灯具(B型)', [161570, 161571, 161572, 162304, 162305, 162306, 161802, 162019, 162044, 162105, 162021]],
	['应急照明集中电源(EPS电源)', [161655, 161656, 162290]],
	['彩钢标志灯(自)', [161336, 161332, 161321, 161324, 161328, 161314, 162497, 161347, 161343, 161343, 161352, 161338]],
	['不锈钢标志灯(自)', [161325, 161333, 161329, 161318, 162460, 161586, 161606, 161350, 161344, 161341, 161348, 161327, 161335, 161331, 161320, 161323, 161615, 162015, 161351, 161346, 161342, 161349, 161337, 162064, 162065, 162066, 162068, 162067, 162069, 162070, 162071, 162072]],
	['水晶标志灯(自)', [162174, 162210, 162429, 162530]],
	['三防标志灯(自)', [162076, 162077, 162078, 162080, 162079, 162081, 162082, 162083, 162084]],
	['消防应急标志灯配件(自)', [161702, 161818, 161967, 161817, 162136]],
	['吸顶灯(自)', [161804, 162017, 162017, 161311, 161416, 161306, 161408, 162074, 162075]],
	['三防吸顶灯(自)', [162101, 162110, 162150, 161960]],
	['方形/贝壳形壁灯(自)', [161810, 161601,]],
	['三防壁灯(自)', [161312, 161633, 161965, 162146, 162104, 161939]],
	['嵌装筒灯(自)', [161595, 161419, 161596, 161421, 161597, 161423]],
	['明装筒灯(自)', [161595, 161419, 161596, 161421, 161597, 161423]],
	['双头灯(自)', [162302, 162051, 162052]],
	['灯管(自)]', [161602, 161603, 161604, 161302, 161631]],
	['应急照明配电箱(自)', [162153, 162154, 162155]],
	['应急照明控制器(自)', [161383, 161382, 161381]],
	['火灾自动报警系统', [161491, 161490, 161489, 161503, 161504, 161505, 161506, 161516, 161512, 161518, 161908]],
	['模块接口产品', [161507, 161508, 161509, 161515, 161510, 161511, 161514, 161517, 161513]],
	['壁挂式火灾报警控制器', [161444, 161661, 161662, 161663, 161448, 161452, 161453, 161454, 161455, 161521, 161522, 161523]],
	['立柜式火灾报警控制器', [161472, 161473, 161466, 161467, 161468, 161469, 161470, 161471, 161457, 161459, 161460, 161461, 161462, 161463, 161464, 161465, 161987, 161988, 161989, 161990]],
	['琴台式火灾报警控制器', [161476, 161478, 161479, 161480, 161474, 161475, 161477, 161660, 161813, 161919, 161920, 161935, 161978, 161979, 161980, 161981, 161982, 161983, 161984, 161985, 161986]],
	['消防联动控制系统产品', [161458, 161488]],
	['消防广播通讯系统产品', [162030, 162031, 162032, 162032, 162034, 162035, 162036, 162037, 162333, 162334, 161708, 162027, 162028, 162045, 162029, 162026]],
	['家用火灾报警产品', [161674, 161675, 162171, 162172]],
	['消防电源类产品', [161714, 161451, 161743, 161381, 161745, 161709, 161710, 161711]],
	['配套类产品', [161487, 162205, 162011, 162010, 162009, 161995, 161996, 161997, 162216]],
	['电气火灾监控系统', [161492, 161494, 161496, 161498, 161493, 161495, 161497, 161499, 161500, 161970, 161766, 161501, 161727, 161526, 161717, 161719, 161728, 161716, 161718, 161720, 161721, 161502, 161482, 161664, 161665, 161666, 161726]],
	['防火门监控系统', [161679, 161680, 161681, 161517, 161685, 161687, 161682, 161684, 161481, 161667, 161668, 161669, 162103]],
	['消防设备电源监控系统', [161691, 161692, 161693, 161694, 161695, 161483, 161670, 161671, 161672, 161723, 161724, 161725, 161722, 161729]],
	['气体灭火控制系统', [161484, 161446, 161485, 161486, 161447, 161696, 161697, 161698]],
	['可燃气体报警系统', [162231, 162232, 162233, 162042, 162106, 162244, 162245, 162246]],
	['余压监控系统', [161732, 161733, 161734, 161735, 161736, 161678, 161731]],
	['环境/车库监控报警系统', [161688, 161690, 162525, 162102, 161678]],
	['A类产品(7天)', [162406, 162407, 161870, 161871, 162411, 162322, 162410, 161876, 161876, 161875, 162309, 161807, 161877, 161880, 161384, 161385, 161635, 161636, 161637, 161386, 161638, 161639, 161640, 161641]],
	['B类产品(15天)', [161859, 161860, 161865, 161866, 161867, 161758, 161759, 161760, 161761]],
	['C类产品(20天)', [161365, 161858, 161376, 161846, 161847, 161823, 161848, 161362, 161368, 162417, 162565, 161840, 161841, 161842, 161843, 161844, 161845, 161380, 161869, 162414, 162415, 162484, 162551, 161850, 161851, 161852, 161853, 161854, 161855, 161856, 161857, 161371, 162416, 162550, 161878, 161879, 161890, 161891, 161994, 161882, 161883, 161884, 162412, 162413, 162445]],
	['集电集控防爆系统', [161812, 161652, 161433, 162418, 162433, 162434, 162420, 162421, 161426, 161975, 161811, 161651, 161431, 161432]],
	['自电集控防爆系统', [161654, 161922, 162424, 162431, 162432, 162422, 162423, 161434, 161976, 161974, 162093, 161438, 162097]]















])
const MainClassItem = ref('集电集控')
const ClassItem = ref('彩钢标志灯(6mm)')
const change = async (index: string | number) => {
	console.log('change', changeMAp.get(index as number));
	active.value = 0
	MainClassItem.value = changeMAp.get(index as number) as string
	data.categoryChild = [].concat(data.categoryInfo.category[index].children)
	console.log("changeItemMap", changeItemMap.get(MainClassItem.value)![0]);
	ClassItem.value = changeItemMap.get(MainClassItem.value)![0]

	await getCategoryF(ClassItem.value)
}





const appStore = useAppStore();


/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '分类'
});

const getData = async () => {



};


//跳转登录页函数
const toLogin = () => {
	Taro.reLaunch({
		url: '/pages/login/index'
	})
}
const searchValue = ref('')

Taro.showShareMenu({
	withShareTicket: true,
	showShareItems: ['shareAppMessage', 'shareTimeline']
})

const active = ref(0)
//产品列表

const state = ref({
	list: [

	]
})
//获取产品列表事件
const getCategoryF = async (msg?: string) => {

	//发起网络请求
	const { error, success }: { error: any, success: any } = await getProduct({ category: ClassItem.value,per_page: 100 })
	console.log("试试", error);
	if (error == null) {
		state.value.list = success.items
	} else {
		Taro.showToast({
			title: '加载失败',
			icon: 'error',
			duration: 2000
		})
	}

}
const clickClassItem = async (item: any, index: number) => {
	console.log(item.catName);
	ClassItem.value = item.catName
	active.value = index

	await getCategoryF()
}

//下拉触底事件
const onReachBottom = _.throttle(function () {
	console.log('下拉触底事件');

}, 300)

//产品事件
const getProductF = async (item: any) => {
	console.log(item);

	//发起网络请求
	const { error, success }: { error: any, success: any } = await getProduct({ model: item.model })
	console.log(error, success);
	if (error == null) {
		//跳转产品详情页
		Taro.preload({ value: item.code })
		Taro.navigateTo({
			url: '/package/package-a/Details/index',
			fail: (res: any) => {
				console.log(res)
			}


		})
		console.log('当前分类数据')
	} else {
		Taro.showToast({
			title: '暂无数据',
			icon: 'error',
			duration: 2000
		})
	}


}
</script>

<template>
	<basic-layout show-tab-bar style="background-color: #f7f7f7;">




		<div class="m-2">
			<nut-cell style="box-shadow: 0 0 black;">
				<nut-category :category="data.category" @change="change" style="width: 100%;">

					<nut-backtop height="75vh" style="width: 100%;" :distance="30" :bottom="150">
						<template #content>

							<scroll-view scroll-x="true" scroll-y="false" style='width: 75% !important;display:flex;justify-content: center;white-space:nowrap;padding: 0 3% !important;vertical-align: middle;padding: 1%;height: 40px !important;
    '>
								<div>
									<template v-for="(item, index) in data.categoryChild" :key="item.catName">
										<div style="display: inline-block;border-radius: 3px;width: 150px;height: 50px;padding: 1%;"
											class="myClickItem" @click="[clickClassItem(item, index)]"
											:class="{ hideItem: active == index }">
											<div class=" h-40px!">
												<!-- <image :src="item.childCateList.backImg" class="w-75px h-75px"></image> -->
												<span class="w-100%! text-center font-600 h-40px!"
													style="line-height: 40px !important;">{{ item.catName }}</span>
											</div>
										</div>
									</template>
								</div>
							</scroll-view>
							<scroll-view :scroll-y="true" style="height: 480px;width: 85%;" class="product-scroll-container"
								@ScrollToLower.stop="onReachBottom">

								<template v-for="(item, index) in state.list" :key="index">
									<Card :item="item" @click="getProductF(item)" />
									
								</template>
							</scroll-view>

						</template>
					</nut-backtop>
				</nut-category>
			</nut-cell>
		</div>


		<!-- 底部信息栏 -->
		<div class="user-button-text">
			<span>{{ '技术支持 © 广东左向科技有限公司' }}</span>
		</div>
	</basic-layout>
</template>
<style lang="scss">
.hideItem {
	filter: brightness(0.9);
	color: #E53935;
}

// .hideItem::before {
// 	content: " ";
// 	position: absolute;
// 	top: 0;
// 	left: 0;
// 	width: 75px;
// 	height: 75px;
// 	/*最后一个参数是半透明度，可以透过调整0-1的数值，调整到满意的透明度*/
// 	background-color: rgba(0, 0, 0, 0.2);
// }


.nut-category__cateListItem {
	font-weight: 900;
}

.user-button-text {
	font-size: 12px;
	color: #A6A6A6;
	text-align: center;
	margin: 2rem auto;
}

.toLogin {
	position: fixed;
	bottom: 13%;
	background-color: rgba($color: #3a3a3a, $alpha: .7);
	border-radius: 5px;
	left: 50%;
	transform: translate(-50%, 0);
	padding: 2% 4%;
	line-height: 100%;
	color: #D0D0D0;
	font-size: 0.75rem;
	line-height: 1.875rem;
	width: 85%;
	display: flex;
	justify-content: space-between;

	.toLogin-button {
		color: #EAEEF9;
	}
}

.cpxg {
	box-shadow: none;
	background: transparent;
	padding-left: 0;
	font-weight: 600;
	font-size: 1.125rem;

	.cpxg-link {
		color: #070707;
		font-size: 12px;
		font-weight: 100;
	}
}

::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}

.nut-fixed-nav__btn {
	width: 60px !important;
}

// .myClickItem:last-child {
// 	padding-right: 150px}

// 重新设计的子分类项样式
.myClickItem {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border: 1px solid rgba(18, 47, 56, 0.1);
	border-radius: 16px;
	margin-right: 8px;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(18, 47, 56, 0.06);

	// 悬停效果
	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 16px rgba(18, 47, 56, 0.12);
		border-color: #122F38;
	}

	// 激活状态
	&.hideItem {
		background: linear-gradient(135deg, #122F38 0%, #0d252c 100%);
		border-color: #122F38;
		color: #fff !important;
		transform: translateY(-1px);
		box-shadow: 0 4px 16px rgba(18, 47, 56, 0.2);

		span {
			color: #fff !important;
			font-weight: 600 !important;
		}

		// 添加激活指示器
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 3px;
			background: linear-gradient(90deg, #FF6B35 0%, #FF9800 100%);
		}

		// 添加右侧箭头指示器
		&::after {
			content: '';
			position: absolute;
			right: 8px;
			top: 50%;
			transform: translateY(-50%);
			width: 0;
			height: 0;
			border-left: 6px solid #FF6B35;
			border-top: 4px solid transparent;
			border-bottom: 4px solid transparent;
		}
	}

	// 内容样式优化
	span {
		color: #122F38;
		font-weight: 500;
		font-size: 13px;
		transition: all 0.3s ease;
		display: block;
		text-align: center;
		padding: 0 8px;
		line-height: 40px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	// 添加微妙的内阴影效果
	&:active {
		transform: translateY(0);
		box-shadow: inset 0 2px 4px rgba(18, 47, 56, 0.1);
	}
}

// 响应式设计
@media (max-width: 375px) {
	.myClickItem {
		border-radius: 12px;
		margin-right: 6px;

		span {
			font-size: 12px;
			padding: 0 6px;
		}

		&.hideItem {
			&::after {
				right: 6px;
				border-left: 5px solid #FF6B35;
				border-top: 3px solid transparent;
				border-bottom: 3px solid transparent;
			}
		}
	}
}

// 产品滚动容器样式 - 解决Card组件宽度溢出问题
.product-scroll-container {
	padding: 8px 12px !important;
	box-sizing: border-box !important;

	// 确保容器不会导致水平滚动
	overflow-x: hidden !important;

	// 为Card组件提供合适的容器环境
	& > * {
		max-width: 100% !important;
		box-sizing: border-box !important;
	}
}

// 响应式优化产品容器
@media (max-width: 375px) {
	.product-scroll-container {
		padding: 6px 8px !important;
	}
}

@media (max-width: 320px) {
	.product-scroll-container {
		padding: 4px 6px !important;
	}
}
</style>
