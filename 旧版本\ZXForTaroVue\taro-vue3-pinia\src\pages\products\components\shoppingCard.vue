<template>
    <!-- {{ props.state }} -->
    <template v-for="state in searchList">
        

        <div class="p1%! w-30%! card-box relative mb2%! " @click="gotoFind(state.itemNumber, state.itemNumber)">
            <div class="flex flex-col w-100%!">
                <image :src="state.imgUrl" mode="widthFix" class="w-100%!" />
                <span style="color: #757575;font-weight: bold;" class=" ellipsis">
                    <span class="flex! justify-between flex-wrap">
                        <nut-tag color="#F7AA71" class="ycyc">{{ state.title.split('/')[0] }}</nut-tag>
                        <nut-tag color="#F7AA71" class="ycyc">{{ state.title.split('/')[1] }}</nut-tag>
                    </span>

                    <span>
                        <nut-tag color="#F7AA71" class="mt-3% ycyc" v-show="state.title.split('/')[2]">{{
                            state.title.split('/')[2] }}</nut-tag>
                    </span>
                </span>
                <span>
                    <nut-tag color="#FA685D" class="m-3%!"> {{ state.itemNumber }} </nut-tag>
                </span>
                <div class="flex flex-row justify-left">
                    <span style="color: #E8220E;font-weight: bold;">{{ state.price }}
                        <span class="unit color-#ED7976  font-900 font-size-10px">{{ '/' }}</span>
                        <span class="color-#ED7976 font-900 font-size-8px">{{ 'PCS' }}</span>
                    </span>
                    <!-- <nut-tag type="danger" class="w-0!" text-color="#E8220E" color="#FDEDEC" ></nut-tag> -->
                </div>
                <div style="color: #FFF;font-weight: bold;background-color: #EF180D;" class="absolute">
                    <i class="i-bx-crown" style="vertical-align: text-bottom;color: #FFF;font-weight: bold;"></i>
                    {{ state.shopDesc }}
                </div>
            </div>
        </div>

    </template>

</template>
<script setup lang="ts">
import { onMounted, ref, watch, watchEffect } from 'vue'
import { defineProps } from 'vue';
import { getProduct, hotProduct} from '@/service/index'
import Taro from '@tarojs/taro';
const props = defineProps({
    state: Array
})
const gotoFind = async (i: any, name: string) => {
    //发起网络请求
    const { error, success }: { error: any, success: any } = await getProduct({ code: i })
    console.log(error, success);
    if (error == null) {
        //跳转产品详情页
        Taro.preload({ model: name })
        Taro.navigateTo({
            url: '/package/package-a/Details/index?model=' + name,
            fail: (res: any) => {
                console.log(res)
            }


        })
        console.log('当前分类数据')
    } else {
        Taro.showToast({
            title: '暂无数据',
            icon: 'error',
            duration: 2000
        })
    }

}
const name = ref('')
const searchList = ref([])
watch([props],async()=>{
    console.log('变化了');
    
   await getProductF()
})
const getProductF=async()=>{
    const { error, success } = await hotProduct()
    if (error == null && success) {
        console.log('success~~~~~~', success.items);

        searchList.value = success.items.map((i: { image: any; model: string; name: string; price_unit: string; code: any; }, index: number) => {
            return {
                imgUrl: i.image,
                title: i.model + '/' + i.name,
                price: '￥' + i.price_unit,
                name: i.name,
                state: i.code,
                shopDesc: `销量TOP` + Number(index + 1),
                tag: '',
                itemNumber: i.code
            }

        })
        // console.log('shopcard', success);
        // i.image=success.items[0].image
        // state.value.imgUrl = success.items[0].image
        // state.value.title = success.items[0].model + '/' + success.items[0].name
        // state.value.price = '￥' + success.items[0].price_unit
        // name.value = success.items[0].name
        // state.value.itemNumber = success.items[0].code

        // i.product_id=success.items[0].id
    }

}
onMounted(async () => {
   await getProductF()
})


const state = ref({
    imgUrl:
        'https://img01.71360.com/file/read/www/M00/41/57/rBwBHmTWAw-AFdtTAAEdAOpPUAo680.png?w=600',
    title: 'ZX1650A/单左/集电集控型消防应急标志灯具',
    price: '￥ 388',
    shopDesc: '销量TOP1',
    tag: '促销中',
    itemNumber: ''

})
</script>
<style lang="scss">
.card-box {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    border-radius: 3px;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 设置行数为2 */
}

.ycyc {
    white-space: nowrap;
    /* 文本不换行 */
    overflow: hidden;
    /* 隐藏溢出的内容 */
    text-overflow: ellipsis;
    /* 使用省略号表示文本溢出 */
}
</style>