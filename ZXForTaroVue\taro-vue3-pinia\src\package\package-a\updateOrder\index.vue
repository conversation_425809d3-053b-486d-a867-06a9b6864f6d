<template>
    <div class="update-order-page">
        <!-- 优化后的楼层搜索弹窗 -->
        <nut-action-sheet v-model:visible="show" class="floor-search-sheet">
            <div class="search-container">
                <div class="search-header">
                    <h3 class="search-title">选择楼层图案</h3>
                    <p class="search-subtitle">请输入楼层编号进行搜索</p>
                </div>
                <nut-searchbar
                    v-model="searchValue"
                    shape="round"
                    placeholder="请输入楼层以搜索"
                    input-background="#f8f9fa"
                    @change="GetInputFocus"
                    id="pop-target"
                    class="custom-searchbar"
                >
                    <template #leftin>
                        <Search2 />
                    </template>
                </nut-searchbar>

                <div class="search-results-container">
                    <div class="search-results">
                        <div v-if="searchList.length > 0" class="results-list">
                            <nut-list :list-data="searchList" :container-height="280" @scroll-bottom="onScrollBottom">
                                <template #default="{ item }">
                                    <div class="floor-item" @click="clickItem(item)">
                                        <div class="floor-info">
                                            <i class="i-bx-building-house floor-icon"></i>
                                            <span class="floor-name">{{ item.name }}</span>
                                        </div>
                                        <i class="i-bx-chevron-right select-icon"></i>
                                    </div>
                                </template>
                            </nut-list>
                        </div>

                        <div v-else class="empty-search">
                            <div class="empty-icon">
                                <i class="i-bx-search-alt"></i>
                            </div>
                            <p class="empty-text">请输入相应正确楼层编号后进行查询</p>
                        </div>
                    </div>
                </div>
            </div>
        </nut-action-sheet>

        <!-- 优化后的收货地址区域 -->
        <div class="address-section">
            <div class="section-header">
                <div class="header-left">
                    <i class="i-bx-map section-icon"></i>
                    <span class="section-title">收货地址</span>
                </div>
                <div class="header-right" @click="editClick" :class="{ disabled: !(stateMap.get(params!.state as string) ?? true) }">
                    <span class="edit-text">编辑</span>
                    <i class="i-bx-edit-alt edit-icon"></i>
                </div>
            </div>
            <div class="address-content">
                <nut-address-list :data="harvestData" :show-bottom-button="false" :data-options="harvestOptions">
                    <template #item-icon>
                        <div class="address-icon">
                            <i class="i-bx-user"></i>
                        </div>
                    </template>
                </nut-address-list>
            </div>
        </div>
        <!-- 优化后的项目信息区域 -->
        <div class="project-section">
            <div class="section-header">
                <i class="i-bx-folder section-icon"></i>
                <span class="section-title">项目信息</span>
            </div>
            <div class="project-content">
                <div class="input-group">
                    <div class="input-label required">
                        <span>项目名称</span>
                        <span class="required-mark">*</span>
                    </div>
                    <nut-textarea
                        v-model="project"
                        limit-show
                        :max-length="50"
                        autosize
                        placeholder="请输入项目名称"
                        class="custom-textarea project-textarea"
                        :disabled="!(stateMap.get(params!.state as string) ?? true)"
                    />
                </div>
                <div class="input-group">
                    <div class="input-label">
                        <span>备注信息</span>
                    </div>
                    <nut-textarea
                        v-model="note"
                        limit-show
                        :max-length="50"
                        autosize
                        :placeholder="((stateMap.get(params!.state as string) ?? true)) ? '请输入备注信息（选填）' : '未填写'"
                        class="custom-textarea note-textarea"
                        :disabled="!(stateMap.get(params!.state as string) ?? true)"
                    />
                </div>
            </div>
        </div>
        <nut-popup v-model:visible="popupShow" position="bottom" round :style="{ height: '65vh' }"
            class="flex flex-col justify-evenly">
            <Address @click-item="(val) => { console.log(val); popupShow = val.boolean; harvestData[0] = val.item }"
                :showDefault="true" @defauult-emit="() => {
                    popupShow = false; harvestData[0].addressName = '采用U9默认联系地址'; harvestData[0].fullAddress = ' '; harvestData[0].phone = ' ';
                }">
            </Address>
            <!-- <nut-form ref="formRef">
                <nut-form-item label="联系人" prop="testaddressName" :rules="[
                    {
                        validator: validatorName,
                        message: '请输入正确的人名'
                    }
                ]">
                    <nut-input v-model="formData.testaddressName" placeholder="请输入联系人" type="text" />
                </nut-form-item>
                <nut-form-item label="联系电话" prop="phone" :rules="[
                    {
                        validator: validatorPhone,
                        message: '请输入正确的手机号'

                    }
                ]">
                    <nut-input v-model="formData.phone" placeholder="请输入联系电话" type="text" />
                </nut-form-item>
                <nut-form-item label="联系地址" prop="fullAddress" :rules="[
                    {
                        validator: validatorAddress,
                        message: '地址应尽量详细'

                    }

                ]">
                    <nut-input v-model="formData.fullAddress" placeholder="请输入地址" type="text" />
                </nut-form-item>
                <nut-button type="primary" size="large" class="verifi-button w-60% " block @click="submitAddress">{{
                    '保存' }}</nut-button>
            </nut-form> -->
            <nut-input>

            </nut-input>
        </nut-popup>
        <!-- 删除确认对话框 -->
        <nut-dialog
            title="确认删除"
            content="确定要移除这个商品吗？"
            v-model:visible="isRmCommodityDialogShow"
            @cancel="reCCard"
            @ok="removeCommodity"
            class="custom-dialog"
        />

        <!-- 优化后的商品列表区域 -->
        <div class="products-section relative" @longpress="onLongPressOrderList">
            <div class="section-header">
                <i class="i-bx-package section-icon"></i>
                <span class="section-title">订单详情</span>
                <span class="product-count">(共{{ cartCommodities.length }}款)</span>
                <!-- 长按复制提示 -->
                <div class="copy-hint">
                    <i class="i-bx-copy-alt"></i>
                    <span>长按复制订单</span>
                </div>
            </div>
            <div class="products-content">
                <template v-if="cartCommodities.length > 0">
                    <nut-swipe-group lock class="product-swipe-group">
                        <nut-swipe
                            v-for="(item, index) in cartCommodities"
                            ref="swipeRefs"
                            class="product-swipe"
                            :name="item.line_id.toString()"
                            :key="item.line_id"
                        >
                            <div class="product-card" @longpress="getHarvestData(item.line_id)">
                                <!-- 商品主要信息区域 -->
                                <div class="product-main-info">
                                    <div class="product-image-section">
                                        <div class="image-container">
                                            <image
                                                :src="item.product_image ?? 'https://tse2-mm.cn.bing.net/th/id/OIP-C.exoIucrWcex80_QKk3z5DAAAAA?w=181&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7'"
                                                class="product-image"
                                            />
                                            <div class="image-overlay" v-if="!item.product_image">
                                                <i class="i-bx-image-alt"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-info-section">
                                        <div class="product-title-area">
                                            <!-- 产品型号标签 -->
                                            <div class="model-tag">
                                                <i class="i-bx-crown"></i>
                                                <span>{{ item.model }}</span>
                                            </div>

                                            <!-- 产品名称标签 -->
                                            <div class="name-tag">
                                                {{ item.name.split('/')[0] }}
                                            </div>

                                            <!-- 产品子名称 -->
                                            <div class="sub-name-tag" v-show="item.name.split('/')[1]">
                                                {{ item.name.split('/')[1] }}
                                            </div>
                                        </div>

                                        <!-- 产品规格 -->
                                        <div class="product-spec">
                                            <span class="spec-text">{{ item.spec }}</span>
                                        </div>

                                                                      <!-- 订单类型 -->
                                    <div class="order-type-section">
                                        <div class="order-type-label">订单类型:</div>
                                        <nut-radio-group
                                            v-model="item.zx_order_type"
                                            direction="horizontal"
                                            @change="(value) => handleOrderTypeChange(value, item)"
                                        >
                                            <nut-radio
                                                label="0"
                                                icon-size="14"
                                                class="order-type-radio"
                                            >
                                                标准做法
                                            </nut-radio>
                                            <nut-radio
                                                label="1"
                                                icon-size="14"
                                                class="order-type-radio"
                                            >
                                                特殊做法
                                            </nut-radio>
                                        </nut-radio-group>
                                    </div>

                                        <!-- 价格和编号区域 -->
                                        <div class="price-code-section">
                                            <div class="price-area">
                                                <span class="currency">¥</span>
                                                <span class="price">{{ item.price_unit }}</span>
                                                <span class="unit">/PCS</span>
                                            </div>
                                            <div class="code-area">
                                                <span class="code-tag">{{ item.code }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 设计选择组件 -->
                                <DesignCell
                                    :design="item.seiban"
                                    :id="item.line_id"
                                    :index="index"
                                    :name="item.name.split('/')[0]"
                                    :class="{
                                        disabled: !(stateMap.get(params!.state as string) ?? true),
                                        noShow: !item.name.split('/')[0].includes('特殊图案')
                                    }"
                                    @update:design="showDialog"
                                    class="design-cell"
                                />

                                <!-- 商品备注区域 -->
                                <div class="product-note-section">
                                    <div class="note-label">
                                        <i class="i-bx-message-square-detail"></i>
                                        <span>商品备注</span>
                                    </div>
                                    <nut-input
                                        v-model="item.zx_line_notes"
                                        :max-length="50"
                                        placeholder="请输入商品备注（下单后无法追加）"
                                        :disabled="!(stateMap.get(params!.state as string) ?? true)"
                                        class="note-input"
                                        :error="item.zx_order_type === '1'"
                                    >
                                        <template #right>
                                            <i class="i-bx-edit-alt note-edit-icon"></i>
                                        </template>
                                    </nut-input>
                                </div>

                                <!-- 数量控制区域 -->
                                <div class="quantity-section">
                                    <div class="quantity-label">数量</div>
                                    <div class="quantity-control">
                                        <nut-input-number
                                            v-model="item.product_qty"
                                            :min="1"
                                            :max="200000"
                                            @blur="countTotalPrice()"
                                            @add="() => plus(item.line_id)"
                                            @reduce="() => minus(item.line_id)"
                                            class="custom-input-number"
                                        />
                                    </div>
                                </div>
                            </div>
                            <template #right>
                                <div class="swipe-actions">
                                    <nut-button
                                        class="delete-btn"
                                        @click="() => { showRmDialog(item.line_id) }"
                                    >
                                        <i class="i-bx-trash"></i>
                                        <span>删除</span>
                                    </nut-button>
                                </div>
                            </template>
                        </nut-swipe>
                    </nut-swipe-group>
                </template>

                <!-- 空状态 -->
                <template v-else>
                    <div class="empty-products">
                        <div class="empty-icon">
                            <i class="i-bx-package"></i>
                        </div>
                        <p class="empty-text">暂无商品</p>
                    </div>
                </template>
            </div>
        </div>

        <!-- 新增产品按钮 -->
        <nut-fixed-nav type="right" :position="{ top: '240px' }" @click="addOrder"
            v-show="stateMap.get(params!.state as string) ?? true">
            <template #btn>
                <span class="text">{{ "新增产品" }}</span>
            </template>
        </nut-fixed-nav>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>

        <!-- 优化后的底部操作栏 -->
        <div class="bottom-action-bar" v-show="stateMap.get(params!.state as string) ?? true">
            <div class="action-bar-content">
                <!-- 价格统计区域 -->
                <div class="price-summary">
                    <div class="total-price-section">
                        <span class="total-label">合计:</span>
                        <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
                    </div>
                    <div class="total-count-section">
                        <span class="count-label">共</span>
                        <span class="count-value">{{ countJS }}</span>
                        <span class="count-unit">件</span>
                    </div>
                </div>

                <!-- 保存修改按钮 -->
                <div class="submit-section">
                    <nut-button
                        type="primary"
                        @click="submitOrderF"
                        class="submit-btn"
                    >
                        <i class="i-bx-save"></i>
                        <span>保存修改</span>
                    </nut-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getOrderMessage, getProductByNumber, updateOrder } from '@/service/index'
import Taro from '@tarojs/taro';
import { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { onMounted, onUnmounted, ref, Ref, watch } from 'vue';
import { useAppStore } from '@/store';
const appStore = useAppStore();
import { toRaw } from "@vue/reactivity";
import Address from '@/components/address/index.vue'
import AddressParse from '@/package/package-a/harvestAddress/dist/zh-address-parse.min.js'
const cartCommodities: Ref<Array<{
    line_id: number,
    code: string,
    name: string,
    spec: string,
    model: string,
    price_qty: number,
    product_image: string,
    product_qty: number,
    checkbox: boolean,
    price_unit: number,
    seiban: string | undefined,
    zx_line_notes: string | undefined,
    fanhao_id: string | undefined,
    price_total: number | string,
    note: string,
    product_id: string,
    project: string,
     zx_order_type: string | number | undefined

}>> = ref([
])
interface IharvestData {
    addressName: string,
    phone: string,
    fullAddress: string
}
//长按事件 - 单个产品
const getHarvestData = (item: number) => {
    // 显示单个产品操作选项
    Taro.showActionSheet({
        itemList: [
            '📋 复制单个产品信息',
            '📄 复制全部订单详情',
            '📊 生成订单Excel',
            '🗑️ 删除此产品'
        ],
        success: function (res) {
            switch (res.tapIndex) {
                case 0:
                    copySingleProductInfo(item);
                    break;
                case 1:
                    copyAllOrderDetails();
                    break;
                case 2:
                    generateOrderExcel();
                    break;
                case 3:
                    showRmDialog(item);
                    break;
            }
        },
        fail: function (err) {
            if (!err.errMsg?.includes('cancel')) {
                console.error('ActionSheet显示失败:', err);
            }
        }
    });
}

// 长按复制全部订单详情功能
const onLongPressOrderList = () => {
    console.log('长按订单列表');

    // 触觉反馈
    Taro.vibrateShort({
        type: 'medium'
    });

    // 显示操作选项
    Taro.showActionSheet({
        itemList: [
            '📄 复制订单详细信息',
            '📊 生成订单Excel并打开',
        ],
        success: function (res) {
            switch (res.tapIndex) {
                case 0:
                    copyOrderDetailedInfo();
                    break;
                case 1:
                    generateOrderExcel();
                    break;
            }
        },
        fail: function (err) {
            if (err.errMsg && err.errMsg.includes('cancel')) {
                console.log('用户取消了操作');
            } else {
                console.error('ActionSheet显示失败:', err);
                Taro.showToast({
                    title: '操作失败',
                    icon: 'error',
                    duration: 1500
                });
            }
        }
    });
}

// 复制单个产品信息
const copySingleProductInfo = (lineId: number) => {
    const product = cartCommodities.value.find(item => item.line_id === lineId);
    if (!product) {
        Taro.showToast({
            title: '产品信息不存在',
            icon: 'error',
            duration: 1500
        });
        return;
    }

    const productInfo = `产品型号: ${product.model}
产品名称: ${product.name}
产品规格: ${product.spec}
产品编号: ${product.code}
单价: ¥${product.price_unit}/PCS
数量: ${product.product_qty}
小计: ¥${(product.price_unit * product.product_qty).toFixed(2)}
商品备注: ${product.zx_line_notes || '无'}
设计图案: ${product.seiban || '无'}
订单类型:${product.zx_order_type}
`;


    copyToClipboard(productInfo, '产品信息');
}

// 复制全部订单详情
const copyAllOrderDetails = () => {
    copyOrderDetailedInfo();
}

// 复制订单基本信息
const copyOrderBasicInfo = () => {
    const currentTime = new Date().toLocaleString('zh-CN');

    const basicInfo = `=== 要货订单基本信息 ===
订单编号: ${OrderId.value}
项目名称: ${project.value || '未填写'}
导出时间: ${currentTime}

收货信息:
联系人: ${harvestData.value[0]?.addressName || '未设置'}
联系电话: ${harvestData.value[0]?.phone || '未设置'}
收货地址: ${harvestData.value[0]?.fullAddress || '未设置'}

订单统计:
商品总数: ${cartCommodities.value.length}款
商品总量: ${countJS.value}件
订单总金额: ¥${totalPrice.value.toFixed(2)}

备注信息: ${note.value || '无'}

技术支持 © 广东左向科技有限公司`;

    copyToClipboard(basicInfo, '订单基本信息');
}

// 复制订单详细信息
const copyOrderDetailedInfo = () => {
    const currentTime = new Date().toLocaleString('zh-CN');

    let productDetails = '';
    cartCommodities.value.forEach((item, index) => {
        productDetails += `
${index + 1}. ${item.name}
   型号: ${item.model}
   规格: ${item.spec}
   编号: ${item.code}
   单价: ¥${item.price_unit}/PCS
   数量: ${item.product_qty}
   小计: ¥${(item.price_unit * item.product_qty).toFixed(2)}
   备注: ${item.zx_line_notes || '无'}
   图案: ${item.seiban || '无'}
   订单类型:${item.zx_order_type}
   `;
    });

    const detailedInfo = `=== 要货订单详细信息 ===
导出时间: ${currentTime}

订单信息:
订单编号: ${OrderId.value}
项目名称: ${project.value || '未填写'}
订单状态: ${params?.state === 'Done' ? '已完成' : '进行中'}

收货信息:
联系人: ${harvestData.value[0]?.addressName || '未设置'}
联系电话: ${harvestData.value[0]?.phone || '未设置'}
收货地址: ${harvestData.value[0]?.fullAddress || '未设置'}

商品详情:${productDetails}

订单统计:
商品总数: ${cartCommodities.value.length}款
商品总量: ${countJS.value}件
订单总金额: ¥${totalPrice.value.toFixed(2)}

备注信息: ${note.value || '无'}

技术支持 © 广东左向科技有限公司`;

    copyToClipboard(detailedInfo, '订单详细信息');
}

// 复制到剪贴板
const copyToClipboard = (text: string, type: string) => {
    if (!text || text.trim() === '') {
        Taro.showToast({
            title: '暂无相关信息',
            icon: 'none',
            duration: 1500
        });
        return;
    }

    Taro.setClipboardData({
        data: text,
        success: function () {
            // 触觉反馈
            Taro.vibrateShort({
                type: 'light'
            });

            Taro.showToast({
                title: `${type}已复制到剪贴板`,
                icon: 'success',
                duration: 2000
            });

            console.log(`已复制${type}:`, text);
        },
        fail: function (err) {
            console.error('复制失败:', err);
            Taro.showToast({
                title: '复制失败，请重试',
                icon: 'error',
                duration: 2000
            });
        }
    });
}

// 生成订单Excel并打开
const generateOrderExcel = async () => {
    try {
        Taro.showLoading({
            title: '生成Excel中...',
            mask: true
        });

        // 构建Excel数据
        const excelData = buildOrderExcelData();

        // 生成Excel文件
        const excelContent = generateExcelContent(excelData);

        // 创建并下载Excel文件
        const fileName = `订单详情_${OrderId.value}_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`;
        await createAndDownloadExcel(excelContent, fileName);

        Taro.hideLoading();

        Taro.showToast({
            title: 'Excel文件已生成',
            icon: 'success',
            duration: 2000
        });

    } catch (error) {
        console.error('生成Excel失败:', error);
        Taro.hideLoading();
        Taro.showToast({
            title: '生成Excel失败，已复制详细信息',
            icon: 'none',
            duration: 2000
        });
        // 降级到复制详细信息
        copyOrderDetailedInfo();
    }
}

// 构建Excel数据
const buildOrderExcelData = () => {
    const currentTime = new Date().toLocaleString('zh-CN');

    // 订单基本信息
    const orderInfo = [
        ['订单编号', OrderId.value],
        ['项目名称', project.value || '未填写'],
        ['订单状态', params?.state === 'Done' ? '已完成' : '进行中'],
        ['导出时间', currentTime],
        ['备注信息', note.value || '无']
    ];

    // 收货信息
    const addressInfo = [
        ['联系人', harvestData.value[0]?.addressName || '未设置'],
        ['联系电话', harvestData.value[0]?.phone || '未设置'],
        ['收货地址', harvestData.value[0]?.fullAddress || '未设置']
    ];

    // 商品详情表头
    const productHeaders = [
        '序号', '产品型号', '产品名称', '产品规格', '产品编号',
        '单价(元)', '数量', '小计(元)', '商品备注', '设计图案'
    ];

    // 商品详情数据
    const productData = cartCommodities.value.map((item, index) => [
        index + 1,
        item.model,
        item.name,
        item.spec,
        item.code,
        item.price_unit,
        item.product_qty,
        (item.price_unit * item.product_qty).toFixed(2),
        item.zx_line_notes || '无',
        item.seiban || '无'
    ]);

    // 订单统计
    const summaryInfo = [
        ['商品总数', `${cartCommodities.value.length}款`],
        ['商品总量', `${countJS.value}件`],
        ['订单总金额', `¥${totalPrice.value.toFixed(2)}`]
    ];

    return {
        orderInfo,
        addressInfo,
        productHeaders,
        productData,
        summaryInfo,
        company: '广东左向科技有限公司'
    };
}

// 生成Excel内容
const generateExcelContent = (data: any) => {
    // 使用CSV格式作为Excel的简化版本，在小程序中更容易处理
    let csvContent = '';

    // 添加BOM以支持中文
    csvContent += '\uFEFF';

    // 订单基本信息
    csvContent += '订单基本信息\n';
    data.orderInfo.forEach((row: any[]) => {
        csvContent += `${row[0]},${row[1]}\n`;
    });
    csvContent += '\n';

    // 收货信息
    csvContent += '收货信息\n';
    data.addressInfo.forEach((row: any[]) => {
        csvContent += `${row[0]},${row[1]}\n`;
    });
    csvContent += '\n';

    // 商品详情
    csvContent += '商品详情\n';
    csvContent += data.productHeaders.join(',') + '\n';
    data.productData.forEach((row: any[]) => {
        csvContent += row.join(',') + '\n';
    });
    csvContent += '\n';

    // 订单统计
    csvContent += '订单统计\n';
    data.summaryInfo.forEach((row: any[]) => {
        csvContent += `${row[0]},${row[1]}\n`;
    });
    csvContent += '\n';

    // 公司信息
    csvContent += `技术支持,© ${data.company}\n`;

    return csvContent;
}

// 创建并下载Excel文件
const createAndDownloadExcel = async (csvContent: string, filename: string) => {
    try {
        // 在小程序环境中，我们使用文件系统API来保存文件
        const fs = Taro.getFileSystemManager();

        // 清理文件名，移除特殊字符和路径分隔符
        const cleanFileName = filename.replace(/[\/\\:*?"<>|]/g, '_');

        // 创建临时文件路径
        const tempFilePath = `${Taro.env.USER_DATA_PATH}/${cleanFileName}`;

        console.log('准备创建文件:', tempFilePath);

        // 将CSV内容写入文件
        await new Promise((resolve, reject) => {
            fs.writeFile({
                filePath: tempFilePath,
                data: csvContent,
                encoding: 'utf8',
                success: (res) => {
                    console.log('文件写入成功:', res);
                    resolve(res);
                },
                fail: (err) => {
                    console.error('文件写入失败:', err);
                    reject(err);
                }
            });
        });

        // 打开文件
        await Taro.openDocument({
            filePath: tempFilePath,
            showMenu: true,
            success: (res) => {
                console.log('文件打开成功:', res);
            },
            fail: (err) => {
                console.error('文件打开失败:', err);
                throw err;
            }
        });

        console.log('Excel文件已生成并打开:', tempFilePath);

    } catch (error) {
        console.error('创建Excel文件失败:', error);

        // 降级处理：复制CSV内容到剪贴板
        Taro.setClipboardData({
            data: csvContent,
            success: () => {
                Taro.showToast({
                    title: 'Excel数据已复制到剪贴板',
                    icon: 'success',
                    duration: 2000
                });
            },
            fail: () => {
                Taro.showToast({
                    title: '文件生成失败',
                    icon: 'error',
                    duration: 2000
                });
            }
        });
    }
}

// 分享订单信息
const shareOrderInfo = () => {
    const shareContent = `【要货订单分享】
订单编号: ${OrderId.value}
项目名称: ${project.value || '未填写'}
商品总数: ${cartCommodities.value.length}款
商品总量: ${countJS.value}件
订单总金额: ¥${totalPrice.value.toFixed(2)}

来自广东左向科技有限公司`;

    // 先复制到剪贴板
    copyToClipboard(shareContent, '订单分享信息');

    // 然后尝试调用分享
    Taro.showShareMenu({
        withShareTicket: true
    });
}
//楼层字段变更
const show = ref(false)
const click = () => {
    show.value = true
}
const searchValue = ref('')
const showSelect = ref(false)
const searchList: Ref<any[]> = ref([])
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)

//删除订单事件
let tempId = -1
const isRmCommodityDialogShow = ref(false)
const showRmDialog = (id: number) => {
    console.log("id", id);

    tempId = id
    isRmCommodityDialogShow.value = true
}
let removeItemArray: Ref<Array<object>> = ref([])
const removeCommodity = () => {
    cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.line_id === tempId), 1)
    removeItemArray.value.push({ "line_id": tempId })
    countTotalPrice()
}

const reCCard = () => {
    // 取消删除操作
    isRmCommodityDialogShow.value = false
}


const GetInputFocus = () => {
    if (searchValue.value.length >= 0) {
        show.value = true
        if (searchValue.value.length > 0) {
            getProductByNumberF(searchValue)
        } else {
            searchList.value = []
            DQsearchListIndex.value = 1
        }

    } else {
        show.value = false
    }

}
const getProductByNumberF = async (item: Ref<string>) => {
    const { error, success } = await getProductByNumber({ name: item.value, per_page: '20' })
    console.log('success                  ~~~', success);
    const { items, psize } = success as { items: Array<any>, psize: number }
    if (success && items) {
        searchList.value = items
        DQsearchListIndex.value = psize
    }
}
//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
    console.log('子组件返回值:id', msg.id);
    console.log('子组件返回值:index', msg.index);
    ClickshowDialogItem = msg.id
    click()
}

const clickItem = (item: any) => {
    const { name, id } = item
    if (ClickshowDialogItem) {
        cartCommodities.value.find(item => {
            if (item.line_id === ClickshowDialogItem) {
                item.seiban = name
                item.fanhao_id = id
            }
        })
        ClickshowDialogItem = null
        searchValue.value = ''
        console.log('cartCommodities', cartCommodities.value);
        show.value = false
        searchList.value = []


    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'error',
            duration: 2000
        })
    }

}
const onScrollBottom = async () => {
    console.log('触底了');
    console.log('searchListIndex', searchListIndex.value);
    console.log('DQsearchListIndex', DQsearchListIndex.value);


    if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
        searchListIndex.value++
        const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
        if (error === null) {
            const { items, psize } = success as { items: Array<any>, psize: number }
            if (success && items) {
                searchList.value = searchList.value.concat(items)
                DQsearchListIndex.value = psize
            }
        }
    } else {
        Taro.showToast({
            title: '加载完毕了~',
            icon: 'error',
            duration: 2000
        })
    }

}

const stateMap = ref(new Map([
    ['null', true],
    ['Done', false]
]))

const addOrder = () => {
    let existingProductList = cartCommodities.value.map(i => { return { id: i.product_id, name: i.name } })


    // if(toRaw(checkList.value)[0]!=undefined){
    //     existingProductList.push(...toRaw(checkList.value)[0].map((i: { id: any; name: any; })=>{
    //     console.log(i,'1111');

    //     return {
    //         id:i.id,
    //         name:i.name
    //     }
    // }))
    // }
    // console.log(toRaw(checkList.value)[0],'1234');



    Taro.preload({
        existingProductList,
        OrderId: OrderId.value
    })
    Taro.navigateTo(
        {
            url: '/package/package-a/search/index'
        }
    )

}



const params = getCurrentInstance().router!.params

const minus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            if (id === item.line_id) {
                if (item.product_qty !== 1) {
                    item.product_qty -= 1
                    item.price_total = item.product_qty * item.price_unit
                } else {
                    Taro.showToast({
                        title: '最少购买一个商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const plus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            console.log("item------", id, item.line_id, item.product_qty);

            if (id === item.line_id) {
                if (item.product_qty !== 999) {

                    item.product_qty = Number(item.product_qty) + 1
                    item.price_total = item.product_qty * item.price_unit
                } else {
                    Taro.showToast({
                        title: '当前最多仅能购买' + 999 + '份该商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const OrderId = ref('')
const addressOrder = ref('')
//备注及特殊发货要求
const note = ref('')
const project = ref('')
// options为可选参数，不传默认使用正则查找
const options = {
    type: 0, // 哪种方式解析，0：正则，1：树查找
    textFilter: [], // 预清洗的字段
    nameMaxLength: 4, // 查找最大的中文名字长度
}

const getOrderMessageF = async (id: string | number) => {
    console.log("------", id);
    const { error, success }: { error: any, success: any } = await getOrderMessage({ id })

    console.log('======', error, success);
    if (error == null && success) {
        //地址赋值

        if (success.address.length > 0) {
            const parseResult = AddressParse(success.address, options)
            harvestData.value[0].addressName = parseResult.name
            harvestData.value[0].phone = parseResult.phone
            harvestData.value[0].fullAddress = parseResult.province + parseResult.city + parseResult.area + parseResult.detail

        } else {
            harvestData.value[0].addressName = '采用U9默认联系地址'
        }

        //备注赋值
        note.value = success.notes
        project.value = success.project
        OrderId.value = success.id
        addressOrder.value = success.address
        //商品列表渲染
        // cartCommodities
        cartCommodities.value = success.order_lines

        note.value = success.notes

        // cartCommodities.value.map(async i => {

        //         // console.log(success);
        //         i.image = success.order_lines.product_image





        // })

        countTotalPrice()

    }
}
const item = ref()
// let checkList: Ref<Array<any>> = ref([])
let addItemArray: Ref<Array<any>> = ref([])
let updateItemArray: Ref<Array<any>> = ref([])


const enable = (updateAddress: boolean = false) => {
    // console.log('~~~~~', removeItemArray.value.length, addItemArray.value.length, updateItemArray.value.length);

    if (removeItemArray.value.length > 0 || addItemArray.value.length > 0 || updateItemArray.value.length > 0 || updateAddress) {
        Taro.enableAlertBeforeUnload({
            message: '您有修改数据还未保存，确定要直接离开吗？',
            complete: () => {
            }
        })
    }


}
let watchResult = null
onMounted(() => {
    // cartCommodities.value.push(...preloadMessage!.message)
    eventCenter.on(getCurrentInstance().router!.onShow, async () => {



        await getOrderMessageF(params.id as any)
        // let pages = Taro.getCurrentPages();
        // let currentPage = pages[pages.length - 1]; // 获取当前页面
        // if (currentPage.__data__.checkList && currentPage.__data__.checkList.length > 0) { // 获取值


        //     checkList.value.push(JSON.parse(JSON.stringify(currentPage.__data__.checkList)))
        //     console.log('checkList', checkList);
        //     //清空
        //     currentPage.__data__.checkList = []




        // }
        //拿到新增数组
        // if (checkList.value.length > 0) {
        //     addItemArray.value = checkList.value.reduce((a, b) => a.concat(b)).map(i => {
        //         return {
        //             product_id: i.id,
        //             product_qty: i.product_qty,
        //             price_unit: i.price_unit,
        //             fanhao_id: i.fanhao_id,
        //             zx_line_notes: i.zx_line_notes,
        //             zx_mfplx: i.zx_mfplx

        //         }
        //     })
        // }
        countTotalPrice()
        // enable(true)
        watchResult = watch([cartCommodities, note, project, removeItemArray, updateItemArray, addItemArray, harvestData], (n, o) => {
            console.log('变化了', n, o);
            enable(true)


        }, { deep: true })
        Taro.disableAlertBeforeUnload({
            success: function (res) {
                console.log("成功&&：", res);
            },
            fail: function (err) {
                console.log("失败&&：", err);
            }
        })




    })
})


onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)


})


//控制弹出层
const popupShow = ref(false)
const formRef = ref(null)
const editClick = () => {
    console.log('Click To Edit')
    popupShow.value = true
}
const formData: Ref<{
    addressName: string,
    phone: string,
    fullAddress: string
}> = ref({
    addressName: '',
    phone: '',
    fullAddress: ''
})
const validatorName = () => {
    if (/^([\u4e00-\u9fa5]{2,6})$/gi.test(formData.value.addressName)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的姓名')
    }
}
const validatorPhone = () => {
    console.log('formData.value.phone', formData.value.phone, /^1[3-9]\d{9}$/.test(formData.value.phone));

    if (/^1[3-9]\d{9}$/.test(formData.value.phone)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的11位手机号')
    }
}
const validatorAddress = () => {
    console.log('formData.value.fullAddress', formData.value.fullAddress, /^[\u4e00-\u9fa5]{2,10}$/gi.test(formData.value.fullAddress));
    if (/^[\u4e00-\u9fa5a-zA-Z0-9!@#\$%\^&*()_+\-=\[\]{};':"\\|,.<>\/?~` ]{2,30}$/.test(formData.value.fullAddress)) {
        return Promise.resolve()
    } else {
        return Promise.reject('联系地址应尽量详实')
    }
}
const submitAddress = () => {

    formRef.value?.validate().then(({ valid, errors }) => {
        if (valid) {
            Taro.showToast({
                title: '暂存成功',
                success: () => {
                    console.log(Object.assign(harvestData.value[0], formData.value));
                    formData.value as Object
                    popupShow.value = false
                },
                duration: 2000
            })

        } else {
            console.warn('error:', errors)
        }
    })

}

//创建要货订单
const createOrder = () => {
}
// {
//     "product_id": 1564,
//     "product_qty": 10,
//     "price_unit": 30,
//     "fanhao_id": 1,
//     "zx_line_notes": "行内备注",
//     "zx_mfplx": "1"
// },
const submitOrderF = async () => {
    //拿到删除数组
    console.log('删除数组', removeItemArray.value);

    //拿到更新数组
    //提交前校验
    updateItemArray.value = cartCommodities.value.map(i => {
        return {
            product_id: i.product_id,
            product_qty: i.product_qty,
            price_unit: i.price_unit,
            fanhao_id: i.fanhao_id,
            zx_line_notes: i.zx_line_notes,
            zx_mfplx: '',
            line_id: i.line_id

        }
    })
    console.log('更新数组', updateItemArray.value);


    console.log('新增数组', addItemArray.value, JSON.stringify(addItemArray.value))

    //拿到行内备注及特殊发货要求
    console.log('拿到行内备注及特殊发货要求', note.value);
    let designConfirm = cartCommodities.value.find(el =>
        el.name.includes('特殊图案') && !('seiban' in el)
    )
    if (designConfirm) {
        Taro.showModal({
            title: '提示',
            content: '特殊图案产品需选择楼层图案后才可下单',
            success: function () {
            }
        })


    }
    else {
        const { error, success } = await updateOrder({
            ...(!['请输入姓名', '采用U9默认联系地址'].includes(harvestData.value?.[0]?.addressName) && { address: `${harvestData.value[0].fullAddress}${harvestData.value[0].phone}${harvestData.value[0].addressName}` }),
            note: note.value,
            project: project.value.length > 0 ? project.value : '未填写',
            add_lines: JSON.stringify(addItemArray.value),
            mod_lines: JSON.stringify(updateItemArray.value),
            del_lines: JSON.stringify(removeItemArray.value)
        }, {
            id: OrderId.value

        })
        if (error === null && success) {
            console.log('success---', success);
            Taro.showToast({
                title: '提交成功',
                success: () => {
                    setTimeout(() => {
                        Taro.switchTab({
                            url: '/pages/orders/index',
                            success: () => {
                                appStore.setActiveTab('/pages/orders/index');
                            }
                        })
                    }, 2000)

                },
                duration: 2000
            })

        } else {
            Taro.showToast({
                title: error!.message,
                icon: 'error',
                duration: 2000
            })
        }
    }





}

const harvestData: Ref<Array<IharvestData>> = ref([{
    addressName: '',
    phone: '',
    fullAddress: '',
}
])
const harvestOptions = {
    addressName: 'addressName',
    phone: 'phone',
    fullAddress: 'fullAddress'
}



const countTotalPrice = () => {
    totalPrice.value = cartCommodities.value.reduce((acc, curr) => acc + curr.price_unit * curr.product_qty, 0);
    countJS.value = cartCommodities.value
        .reduce((acc, curr) => Number(acc) + Number(curr.product_qty), 0);

    if (addItemArray.value.length > 0) {
        totalPrice.value += addItemArray.value.reduce((a, b) => a + b.product_qty * b.price_unit, 0)
    }
}
const totalPrice = ref(0)
const countJS = ref(0)

// 处理订单类型变化
const handleOrderTypeChange = (value: string, item: any) => {
    console.log('订单类型变化:', {
        productName: item.name,
        productId: item.id,
        oldType: item.zx_order_type,
        newType: value,
        typeName: value === '0' ? '标准做法' : '特殊做法'
    });

    // 更新商品的订单类型
    item.zx_order_type = value;

    // 触觉反馈
    Taro.vibrateShort({
        type: 'light'
    });
}

/** 设置页面属性 */
definePageConfig({
    navigationBarTitleText: '要货订单',
});

</script>

<style lang="scss">
// 订单类型选择框样式
.order-type-section {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .order-type-label {
        font-size: 14px;
        font-weight: 600;
        color: #122F38;
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            width: 3px;
            height: 14px;
            background: #FF6B35;
            border-radius: 2px;
            margin-right: 6px;
        }
    }

    .nut-radio-group {
        display: flex;
        gap: 16px;

        .order-type-radio {
            flex: 1;

            .nut-radio {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background: #fff;
                transition: all 0.3s ease;
                font-size: 13px;

                &:hover {
                    border-color: #122F38;
                    background: rgba(18, 47, 56, 0.05);
                }

                &.nut-radio--checked {
                    border-color: #122F38;
                    background: rgba(18, 47, 56, 0.1);
                    color: #122F38;
                    font-weight: 600;

                    .nut-radio__icon {
                        color: #122F38;
                    }
                }

                .nut-radio__label {
                    margin-left: 6px;
                    font-size: 13px;
                }
            }
        }
    }
}
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$info-color: #2196F3;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 页面基础样式
page {
    background: $background-light;
}

.update-order-page {
    min-height: 100vh;
    background: $background-light;
    padding-bottom: 80px;
}

// 楼层搜索弹窗样式
.floor-search-sheet {
    --nutui-actionsheet-border-radius: 16px 16px 0 0;

    .search-container {
        padding: 20px;
        background: #fff;

        .search-header {
            text-align: center;
            margin-bottom: 20px;

            .search-title {
                font-size: 18px;
                font-weight: 600;
                color: $text-primary;
                margin: 0 0 4px 0;
            }

            .search-subtitle {
                font-size: 14px;
                color: $text-secondary;
                margin: 0;
            }
        }

        .custom-searchbar {
            --nutui-searchbar-background: #{$primary-light};
            --nutui-searchbar-input-background: #fff;
            --nutui-searchbar-input-text-color: #{$text-primary};
            margin-bottom: 16px;
        }

        .search-results-container {
            .search-results {
                .results-list {
                    max-height: 280px;
                    overflow-y: auto;

                    .floor-item {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 12px 16px;
                        background: #fff;
                        border-radius: 8px;
                        margin-bottom: 8px;
                        border: 1px solid $border-color;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover, &:active {
                            background: $primary-light;
                            border-color: $primary-color;
                        }

                        .floor-info {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .floor-icon {
                                font-size: 16px;
                                color: $primary-color;
                            }

                            .floor-name {
                                font-size: 14px;
                                color: $text-primary;
                                font-weight: 500;
                            }
                        }

                        .select-icon {
                            font-size: 16px;
                            color: $text-light;
                        }
                    }
                }

                .empty-search {
                    text-align: center;
                    padding: 40px 20px;

                    .empty-icon {
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: $primary-light;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;

                        i {
                            font-size: 24px;
                            color: $primary-color;
                        }
                    }

                    .empty-text {
                        font-size: 14px;
                        color: $text-secondary;
                        margin: 0;
                    }
                }
            }
        }
    }
}

// 收货地址区域样式
.address-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .section-icon {
                font-size: 18px;
                color: #fff;
            }

            .section-title {
                font-size: 16px;
                font-weight: 600;
                color: #fff;
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;

            &:active {
                background: rgba(255, 255, 255, 0.2);
            }

            &.disabled {
                opacity: 0.5;
                pointer-events: none;
            }

            .edit-text {
                font-size: 14px;
                color: #fff;
                font-weight: 500;
            }

            .edit-icon {
                font-size: 14px;
                color: #fff;
            }
        }
    }

    .address-content {
        padding: 16px;

        .address-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: $primary-light;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
                font-size: 16px;
                color: $primary-color;
            }
        }
    }
}

// 项目信息区域样式
.project-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }
    }

    .project-content {
        padding: 16px;

        .input-group {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            .input-label {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 8px;

                span {
                    font-size: 14px;
                    color: $text-primary;
                    font-weight: 500;
                }

                .required-mark {
                    color: $error-color;
                    font-weight: 600;
                }
            }

            .custom-textarea {
                --nutui-textarea-border-color: #{$border-color};
                --nutui-textarea-border-radius: 8px;
                --nutui-textarea-text-color: #{$text-primary};
                --nutui-textarea-placeholder-color: #{$text-light};
                width: 100%;

                // 修复文本域高度显示问题
                .nut-textarea {
                    width: 100% !important;
                    min-height: 60px !important;
                }

                .nut-textarea__textarea {
                    width: 100% !important;
                    min-height: 60px !important;
                    line-height: 1.5 !important;
                    padding: 8px 12px !important;
                    box-sizing: border-box !important;
                    resize: none !important;
                    word-wrap: break-word !important;
                }

                &.project-textarea {
                    --nutui-textarea-border-color: #{$primary-color};

                    .nut-textarea {
                        min-height: 80px !important;
                    }

                    .nut-textarea__textarea {
                        min-height: 80px !important;
                        font-weight: 500 !important;
                    }
                }

                &.note-textarea {
                    .nut-textarea {
                        min-height: 100px !important;
                    }

                    .nut-textarea__textarea {
                        min-height: 100px !important;
                    }
                }

                &:disabled {
                    --nutui-textarea-background-color: #{$background-light};
                    --nutui-textarea-text-color: #{$text-light};
                }
            }
        }
    }
}

// 商品列表区域样式
.products-section {
    background: #fff;
    border-radius: 12px;
    margin: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;

    .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px;
        border-bottom: 1px solid $border-color;
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);

        .section-icon {
            font-size: 18px;
            color: #fff;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .product-count {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: auto;
        }

        // 长按复制提示
        .copy-hint {
            position: absolute;
            top: 8px;
            right: 80px;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            font-size: 10px;
            color: $text-secondary;
            font-weight: 500;
            z-index: 5;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            opacity: 0.9;
            transition: all 0.3s ease;

            i {
                font-size: 12px;
                color: $primary-color;
            }

            span {
                white-space: nowrap;
                color: $primary-color;
            }

            // 悬停效果
            .products-section:hover & {
                opacity: 1;
                transform: scale(1.05);
            }

            // 动画效果
            animation: orderCopyHintPulse 4s ease-in-out infinite;
        }
    }

    .products-content {
        .product-swipe-group {
            .product-swipe {
                margin-bottom: 0;
                border-bottom: 1px solid $border-color;

                &:last-child {
                    border-bottom: none;
                }

                .product-card {
                    display: flex;
                    flex-direction: column;
                    padding: 10px;
                    gap: 6px;

                    // 商品主要信息区域（图片+基本信息）
                    .product-main-info {
                        display: flex;
                        gap: 8px;

                        .product-image-section {
                            flex-shrink: 0;

                            .image-container {
                                position: relative;
                                width: 60px;
                                height: 60px;
                                border-radius: 6px;
                                overflow: hidden;
                                background: $background-light;

                                .product-image {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                }

                                .image-overlay {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    background: $background-light;

                                    i {
                                        font-size: 18px;
                                        color: $text-light;
                                    }
                                }
                            }
                        }

                        .product-info-section {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 4px;
                            min-width: 0;

                            .product-title-area {
                                display: flex;
                                flex-direction: column;
                                gap: 2px;

                                .model-tag {
                                    display: flex;
                                    align-items: center;
                                    gap: 2px;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: $primary-light;
                                    border: 1px solid $primary-color;
                                    width: fit-content;

                                    i {
                                        font-size: 12px;
                                        color: $primary-color;
                                    }

                                    span {
                                        font-size: 12px;
                                        color: $primary-color;
                                        font-weight: 600;
                                        word-break: break-all;
                                        line-height: 1.2;
                                    }
                                }

                                .name-tag {
                                    display: block;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(76, 175, 80, 0.1);
                                    border: 1px solid $success-color;
                                    font-size: 12px;
                                    color: $success-color;
                                    font-weight: 600;
                                    word-break: break-all;
                                    line-height: 1.2;
                                    width: fit-content;
                                }

                                .sub-name-tag {
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    background: rgba(255, 107, 53, 0.1);
                                    font-size: 11px;
                                    color: $accent-color;
                                    font-weight: 500;
                                    word-break: break-all;
                                    line-height: 1.2;
                                    width: fit-content;
                                }
                            }

                            .product-spec {
                                .spec-text {
                                    font-size: 12px;
                                    color: $text-secondary;
                                    background: $background-light;
                                    padding: 2px 6px;
                                    border-radius: 3px;
                                    display: inline-block;
                                    word-break: break-all;
                                    line-height: 1.3;
                                }
                            }

                            .price-code-section {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;

                                .price-area {
                                    display: flex;
                                    align-items: baseline;
                                    gap: 2px;

                                    .currency {
                                        font-size: 14px;
                                        color: $accent-color;
                                        font-weight: 600;
                                    }

                                    .price {
                                        font-size: 18px;
                                        color: $accent-color;
                                        font-weight: 700;
                                    }

                                    .unit {
                                        font-size: 12px;
                                        color: $text-secondary;
                                        font-weight: 500;
                                    }
                                }

                                .code-area {
                                    .code-tag {
                                        background: $primary-color;
                                        color: #fff;
                                        padding: 2px 6px;
                                        border-radius: 3px;
                                        font-size: 11px;
                                        font-weight: 600;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .empty-products {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: $primary-light;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;

                i {
                    font-size: 32px;
                    color: $primary-color;
                }
            }

            .empty-text {
                font-size: 16px;
                color: $text-secondary;
                margin: 0;
            }
        }
    }
}

// 设计选择组件
.design-cell {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid $border-color;

    &.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
}

// 商品备注区域
.product-note-section {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid $border-color;

    .note-label {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;

        i {
            font-size: 14px;
            color: $primary-color;
        }

        span {
            font-size: 14px;
            color: $text-primary;
            font-weight: 500;
        }
    }

    .note-input {
        --nutui-input-border-color: #{$border-color};
        --nutui-input-border-radius: 6px;
        --nutui-input-text-color: #{$text-primary};
        --nutui-input-placeholder-color: #{$text-light};

        &:disabled {
            --nutui-input-background-color: #{$background-light};
            --nutui-input-text-color: #{$text-light};
        }

        .note-edit-icon {
            color: $text-light;
            font-size: 14px;
        }
    }
}

// 数量控制区域
.quantity-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
    padding: 6px 10px;
    background: $background-light;
    border-radius: 6px;

    .quantity-label {
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
    }

    .quantity-control {
        .custom-input-number {
            --nutui-input-number-button-width: 24px;
            --nutui-input-number-button-height: 24px;
            --nutui-input-number-input-width: 40px;
            --nutui-input-number-input-height: 24px;
            --nutui-input-number-input-font-size: 12px;
            --nutui-input-number-button-background: #{$primary-color};
            --nutui-input-number-button-color: #fff;
            --nutui-input-number-input-border-color: #{$border-color};
        }
    }
}

// 滑动删除按钮
.swipe-actions {
    display: flex;
    align-items: center;
    height: 100%;

    .delete-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 4px;
        background: $error-color;
        color: #fff;
        border: none;
        height: 100%;
        padding: 0 20px;
        font-size: 12px;
        font-weight: 600;

        i {
            font-size: 16px;
        }

        &:active {
            background: darken($error-color, 10%);
        }
    }
}

// 新增产品按钮 - 恢复原版样式
.nut-fixed-nav__btn {
    width: 60px !important;
}

// 底部操作栏样式
.bottom-spacer {
    height: 80px;
}

.bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid $border-color;
    box-shadow: 0 -2px 12px rgba(18, 47, 56, 0.08);
    z-index: 999;

    .action-bar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        gap: 16px;

        .price-summary {
            flex: 1;

            .total-price-section {
                display: flex;
                align-items: baseline;
                gap: 4px;
                margin-bottom: 4px;

                .total-label {
                    font-size: 14px;
                    color: $text-secondary;
                    font-weight: 500;
                }

                .total-price {
                    font-size: 20px;
                    color: $accent-color;
                    font-weight: 700;
                }
            }

            .total-count-section {
                display: flex;
                align-items: baseline;
                gap: 2px;

                .count-label {
                    font-size: 12px;
                    color: $text-secondary;
                }

                .count-value {
                    font-size: 16px;
                    color: $primary-color;
                    font-weight: 600;
                }

                .count-unit {
                    font-size: 12px;
                    color: $text-secondary;
                }
            }
        }

        .submit-section {
            flex-shrink: 0;

            .submit-btn {
                --nutui-button-primary-background-color: #{$primary-color};
                --nutui-button-primary-border-color: #{$primary-color};
                --nutui-button-border-radius: 20px;
                --nutui-button-font-weight: 600;
                --nutui-button-font-size: 16px;
                padding: 0 24px;
                height: 44px;
                display: inline-flex;
                align-items: center;
                gap: 6px;

                i {
                    font-size: 16px;
                }

                &:not(:disabled):active {
                    --nutui-button-primary-background-color: #{$primary-dark};
                }
            }
        }
    }
}

// 对话框样式覆盖
.custom-dialog {
    --nutui-dialog-header-font-weight: 600;
    --nutui-dialog-header-color: #{$text-primary};
    --nutui-dialog-content-color: #{$text-secondary};
    --nutui-dialog-ok-color: #{$primary-color};
    --nutui-dialog-cancel-color: #{$text-light};
}

// 地址列表样式覆盖
.nut-address-list {
    width: 100% !important;

    &:last-child {
        padding-bottom: 0 !important;
    }
}

// 隐藏元素
.noShow {
    visibility: hidden !important;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

// 全局组件样式调整
.nut-cell-group__wrap {
    box-shadow: none;
}

// 价格组件样式调整
.nut-price {
    --nutui-price-symbol-color: #{$accent-color};
    --nutui-price-integer-color: #{$accent-color};
    --nutui-price-decimal-color: #{$accent-color};
}

// 标签组件样式调整
.nut-tag {
    --nutui-tag-border-radius: 4px;
    --nutui-tag-font-weight: 500;
}

// 按钮组件样式调整
.nut-button {
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

// 文本域样式调整
.nut-textarea {
    --nutui-textarea-border-color: #{$border-color};
    --nutui-textarea-border-radius: 8px;
    --nutui-textarea-text-color: #{$text-primary};
    --nutui-textarea-placeholder-color: #{$text-light};
}

// 响应式设计
@media (max-width: 375px) {
    .update-order-page {
        padding-bottom: 70px;
    }

    .address-section,
    .project-section,
    .products-section {
        margin: 8px;
        border-radius: 8px;

        .section-header {
            padding: 12px;

            .section-title {
                font-size: 14px;
            }
        }
    }

    // 小屏幕下的项目信息区域优化
    .project-section {
        .project-content {
            padding: 12px;

            .input-group {
                margin-bottom: 12px;

                .input-label {
                    margin-bottom: 6px;

                    span {
                        font-size: 13px;
                    }
                }

                .custom-textarea {
                    &.project-textarea {
                        .nut-textarea {
                            min-height: 60px !important;
                        }

                        .nut-textarea__textarea {
                            min-height: 60px !important;
                            font-size: 13px !important;
                            padding: 6px 10px !important;
                        }
                    }

                    &.note-textarea {
                        .nut-textarea {
                            min-height: 80px !important;
                        }

                        .nut-textarea__textarea {
                            min-height: 80px !important;
                            font-size: 13px !important;
                            padding: 6px 10px !important;
                        }
                    }
                }
            }
        }
    }

    .product-card {
        padding: 8px !important;
        gap: 4px !important;

        .product-main-info {
            gap: 6px;

            .product-image-section .image-container {
                width: 50px;
                height: 50px;
            }

            .product-info-section {
                gap: 3px;

                .product-title-area {
                    gap: 1px;

                    .model-tag, .name-tag {
                        padding: 1px 4px;
                        font-size: 11px;
                    }

                    .sub-name-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }

                .product-spec .spec-text {
                    font-size: 11px;
                    padding: 1px 4px;
                }

                .price-code-section {
                    .price-area {
                        .currency {
                            font-size: 12px;
                        }

                        .price {
                            font-size: 16px;
                        }

                        .unit {
                            font-size: 10px;
                        }
                    }

                    .code-area .code-tag {
                        padding: 1px 4px;
                        font-size: 10px;
                    }
                }
            }
        }

        .product-note-section {
            margin-top: 3px;
            padding-top: 3px;

            .note-label {
                gap: 4px;
                margin-bottom: 4px;

                i {
                    font-size: 12px;
                }

                span {
                    font-size: 12px;
                }
            }

            .note-input {
                --nutui-input-height: 28px;
                --nutui-input-font-size: 12px;
            }
        }

        .quantity-section {
            margin-top: 3px;
            padding: 4px 8px;

            .quantity-label {
                font-size: 12px;
            }

            .quantity-control .custom-input-number {
                --nutui-input-number-button-width: 20px;
                --nutui-input-number-button-height: 20px;
                --nutui-input-number-input-width: 32px;
                --nutui-input-number-input-height: 20px;
                --nutui-input-number-input-font-size: 11px;
            }
        }

        .design-cell {
            margin-top: 3px;
            padding-top: 3px;
        }
    }

    .bottom-action-bar .action-bar-content {
        padding: 10px 12px;
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
        gap: 12px;

        .price-summary .total-price-section .total-price {
            font-size: 18px;
        }

        .submit-section .submit-btn {
            height: 40px;
            font-size: 14px;
            padding: 0 20px;
        }
    }
}

// 订单复制提示脉冲动画
@keyframes orderCopyHintPulse {
    0%, 100% {
        opacity: 0.9;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

// 响应式设计优化
@media (max-width: 375px) {
    .products-section {
        .section-header {
            .copy-hint {
                top: 6px;
                right: 6px;
                padding: 3px 6px;
                font-size: 9px;
                border-radius: 8px;

                i {
                    font-size: 10px;
                }

                span {
                    font-size: 9px;
                }
            }
        }
    }
}

@media (max-width: 320px) {
    .products-section {
        .section-header {
            .copy-hint {
                top: 4px;
                right: 4px;
                padding: 2px 4px;
                font-size: 8px;
                border-radius: 6px;

                i {
                    font-size: 9px;
                }

                span {
                    font-size: 8px;
                }
            }
        }
    }
}
</style>