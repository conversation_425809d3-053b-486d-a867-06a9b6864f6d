"use strict";(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[257],{2270:function(n,e,t){var a=t(1065),o=t(6944),c=t(889),i=(t(9932),t(8701)),r=(t(959),t(7369)),u=(t(9251),t(9841)),l=(t(30),t(8751)),s=(t(2148),t(2344)),d=(t(2240),t(9328)),p=(t(8277),t(2018)),v=t(2419),f=t(2810),m=t(3221),_=t(7011),h=t(2e3),w=t(6821),x=t(1959),b=t.n(x),g=t(4733),k=t(4081),y={class:"header-container"},Z={class:"header-content"},C={class:"header-title"},T={class:"cart-count"},V={class:"action-text"},H={class:"cart-container"},z=["scroll-top"],W={class:"product-card"},U=["onClick"],B={class:"product-content"},S={class:"product-left"},F={class:"product-image-container"},j=["src"],E={key:0,class:"image-overlay"},q={class:"product-info"},I={class:"product-title-area"},O={class:"model-tag"},A={class:"name-tag"},D={class:"product-spec"},P={class:"spec-text"},R={class:"price-code-section"},J={class:"price-area"},Y={class:"price"},K={class:"code-area"},M={class:"code-tag"},N={class:"quantity-control"},G={key:1,class:"empty-cart-state"},L={class:"empty-content"},Q={class:"bottom-action-bar"},X={class:"action-bar-content"},$={class:"price-section"},nn={class:"total-price"},en={class:"button-section"},tn={class:"bottom-action-bar"},an={class:"action-bar-content"},on={class:"manage-buttons"},cn=(0,m.aZ)({__name:"index",setup:function(n){var e=(0,w.iH)(!1),t=(0,w.iH)(!1),a=(0,w.iH)([]),cn=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(){var e,t,a,o,c;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,g.hT)();case 2:if(e=n.sent,t=e.error,a=e.success,null!==t||!a){n.next=14;break}if(console.log("getFindCartF",a),c=a,Vn.value=(null===(o=c.items)||void 0===o?void 0:o.length)||0,!c.items){n.next=12;break}return n.next=12,Un(c.items.map((function(n){return n.product_id})),c.items);case 12:n.next=15;break;case 14:b().showToast({title:"\u83b7\u53d6\u8d2d\u7269\u8f66\u5931\u8d25",icon:"error",duration:2e3});case 15:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),rn=(0,k.qr)();(0,m.bv)((function(){x.eventCenter.on((0,x.getCurrentInstance)().router.onShow,(function(){rn.setActiveTab("/pages/shoppingCart/index"),cn(),a.value.forEach((function(n){n["checkbox"]=!1})),pn()}))})),(0,m.Ah)((function(){x.eventCenter.off((0,x.getCurrentInstance)().router.onShow)}));var un=(0,w.iH)(0),ln=(0,w.iH)([]),sn=function(){gn.value=!0},dn=-1,pn=function(){un.value=a.value.filter((function(n){return n.checkbox})).reduce((function(n,e){return n+e.price_unit*e.count}),0)},vn=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(){var e,t;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a.value.splice(a.value.findIndex((function(n){return n.id===dn})),1),n.next=3,(0,g.tB)({product_id:-dn});case 3:e=n.sent,t=e.error,null===t?b().showToast({title:"\u5220\u9664\u6210\u529f",icon:"success",duration:2e3}):b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 6:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),fn=function(){var n;null===(n=ln.value[a.value.findIndex((function(n){return n.id===dn}))])||void 0===n||n.close()},mn=function(){var n=a.value.map((function(n){return n.checkbox})).filter((function(n){return n})).length;e.value=n===a.value.length,pn()},_n=(0,w.iH)([]);function hn(n,e){console.log("array",n,e);var t=n.findIndex((function(n){return n[0]===e[0]}));console.log("index",t),-1!==t?n[t]=[n[t][0],n[t][1]+e[1]]:n.push(e)}var wn=function(n){a.value.forEach((function(e){n===e.id&&(1!==e.count?(hn(_n.value,[e.id,-1]),e.count-=1):b().showToast({title:"\u6700\u5c11\u8d2d\u4e70\u4e00\u4e2a\u5546\u54c1~",icon:"none",duration:2e3}))})),pn()},xn=function(n){a.value.forEach((function(e){n===e.id&&(e.count!==e.maxBuy?(hn(_n.value,[e.id,1]),e.count=Number(e.count)+1):b().showToast({title:"\u5f53\u524d\u6700\u591a\u4ec5\u80fd\u8d2d\u4e70"+e.maxBuy+"\u4efd\u8be5\u5546\u54c1~",icon:"none",duration:2e3}))})),pn()},bn=function(){e.value?a.value.forEach((function(n){n.checkbox=!0})):a.value.forEach((function(n){n.checkbox=!1}))},gn=(0,w.iH)(!1),kn=function(){var n=a.value.filter((function(n){return n.checkbox}));console.log("\u8ba2\u5355\u4fe1\u606f",n),b().navigateTo({url:"/package/package-a/confirmOrder/index"}),b().preload({message:n,fromSCartPage:!0})},yn=(0,w.iH)(0),Zn=function(n){console.log("upper:",n)},Cn=function(n){console.log("lower:",n)},Tn=function(n){console.log("scroll:",n)},Vn=(0,w.iH)(0),Hn=(0,w.iH)(!0),zn=(0,w.iH)(new Map([[!0,"\u7ba1\u7406"],[!1,"\u9000\u51fa"]])),Wn=function(){console.log("manage"),Hn.value=!Hn.value},Un=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(e,t){var o,c,i,r;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,g.wv)({ids:e});case 2:o=n.sent,c=o.success,i=c,r=null===i||void 0===i?void 0:i.items,console.log(c,r),c&&r&&(a.value=r.map((function(n){return(0,p.Z)((0,p.Z)({},n),{},{checkbox:!1,maxBuy:1e4,count:1})})),t.forEach((function(n){var e=a.value.find((function(e){return e.id===n.product_id}));e&&(e.count=n.product_qty)})),console.log("cartCommodities.value!!!!",a.value));case 8:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}(),Bn=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(){var e,t,o,c,i;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e=a.value.filter((function(n){return n.checkbox})),console.log(e,"~~~"),t=e.map((function(n){return[-n.id,n.count]})),o="["+t.map((function(n){return"("+n.join(",")+")"})).join(",")+"]",n.next=6,(0,g.tB)({product_ids:o});case 6:if(c=n.sent,i=c.error,null!==i){n.next=15;break}return b().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),n.next=12,cn();case 12:pn(),n.next=16;break;case 15:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 16:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),Sn=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(){var e,t,a;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(0!==_n.value.length){n.next=3;break}return b().showToast({title:"\u6ca1\u6709\u9700\u8981\u4fdd\u5b58\u7684\u4fee\u6539",icon:"none",duration:2e3}),n.abrupt("return");case 3:return e="["+_n.value.map((function(n){return"("+n.join(",")+")"})).join(",")+"]",n.next=6,(0,g.tB)({product_ids:e});case 6:if(t=n.sent,a=t.error,null!==a){n.next=15;break}return b().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),_n.value=[],n.next=13,cn();case 13:n.next=16;break;case 15:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 16:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),Fn=function(){var n=(0,f.Z)((0,v.Z)().mark((function n(e,t){var a,o;return(0,v.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,g.tB)({product_id:-e,product_qty:t});case 2:if(a=n.sent,o=a.error,null!==o){n.next=11;break}return b().showToast({title:"\u5220\u9664\u6210\u529f",icon:"success",duration:2e3}),n.next=8,cn();case 8:pn(),n.next=12;break;case 11:b().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"none",duration:2e3});case 12:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}(),jn=function(){b().switchTab({url:"/pages/index/index"})};return function(n,p){var v=d.Z,f=s.Z,w=l.Z,x=u.Z,b=r.Z,g=i.Z,k=c.Z,cn=o.Z;return(0,m.wg)(),(0,m.iD)(m.HY,null,[(0,m.Wm)(v,{top:"0",class:"header-sticky"},{default:(0,m.w5)((function(){return[(0,m._)("div",y,[(0,m._)("div",Z,[(0,m._)("div",C,[p[5]||(p[5]=(0,m._)("i",{class:"i-bx-cart-alt header-icon"},null,-1)),p[6]||(p[6]=(0,m._)("span",{class:"title-text"},"\u8d2d\u7269\u8f66",-1)),(0,m._)("span",T,"("+(0,_.zw)(Vn.value)+")",1)]),(0,m._)("div",{class:"header-action",onClick:Wn},[p[7]||(p[7]=(0,m._)("i",{class:"i-bx-cog action-icon"},null,-1)),(0,m._)("span",V,(0,_.zw)(zn.value.get(Hn.value)),1)])])])]})),_:1}),(0,m.Wm)(cn,{"show-tab-bar":""},{default:(0,m.w5)((function(){return[(0,m.Wm)(f,{title:"\u63d0\u4ea4\u8ba2\u5355",content:"\u786e\u8ba4\u63d0\u4ea4\u8ba2\u8d27\u4fe1\u606f\u5e76\u751f\u6210\u8ba2\u5355\u5417\uff1f",visible:gn.value,"onUpdate:visible":p[0]||(p[0]=function(n){return gn.value=n}),onOk:kn},null,8,["visible"]),(0,m._)("div",H,[a.value.length>0?((0,m.wg)(),(0,m.j4)(g,{key:0,lock:"",style:{width:"100vw"}},{default:(0,m.w5)((function(){return[(0,m._)("scroll-view",{"scroll-y":!0,style:{height:"calc(100%-58rpx)","margin-bottom":"60rpx"},onScrolltoupper:Zn,onScrolltolower:Cn,onScroll:Tn,"scroll-top":yn.value},[((0,m.wg)(!0),(0,m.iD)(m.HY,null,(0,m.Ko)(a.value,(function(n){return(0,m.wg)(),(0,m.j4)(b,{ref_for:!0,ref_key:"swipeRefs",ref:ln,name:n.id.toString(),key:n.id,class:"product-swipe"},{default:(0,m.w5)((function(){return[(0,m._)("div",W,[(0,m.wy)((0,m._)("div",{class:"delete-btn",onClick:function(e){return Fn(n.id,n.count)}},p[8]||(p[8]=[(0,m._)("i",{class:"i-bx-trash"},null,-1)]),8,U),[[h.F8,!Hn.value]]),(0,m._)("div",B,[(0,m._)("div",S,[(0,m.Wm)(w,{onChange:mn,modelValue:n.checkbox,"onUpdate:modelValue":function(e){return n.checkbox=e},"icon-size":"18",class:"product-checkbox"},null,8,["modelValue","onUpdate:modelValue"]),(0,m._)("div",F,[(0,m._)("image",{src:n.image,class:"product-image"},null,8,j),n.image?(0,m.kq)("",!0):((0,m.wg)(),(0,m.iD)("div",E,p[9]||(p[9]=[(0,m._)("i",{class:"i-bx-image-alt"},null,-1)])))])]),(0,m._)("div",q,[(0,m._)("div",I,[(0,m._)("div",O,[p[10]||(p[10]=(0,m._)("i",{class:"i-bx-crown"},null,-1)),(0,m._)("span",null,(0,_.zw)(n.model),1)]),(0,m._)("div",A,(0,_.zw)(n.name.split("/")[0]),1),(0,m.wy)((0,m._)("div",{class:"sub-name-tag"},(0,_.zw)(n.name.split("/")[1]),513),[[h.F8,n.name.split("/")[1]]])]),(0,m._)("div",D,[(0,m._)("span",P,(0,_.zw)(n.spec),1)]),(0,m._)("div",R,[(0,m._)("div",J,[p[11]||(p[11]=(0,m._)("span",{class:"currency"},"\xa5",-1)),(0,m._)("span",Y,(0,_.zw)(n.price_unit),1),p[12]||(p[12]=(0,m._)("span",{class:"unit"},"/PCS",-1))]),(0,m._)("div",K,[(0,m._)("span",M,(0,_.zw)(n.code),1)])])]),(0,m._)("div",N,[(0,m.Wm)(x,{modelValue:n.count,"onUpdate:modelValue":function(e){return n.count=e},min:1,max:2e5,"input-width":"55",onBlur:p[1]||(p[1]=function(n){return pn()}),onAdd:function(){return xn(n.id)},onReduce:function(){return wn(n.id)},class:"custom-input-number"},null,8,["modelValue","onUpdate:modelValue","onAdd","onReduce"])])])])]})),_:2},1032,["name"])})),128)),p[13]||(p[13]=(0,m._)("div",{class:"footer-info"},[(0,m._)("div",{class:"company-info"},[(0,m._)("i",{class:"i-bx-copyright"}),(0,m._)("span",null,"\u6280\u672f\u652f\u6301 \xa9 \u5e7f\u4e1c\u5de6\u5411\u79d1\u6280\u6709\u9650\u516c\u53f8")])],-1))],40,z)]})),_:1})):((0,m.wg)(),(0,m.iD)("div",G,[(0,m._)("div",L,[p[15]||(p[15]=(0,m._)("div",{class:"empty-icon"},[(0,m._)("i",{class:"i-bx-cart-alt"})],-1)),p[16]||(p[16]=(0,m._)("div",{class:"empty-title"},"\u8d2d\u7269\u8f66\u7a7a\u7a7a\u5982\u4e5f",-1)),p[17]||(p[17]=(0,m._)("div",{class:"empty-description"},"\u5feb\u53bb\u6311\u9009\u5fc3\u4eea\u7684\u5546\u54c1\u5427",-1)),(0,m.Wm)(k,{class:"go-shopping-btn",type:"primary",onClick:jn},{default:(0,m.w5)((function(){return p[14]||(p[14]=[(0,m._)("i",{class:"i-bx-shopping-bag"},null,-1),(0,m._)("span",null,"\u53bb\u8d2d\u7269",-1)])})),_:1})])]))]),(0,m.wy)((0,m._)("div",Q,[(0,m._)("div",X,[(0,m._)("div",{class:"select-all-section",onClick:bn},[(0,m.Wm)(w,{modelValue:e.value,"onUpdate:modelValue":p[2]||(p[2]=function(n){return e.value=n}),"icon-size":"16"},null,8,["modelValue"]),p[18]||(p[18]=(0,m._)("span",{class:"select-all-text"},"\u5168\u9009",-1))]),(0,m._)("div",$,[p[19]||(p[19]=(0,m._)("span",{class:"total-label"},"\u5408\u8ba1:",-1)),(0,m._)("span",nn,"\xa5"+(0,_.zw)(un.value.toFixed(2)),1)]),(0,m._)("div",en,[(0,m.Wm)(k,{class:"create-order-btn",type:"primary",onClick:sn,disabled:0===a.value.length||0===a.value.filter((function(n){return n.checkbox})).length},{default:(0,m.w5)((function(){return p[20]||(p[20]=[(0,m.Uk)(" \u521b\u5efa\u8ba2\u5355 ")])})),_:1},8,["disabled"])])])],512),[[h.F8,Hn.value]]),(0,m.wy)((0,m._)("div",tn,[(0,m._)("div",an,[(0,m._)("div",{class:"select-all-section",onClick:bn},[(0,m.Wm)(w,{modelValue:e.value,"onUpdate:modelValue":p[3]||(p[3]=function(n){return e.value=n}),"icon-size":"16"},null,8,["modelValue"]),p[21]||(p[21]=(0,m._)("span",{class:"select-all-text"},"\u5168\u9009",-1))]),(0,m._)("div",on,[(0,m.Wm)(k,{class:"save-btn",onClick:Sn},{default:(0,m.w5)((function(){return p[22]||(p[22]=[(0,m.Uk)(" \u4fdd\u5b58\u4fee\u6539 ")])})),_:1}),(0,m.Wm)(k,{class:"remove-btn",type:"primary",onClick:Bn},{default:(0,m.w5)((function(){return p[23]||(p[23]=[(0,m.Uk)(" \u79fb\u9664\u8d2d\u7269\u8f66 ")])})),_:1})])])],512),[[h.F8,!Hn.value]]),(0,m.Wm)(f,{content:"\u786e\u5b9a\u5c06\u5546\u54c1\u4ece\u8d2d\u7269\u8f66\u79fb\u9664\u5417\uff1f",visible:t.value,"onUpdate:visible":p[4]||(p[4]=function(n){return t.value=n}),onCancel:fn,onOk:vn},null,8,["visible"])]})),_:1})],64)}}});const rn=cn;var un=rn,ln={navigationBarTitleText:"\u8d2d\u7269\u8f66"};Page((0,a.createPageConfig)(un,"pages/shoppingCart/index",{root:{cn:[]}},ln||{}))}},function(n){var e=function(e){return n(n.s=e)};n.O(0,[107,216,592],(function(){return e(2270)}));n.O()}]);