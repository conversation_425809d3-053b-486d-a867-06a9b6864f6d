import { localStg } from '@/utils';
import Taro from '@tarojs/taro';

/** 获取token */
export function getToken() {
	console.log('getToken()',localStg.get('token'));
	
	return localStg.get('token') || '';
}
/** 获取用户信息 */
export function getUserInfo() {
	const emptyInfo: Auth.UserInfoProp = {
		nickName: '',
		avatarUrl: '',
		userId:''
	};
	const userInfo: Auth.UserInfoProp = localStg.get('userInfo') || emptyInfo;

	return userInfo;
}

/** 去除用户相关缓存 */
export function clearAuthStorage() {
	Taro.removeStorageSync('token');
}
