<script setup lang="ts">
import { useAuthStore } from '@/store/modules/auth'
import { My, Add, Order, Failure, Setting, IconFont, Edit,Date } from '@nutui/icons-vue-taro'
import Taro, { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { onMounted, onUpdated, ref, computed } from 'vue';
import { getUserInfo } from '@/service/api/index';
import { useAppStore } from '@/store/index';
import myImgCanvas from './myImgCanvas.vue';


const auth = useAuthStore()
// const userInfo = computed(() => auth.userInfo)
const isLogin = computed(() => auth.isLogin)
//跳转登录页函数
const toLogin = () => {
	Taro.redirectTo({
		url: '/pages/login/index'
	})
}

const avatarUrl = ref('')
const nickName = ref('')
definePageConfig({
	navigationBarTitleText: '个人中心'
});

const getUserInfoF = async (token: string) => {

	const res = await getUserInfo({ token })
	console.log('loginRes',res);
	
	
	Taro.setStorageSync('userInfo', res?.success?.user)

	
	avatarUrl.value = auth.userInfo()?.avator
	nickName.value = auth.userInfo()?.nickName

	
	

}
onMounted(() => {

	getUserInfoF(Taro.getStorageSync('token'))
	// isLogin.value=true


})
const closeLogin = () => {


	auth.resetAuthStore()



	if (Taro.getStorageSync('token') == '') {
		Taro.redirectTo({
			url: '/pages/login/index'
		})
	}
}
const getHarvestAddress = () => {
	
	Taro.navigateTo({
		url: '/package/package-a/harvestAddress/index?id=' + '传递过去用户id',
		fail: res => {
			console.log(res);

		}
	})

}
const appStore = useAppStore();
const getActiveTab=()=>{}
const gotoOrders = () => {
	appStore.setActiveTab('/pages/orders/index');
	Taro.switchTab({
		url: '/pages/orders/index'
	})
}
const addressIconList = ref({
	list: [
		{
			text: '待发货',
			icon: 'i-bx-archive-in'
		},
		{
			text: '已发货',
			icon: 'i-bx-archive'
		},
	]
})
const gotoEdit = () => {
	Taro.showToast({
		title: '暂停使用',
		icon: 'error',
		duration: 2000
	})
	// Taro.navigateTo({
	// 	url: '/package/package-a/edit/index'
	// })

}
</script>
<template>
	<basic-layout show-tab-bar>
		<!-- <custom-navbar title="个人中心" /> -->
		 <!-- <myImgCanvas :text="'我的'" /> -->
		<div class="my-box relative">
			<div class="user-card flex p-8">

				<nut-avatar style="border: 1px solid #fff;" size="large">
					<My v-if="!avatarUrl" />
					<img v-else :src="avatarUrl" style="overflow: hidden;border-radius: 50%;" />
				</nut-avatar>
				<view class="ml-8" @click="toLogin" v-if="!nickName">
					<view class="user-card-text font-600 ">{{ '点击登录' }}</view>
					<view class="user-card-subtitle">{{ '可解锁全部信息' }}</view>
				</view>
				<view class="ml-8 user-card-text" v-else>{{ nickName }}</view>
			</div>
			<!-- /myaddress -->
			<!-- <nut-cell-group class="ml-2 mr-2">
				<nut-cell title="订单中心" desc="查看全部" is-link class="font-600 myaddress-title"></nut-cell>
				<nut-cell>
					<ul class="flex justify-around w-full">
						<li v-for="item in addressIconList.list">
							<div class="flex flex-col justify-center">
								<IconFont :class="item.icon" size="22px" style="width: 100%;"></IconFont>
								<span>{{ item.text }}</span>
							</div>
						</li>
					</ul>
				</nut-cell>
			</nut-cell-group> -->

			<nut-grid class=" ml-2 mr-2 justify-left border-rounded" :column-num="1" direction="horizontal">
				<!-- <nut-grid-item text="资质绑定" @click="toVerification">
					<Add />
				</nut-grid-item> -->
				<nut-grid-item text="历史订单" @click="gotoOrders" class="justify-left">
					<Order />
				</nut-grid-item>
				<nut-grid-item text="修改密码" @click="gotoEdit" class="justify-left" disabled>
					<Edit />
				</nut-grid-item>
				<nut-grid-item text="联系地址" @click="getHarvestAddress" class="justify-left" disabled>
					<Date/>
				</nut-grid-item>
				<nut-grid-item text="退出登录" @click="closeLogin" disabled="false" class="justify-left">
					<Failure />
				</nut-grid-item>
			</nut-grid>

			<!-- 底部信息栏 -->
			<div class="user-button-text">
				<span>{{ '版权所有 © 广东左向科技有限公司' }}</span>
			</div>
		</div>
	</basic-layout>
</template>

<style lang="scss">
.myaddress-title {
	font-size: 1rem;
}

.my-box {
	.user_img {
		text-align: center;
		color: #000;
		background-color: #fff;
		padding: 1.875rem 0;
	}

	.userinfo_name {
		clear: both;
		width: 100%;
		padding-top: .625rem;
		color: #666666;
		font-size: 1.25rem;
	}

	.userinfo-avatar {
		color: #666666;
		font-size: 1.625rem;
		font-weight: bolder;
		text-align: center;
	}

	// css 背景色从上到下渐变 浅蓝到无颜色 过度缓慢点
	.nut-grid-item__content {
		justify-content: left;
		padding: var(--nut-grid-item-content-padding, 32rpx 56rpx);
	}

	height: 100vh;
	background: #F7F7F7;

	.user-card {

		.user-card-text {
			font-size: 1rem;
			color: #000000;
			font-weight: 600;
		}

		.user-card-subtitle {
			color: #a7a7a7;
			font-size: .75rem;
		}
	}

	.user-button-text {
		font-size: 12px;
		color: #A6A6A6;
		text-align: center;
		margin: 2rem auto;
	}
}
</style>
