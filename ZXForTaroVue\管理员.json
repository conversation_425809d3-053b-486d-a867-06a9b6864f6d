{"msg": "操作成功", "code": 200, "permissions": ["*:*:*"], "roles": ["admin"], "user": {"createBy": "admin", "createTime": "2025-03-12 08:29:55", "updateBy": null, "updateTime": null, "remark": "管理员", "params": {"@type": "java.util.HashMap"}, "userId": 1, "deptId": 100, "userName": "admin", "nickName": "张硕", "email": "<EMAIL>", "userType": "4", "phonenumber": "17770092341", "sex": "0", "avatar": "/profile/avatar/2025/04/12/微信截图_azgc_20250412141358A001.png", "password": "$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2", "status": "0", "delFlag": "0", "loginIp": "*************", "loginDate": "2025-04-14T20:51:20.000+08:00", "dept": {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "deptId": 100, "parentId": 0, "ancestors": "0", "deptName": "南昌分校", "orderNum": 0, "leader": null, "phone": null, "email": null, "status": "0", "delFlag": null, "parentName": null, "children": []}, "roles": [{"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "roleId": 1, "roleName": "超级管理员", "roleKey": "admin", "roleSort": 1, "dataScope": "1", "menuCheckStrictly": false, "deptCheckStrictly": false, "status": "0", "delFlag": null, "flag": false, "menuIds": null, "deptIds": null, "permissions": null, "admin": true}], "roleIds": null, "postIds": null, "roleId": null, "admin": true}}