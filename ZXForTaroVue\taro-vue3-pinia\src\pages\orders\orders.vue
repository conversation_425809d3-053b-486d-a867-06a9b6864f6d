<script setup>
import { onBeforeMount, ref } from "vue";
import { updateOrder } from '@/service/index'
import Taro, { showToast } from '@tarojs/taro';
// props
const props = defineProps({
    values: {
        type: Array,
        required: true
    },
    type: String
});

// 修订订单状态事件
const updateOrderState = async (order, state, index) => {
    const { error, success, msg, code } = await updateOrder({
        state: state,
    }, {
        id: order
    })
    console.log('success---!!!', error, success, msg, code);
    if (error == null) {
        if (code == 15) {
            Taro.showModal({
                title: '提示',
                content: msg,
                duration: 2000
            })

        } else if (code == 0) {
            Taro.showToast({
                title: '状态修改成功',
                icon: 'success',
                duration: 2000

            });
            console.log(props.values, state);

            // props.values.state = state
            isModify.value = order
            props.values.splice(index, 1)
        } else {
            Taro.showToast({
                title: msg,
                duration: 2000

            });
        }


    } else {
        Taro.showToast({
            title: msg,
            icon: 'none',
            duration: 4000
        });
    }


};
//订单状态是否修改过
const isModify = ref(null)
//修改订单信息
const updateOrderMessage = async (order, state = null) => {
    Taro.preload({ message: order, state })
    Taro.navigateTo({
        url: `/package/package-a/updateOrder/index?id=${order}&state=${state}`,
    })

}
const stateMap = ref(new Map([
    ['draft', '订单开立'],
    ['sent', '核准成功'],
    ['done', '审核通过']
]))
const oncancelOrderOk = () => {
    console.log('取消订单,id为:', cancelOrderId.value);
    //取消订单接口
}
const visibleCancelOrder = ref(false);
const cancelOrderId = ref(0)
// mounted
onBeforeMount(() => {
    console.log(props.values);
    // console.log(props.values)
});
// 长按复制订单信息
const onLongpress = (order) => {
    console.log('长按订单:', order);

    // 触觉反馈
    Taro.vibrateShort({
        type: 'medium'
    });

    // 构建复制的订单信息
    const copyInfo = buildOrderCopyInfo(order);

    // 显示复制选项弹窗
    Taro.showActionSheet({
        itemList: [
            '📋 复制订单基本信息',
            '📄 复制订单详细信息',
            '🔢 复制订单号',
            '📞 复制联系信息'
        ],
        success: function (res) {
            switch (res.tapIndex) {
                case 0:
                    copyToClipboard(copyInfo.basic, '订单基本信息');
                    break;
                case 1:
                    copyToClipboard(copyInfo.detailed, '订单详细信息');
                    break;
                case 2:
                    copyToClipboard(copyInfo.orderNumber, '订单号');
                    break;
                case 3:
                    copyToClipboard(copyInfo.contact, '联系信息');
                    break;
            }
        },
        fail: function () {
            console.log('用户取消了操作');
        }
    });
}

// 构建订单复制信息
const buildOrderCopyInfo = (order) => {
    const statusText = stateMap.value.get(order.state) || '未知状态';

    // 基本信息
    const basic = `订单号: ${order.u9c_no || order.order_no || '无'}
状态: ${statusText}
日期: ${order.order_date || '无'}
金额: ¥${order.total || '0'}`;

    // 详细信息
    let detailed = `=== 订单详细信息 ===
订单号: ${order.u9c_no || order.order_no || '无'}
状态: ${statusText}
创建日期: ${order.order_date || '无'}
订单金额: ¥${order.total || '0'}`;

    if (order.u9c_no) {
        detailed += `\nU9订单号: ${order.u9c_no}`;
    }
    if (order.order_no) {
        detailed += `\nODOO订单号: ${order.order_no}`;
    }
    if (order.date_approve) {
        detailed += `\n核准日期: ${order.date_approve}`;
    }
    if (order.date_planned) {
        detailed += `\n交期: ${order.date_planned}`;
    }
    if (order.zx_wl) {
        detailed += `\n物流单号: ${order.zx_wl}`;
    }
    if (order.notes) {
        detailed += `\n备注: ${order.notes}`;
    }

    // 订单号（优先U9订单号）
    const orderNumber = order.u9c_no || order.order_no || '无订单号';

    return {
        basic,
        detailed,
        orderNumber
    };
}

// 复制到剪贴板
const copyToClipboard = (text, type) => {
    Taro.setClipboardData({
        data: text,
        success: function () {
            Taro.showToast({
                title: `${type}已复制`,
                icon: 'success',
                duration: 2000
            });
        },
        fail: function () {
            Taro.showToast({
                title: '复制失败',
                icon: 'error',
                duration: 2000
            });
        }
    });
}

// 获取状态样式类
const getStatusClass = (state) => {
    const statusClasses = {
        'draft': 'status-draft',
        'sent': 'status-sent',
        'done': 'status-done'
    }
    return statusClasses[state] || 'status-default'
}

// 获取状态图标
const getStatusIcon = (state) => {
    const statusIcons = {
        'draft': 'i-bx-edit-alt',
        'sent': 'i-bx-paper-plane',
        'done': 'i-bx-check-circle'
    }
    return statusIcons[state] || 'i-bx-info-circle'
}

</script>

<template>
    <div class="orders-container">
        <!-- 空状态优化 -->
        <div v-if="values.length === 0" class="empty-state">
            <div class="empty-content">
                <div class="empty-icon">
                    <i class="i-bx-receipt"></i>
                </div>
                <h3 class="empty-title">暂无相关订单</h3>
                <p class="empty-description">您还没有任何订单记录</p>
            </div>
        </div>

        <!-- 订单列表 -->
        <div v-else class="orders-list">
            <div v-for="(order, index) in values" :key="order.id" class="order-item-wrapper">
                <transition name="order-fade">
                    <div class="order-card" @longpress="onLongpress(order)">
                        <!-- 长按复制提示 -->
                        <div class="copy-hint">
                            <i class="i-bx-copy-alt"></i>
                            <span>长按复制</span>
                        </div>

                        <!-- 订单头部 -->
                        <div class="order-header">
                            <div class="order-info">
                                <div class="order-number">
                                    <i class="i-bx-receipt"></i>
                                    <span class="number-text">{{ order.u9c_no || order.order_no }}</span>
                                </div>
                                <div class="order-date">
                                    <i class="i-bx-time"></i>
                                    <span>{{ order.order_date }}</span>
                                </div>
                            </div>
                            <div class="order-status">
                                <div class="status-badge" :class="getStatusClass(order.state)">
                                    <i :class="getStatusIcon(order.state)"></i>
                                    <span>{{ stateMap.get(order.state) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 订单详情 -->
                        <div class="order-details">
                            <div class="detail-row" v-if="order.u9c_no">
                                <span class="detail-label">U9订单号</span>
                                <span class="detail-value">{{ order.u9c_no }}</span>
                            </div>
                            <div class="detail-row" v-if="order.order_no">
                                <span class="detail-label">ODOO订单号</span>
                                <span class="detail-value">{{ order.order_no }}</span>
                            </div>
                            <div class="detail-row" v-if="order.date_approve">
                                <span class="detail-label">核准日期</span>
                                <span class="detail-value">{{ order.date_approve }}</span>
                            </div>
                            <div class="detail-row" v-if="order.date_planned">
                                <span class="detail-label">交期</span>
                                <span class="detail-value">{{ order.date_planned }}</span>
                            </div>
                            <div class="detail-row" v-if="order.notes">
                                <span class="detail-label">备注</span>
                                <span class="detail-value note-text">{{ order.notes }}</span>
                            </div>
                            <div class="detail-row" v-if="order.zx_wl">
                                <span class="detail-label">物流单号</span>
                                <span class="detail-value">{{ order.zx_wl }}</span>
                            </div>
                        </div>

                        <!-- 订单金额 -->
                        <div class="order-amount">
                            <div class="amount-label">订单金额</div>
                            <div class="amount-value">
                                <span class="currency">¥</span>
                                <span class="price">{{ order.total }}</span>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div class="order-actions">
                            <!-- 草稿状态按钮 -->
                            <template v-if="order.state === 'draft'">
                                <nut-button
                                    class="action-btn edit-btn"
                                    @click="updateOrderMessage(order.id)"
                                >
                                    <i class="i-bx-edit"></i>
                                    <span>修改订单</span>
                                </nut-button>
                                <nut-button
                                    class="action-btn approve-btn"
                                    @click="updateOrderState(order.id, 'done', index)"
                                >
                                    <i class="i-bx-check"></i>
                                    <span>审核</span>
                                </nut-button>
                            </template>

                            <!-- 已发送状态按钮 -->
                            <template v-if="order.state === 'sent'">
                                <nut-button
                                    class="action-btn cancel-btn"
                                    @click="updateOrderState(order.id, 'draft', index)"
                                >
                                    <i class="i-bx-x"></i>
                                    <span>取消</span>
                                </nut-button>
                                <nut-button
                                    class="action-btn approve-btn"
                                    @click="updateOrderState(order.id, 'done', index)"
                                >
                                    <i class="i-bx-check"></i>
                                    <span>审核</span>
                                </nut-button>
                                <nut-button
                                    class="action-btn detail-btn"
                                    @click="updateOrderMessage(order.id, 'Done')"
                                >
                                    <i class="i-bx-detail"></i>
                                    <span>订单详情</span>
                                </nut-button>
                            </template>

                            <!-- 完成状态按钮 -->
                            <template v-if="order.state === 'done'">
                                <nut-button
                                    class="action-btn reject-btn"
                                    @click="updateOrderState(order.id, 'draft', index)"
                                >
                                    <i class="i-bx-undo"></i>
                                    <span>弃审</span>
                                </nut-button>
                                <nut-button
                                    class="action-btn detail-btn"
                                    @click="updateOrderMessage(order.id, 'Done')"
                                >
                                    <i class="i-bx-detail"></i>
                                    <span>订单详情</span>
                                </nut-button>
                            </template>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
// 主色调变量
$primary-color: #122F38;
$primary-light: rgba(18, 47, 56, 0.1);
$primary-dark: #0d252c;
$accent-color: #FF6B35;
$success-color: #4CAF50;
$warning-color: #FF9800;
$error-color: #F44336;
$info-color: #2196F3;
$text-primary: #122F38;
$text-secondary: #666;
$text-light: #999;
$background-light: #f8f9fa;
$border-color: rgba(18, 47, 56, 0.1);

// 订单容器
.orders-container {
    min-height: 100vh;
    background: $background-light;
    padding: 12px;
}

// 空状态样式
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;

    .empty-content {
        text-align: center;
        padding: 40px 20px;

        .empty-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;

            i {
                font-size: 32px;
                color: #fff;
            }
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            color: $text-primary;
            margin: 0 0 8px 0;
        }

        .empty-description {
            font-size: 14px;
            color: $text-secondary;
            margin: 0;
        }
    }
}

// 订单列表
.orders-list {
    .order-item-wrapper {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

// 订单卡片
.order-card {
    position: relative;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 4px 20px rgba(18, 47, 56, 0.12);
    }
}

// 长按复制提示
.copy-hint {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    font-size: 10px;
    color: $text-secondary;
    font-weight: 500;
    z-index: 10;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(18, 47, 56, 0.1);
    opacity: 0.7;
    transition: all 0.3s ease;

    i {
        font-size: 12px;
        color: $primary-color;
    }

    span {
        white-space: nowrap;
    }

    // 悬停效果
    .order-card:hover & {
        opacity: 1;
        transform: scale(1.05);
    }

    // 动画效果
    animation: copyHintPulse 3s ease-in-out infinite;
}

// 复制提示脉冲动画
@keyframes copyHintPulse {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

// 订单头部
.order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
    color: #fff;

    .order-info {
        flex: 1;

        .order-number {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            i {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.8);
            }

            .number-text {
                font-size: 16px;
                font-weight: 600;
                color: #fff;
            }
        }

        .order-date {
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.6);
            }

            span {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }

    .order-status {
        .status-badge {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;

            &.status-draft {
                background: rgba(255, 152, 0, 0.2);
                color: $warning-color;
                border: 1px solid rgba(255, 152, 0, 0.3);
            }

            &.status-sent {
                background: rgba(33, 150, 243, 0.2);
                color: $info-color;
                border: 1px solid rgba(33, 150, 243, 0.3);
            }

            &.status-done {
                background: rgba(76, 175, 80, 0.2);
                color: $success-color;
                border: 1px solid rgba(76, 175, 80, 0.3);
            }

            i {
                font-size: 12px;
            }
        }
    }
}

// 订单详情
.order-details {
    padding: 16px;

    .detail-row {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            font-size: 14px;
            color: $text-secondary;
            font-weight: 500;
            min-width: 80px;
            flex-shrink: 0;
        }

        .detail-value {
            font-size: 14px;
            color: $text-primary;
            font-weight: 500;
            text-align: right;
            flex: 1;
            word-break: break-all;

            &.note-text {
                text-align: left;
                margin-left: 12px;
                color: $text-secondary;
                font-weight: 400;
                line-height: 1.4;
            }
        }
    }
}

// 订单金额
.order-amount {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: $background-light;
    border-top: 1px solid $border-color;

    .amount-label {
        font-size: 14px;
        color: $text-secondary;
        font-weight: 500;
    }

    .amount-value {
        display: flex;
        align-items: baseline;
        gap: 2px;

        .currency {
            font-size: 16px;
            color: $accent-color;
            font-weight: 600;
        }

        .price {
            font-size: 20px;
            color: $accent-color;
            font-weight: 700;
        }
    }
}

// 操作按钮区域
.order-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px;
    border-top: 1px solid $border-color;
    background: #fff;

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        i {
            font-size: 14px;
        }

        &:active {
            transform: scale(0.95);
        }

        // 编辑按钮
        &.edit-btn {
            background: rgba(33, 150, 243, 0.1);
            color: $info-color;
            border: 1px solid rgba(33, 150, 243, 0.3);

            &:active {
                background: rgba(33, 150, 243, 0.2);
            }
        }

        // 审核按钮
        &.approve-btn {
            background: rgba(76, 175, 80, 0.1);
            color: $success-color;
            border: 1px solid rgba(76, 175, 80, 0.3);

            &:active {
                background: rgba(76, 175, 80, 0.2);
            }
        }

        // 取消按钮
        &.cancel-btn {
            background: rgba(244, 67, 54, 0.1);
            color: $error-color;
            border: 1px solid rgba(244, 67, 54, 0.3);

            &:active {
                background: rgba(244, 67, 54, 0.2);
            }
        }

        // 详情按钮
        &.detail-btn {
            background: $primary-light;
            color: $primary-color;
            border: 1px solid rgba(18, 47, 56, 0.3);

            &:active {
                background: rgba(18, 47, 56, 0.2);
            }
        }

        // 弃审按钮
        &.reject-btn {
            background: rgba(255, 152, 0, 0.1);
            color: $warning-color;
            border: 1px solid rgba(255, 152, 0, 0.3);

            &:active {
                background: rgba(255, 152, 0, 0.2);
            }
        }
    }
}

// 动画效果
.order-fade-enter-active,
.order-fade-leave-active {
    transition: all 0.5s ease;
}

.order-fade-enter-from {
    opacity: 0;
    transform: translateY(20px);
}

.order-fade-leave-to {
    opacity: 0;
    transform: translateY(-20px);
}

// NutUI组件样式覆盖
.nut-button {
    --nutui-button-border-radius: 20px;
    --nutui-button-font-weight: 600;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

// 响应式设计
@media (max-width: 375px) {
    .orders-container {
        padding: 8px;
    }

    .order-card {
        border-radius: 8px;

        .copy-hint {
            top: 6px;
            right: 6px;
            padding: 3px 6px;
            font-size: 9px;
            border-radius: 8px;

            i {
                font-size: 10px;
            }
        }
    }

    .order-header {
        padding: 12px;

        .order-info .order-number .number-text {
            font-size: 14px;
        }

        .order-status .status-badge {
            padding: 4px 8px;
            font-size: 11px;
        }
    }

    .order-details {
        padding: 12px;

        .detail-row {
            margin-bottom: 10px;

            .detail-label {
                font-size: 13px;
                min-width: 70px;
            }

            .detail-value {
                font-size: 13px;
            }
        }
    }

    .order-amount {
        padding: 12px;

        .amount-value .price {
            font-size: 18px;
        }
    }

    .order-actions {
        padding: 12px;
        gap: 6px;

        .action-btn {
            padding: 6px 12px;
            font-size: 11px;

            i {
                font-size: 12px;
            }
        }
    }

    .empty-state .empty-content {
        padding: 30px 15px;

        .empty-icon {
            width: 60px;
            height: 60px;

            i {
                font-size: 24px;
            }
        }

        .empty-title {
            font-size: 16px;
        }

        .empty-description {
            font-size: 13px;
        }
    }
}
</style>
