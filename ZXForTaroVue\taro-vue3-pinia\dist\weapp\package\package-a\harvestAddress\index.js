"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[130],{9921:function(e,n,t){var o=t(1065),r=t(6944),a=t(889),u=(t(9932),t(1845)),i=(t(3505),t(1394)),s=(t(8021),t(2344)),c=(t(2240),t(2419)),l=t(2810),d=t(3221),v=t(7011),f=t(1959),p=t.n(f),w=t(6821),h=t(4733),g=t(4081),k={class:"harvest-box"},m={class:"harvest-newButton text-center m-auto"},b=function(e){return e["\u6dfb\u52a0"]="add",e["\u7f16\u8f91"]="update",e["\u5220\u9664"]="delete",e}(b||{}),x=(0,d.aZ)({__name:"index",setup:function(e){(0,g.tN)();var n=function(){var e=(0,l.Z)((0,c.Z)().mark((function e(){var n,t,r;return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("aaaaaa"),""===p().getStorageSync("token")){e.next=10;break}return e.next=4,(0,h.Kn)();case 4:n=e.sent,t=n.error,r=n.success,console.log(t,"error"),console.log(r,"success"),null===t&&(o.value=r.items);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),t=(0,f.getCurrentInstance)();(0,d.bv)((function(){t&&t.router&&t.router.onShow?f.eventCenter.on(t.router.onShow,n):(console.error("\u65e0\u6cd5\u83b7\u53d6router.onShow\u4e8b\u4ef6\u5904\u7406\u51fd\u6570"),p().showToast({title:"\u65e0\u6cd5\u83b7\u53d6\u4e8b\u4ef6",icon:"none",duration:2e3}))})),(0,d.Ah)((function(){t&&t.router&&t.router.onShow?f.eventCenter.off(t.router.onShow,n):(console.error("\u65e0\u6cd5\u83b7\u53d6router.onShow\u4e8b\u4ef6\u5904\u7406\u51fd\u6570"),p().showToast({title:"\u65e0\u6cd5\u83b7\u53d6\u4e8b\u4ef6",icon:"none",duration:2e3}))}));var o=(0,w.iH)([]),x=(0,w.qj)({id:"id",fullAddress:"address",addressName:"name"}),Z=function(e,n){S(b.\u7f16\u8f91,n)},S=function(e,n){p().navigateTo({url:"/package/package-a/harvestAddress/setAddress/index?state=".concat(e)}),p().preload({item:n})},_=(0,w.iH)(""),T=(0,w.iH)(!1),j=function(e,n){T.value=!0,_.value=n.id},y=function(){var e=(0,l.Z)((0,c.Z)().mark((function e(){var n;return(0,c.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,h._N)({id:_.value},{id:_.value});case 2:n=e.sent,console.log(n),null===n.error&&(o.value=o.value.filter((function(e){return e.id!==_.value})),_.value="",p().showToast({title:"\u5220\u9664\u6210\u529f",icon:"none",duration:2e3}));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,n){var t=s.Z,c=i.Z,l=u.Z,f=a.Z,p=r.Z;return(0,d.wg)(),(0,d.j4)(p,{"show-tab-bar":""},{default:(0,d.w5)((function(){return[(0,d.Wm)(t,{title:"\u5220\u9664\u5730\u5740",content:"\u786e\u8ba4\u5220\u9664\u5f53\u524d\u6536\u8d27\u5730\u5740\u5417\uff1f",visible:T.value,"onUpdate:visible":n[0]||(n[0]=function(e){return T.value=e}),onOk:y},null,8,["visible"]),(0,d._)("div",k,[o.value.length>0?((0,d.wg)(),(0,d.j4)(c,{key:0,data:o.value,onDelIcon:j,onEditIcon:Z,"show-bottom-button":!1,"data-options":x},null,8,["data","data-options"])):((0,d.wg)(),(0,d.j4)(l,{key:1,image:"https://static-ftcms.jd.com/p/files/61a9e3313985005b3958672e.png",description:"\u6682\u65e0\u6536\u8d27\u5730\u5740"}))]),(0,d._)("div",m,[(0,d.Wm)(f,{size:"large",color:"linear-gradient(to right, #ff6034, #ee0a24)",onClick:n[1]||(n[1]=function(e){return S(b.\u6dfb\u52a0,"\u8fd9\u91cc\u4f20\u9012\u8fc7\u6765id")})},{default:(0,d.w5)((function(){return n[2]||(n[2]=[(0,d.Uk)((0,v.zw)("\u65b0\u5efa\u5730\u5740"))])})),_:1})])]})),_:1})}}});const Z=x;var S=Z,_={navigationBarTitleText:"\u6536\u8d27\u5730\u5740"};Page((0,o.createPageConfig)(S,"package/package-a/harvestAddress/index",{root:{cn:[]}},_||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(9921)}));e.O()}]);