{"name": "@unocss/webpack", "version": "0.58.0", "description": "The Webpack plugin for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/webpack#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/webpack"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "webpack-plugin"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "peerDependencies": {"webpack": "^4 || ^5"}, "dependencies": {"@ampproject/remapping": "^2.2.1", "@rollup/pluginutils": "^5.1.0", "chokidar": "^3.5.3", "fast-glob": "^3.3.2", "magic-string": "^0.30.5", "unplugin": "^1.5.1", "webpack-sources": "^3.2.3", "@unocss/config": "0.58.0", "@unocss/core": "0.58.0"}, "devDependencies": {"@types/webpack": "^5.28.5", "@types/webpack-sources": "^3.2.3", "webpack": "^5.89.0"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}