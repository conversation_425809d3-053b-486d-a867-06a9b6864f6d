<script setup lang="ts">
import Card from '../../components/Card'
import { ref, onBeforeMount, Ref, onMounted, onUnmounted } from 'vue';
import { IconFont } from "@nutui/icons-vue-taro";
import Taro, { eventCenter, getCurrentInstance } from "@tarojs/taro";
import { getFindCart, updateFindCart, getProduct } from '@/service/index'
import { useAppStore } from '@/store';
const allSelected = ref(false)
const isRmCommodityDialogShow = ref(false)
interface IcartCommodities {
    id: number,
    image: string,
    name: string,
    price_unit: number,
    count: number,
    maxBuy: number,
    checkbox: boolean,
    spec: string,
    code: string,
    model: string

}
const cartCommodities: Ref<Array<IcartCommodities>> = ref(
    [])


const getFindCartF = async () => {
    const { error, success } = await getFindCart()
    if (error === null) {
        console.log('getFindCartF', success);
        cartNum.value = success.items.length
        await getProductF(success.items.map((i: any) => i.product_id), success.items)


    } else {
        Taro.showToast({
            title: '获取购物车失败',
            icon: 'error',
            duration: 2000
        })
    }

}
const appStore = useAppStore();
onMounted(() => {
    eventCenter.on(getCurrentInstance().router!.onShow, () => {
        // if(isManage.value===false) isManage.value=true
        appStore.setActiveTab('/pages/shoppingCart/index');
        getFindCartF()
        cartCommodities.value.forEach(item => {
            item['checkbox'] = false
        })
        countTotalPrice()


    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)
})
// onBeforeMount(() => {
//     getFindCartF()
//     cartCommodities.value.forEach(item => {
//         item['checkbox'] = false
//     })
// })
const totalPrice = ref(0)
const swipeRefs = ref([])
const goMakeOrder = () => {
    visibleSubmit.value = true
}

let tempId = -1
const showRmDialog = (id: number) => {
    console.log("id", id);

    tempId = id
    isRmCommodityDialogShow.value = true
}

const countTotalPrice = () => {
    totalPrice.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) => {


            return acc + curr.price_unit * curr.count
        }, 0);
}
const removeCommodity = async () => {
    cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.id === tempId), 1)
    const { error, success } = await updateFindCart({
        product_id: -tempId
    })
    if (error === null) {
        Taro.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
        })
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }



}
const reCCard = () => {
    swipeRefs.value[cartCommodities.value.findIndex(item => item.id === tempId)]?.close()
}

const cCheckboxChange = (state: any, label: any) => {
    const l = cartCommodities.value.map(i => i.checkbox).filter(x => x).length
    allSelected.value = l === cartCommodities.value.length;
    countTotalPrice()
}
//商品变动的数组
const updateCartCommodities = ref([])
function updateOrInsert(array: any[][], target: any[]) {
    console.log('array', array, target);

    // 查找目标子数组的索引
    const index = array.findIndex((item: any[]) => item[0] === target[0]);
    console.log('index', index);


    if (index !== -1) {
        // 更新已存在的子数组
        array[index] = [array[index][0], array[index][1] + target[1]];
    } else {
        // 新增新子数组
        array.push(target);
    }
}
const minus = (id: number) => {

    cartCommodities.value.forEach(
        item => {
            if (id === item.id) {
                if (item.count !== 1) {
                    updateOrInsert(updateCartCommodities.value, [item.id, -1]);
                    item.count -= 1
                } else {
                    Taro.showToast({
                        title: '最少购买一个商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const plus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            if (id === item.id) {
                if (item.count !== item.maxBuy) {
                    updateOrInsert(updateCartCommodities.value, [item.id, 1]);
                    item.count = Number(item.count) + 1
                } else {
                    Taro.showToast({
                        title: '当前最多仅能购买' + item.maxBuy + '份该商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
//全选事件
const checkAll = () => {
    if (allSelected.value) {
        cartCommodities.value.forEach(item => {
            item.checkbox = true
        })
    } else {
        cartCommodities.value.forEach(item => {
            item.checkbox = false
        })
    }
}
//控制按箱下单还是散件提交
const submitType = ref(0)
//每箱件数
const boxNum = ref(1)
//提交订单提示框
const visibleSubmit = ref(false)
const onSubmit = () => {
    const checkCartCommodities = cartCommodities.value
        .filter(x => x.checkbox)
    console.log('订单信息', checkCartCommodities);
    Taro.navigateTo({
        url: '/package/package-a/confirmOrder/index',

    })
    Taro.preload({ message: checkCartCommodities, fromSCartPage: true })
}
//滚动加载
const scrollTop = ref(0)
const toView = ref('demo2')
const upper = (e: any) => {
    console.log('upper:', e)
}

const lower = (e: any) => {
    console.log('lower:', e)
}

const scroll = (e: any) => {
    console.log('scroll:', e)
}
//返回顶部
const backTop = () => {
    console.log('click')
}

//购物车数量
const cartNum = ref(0)
//管理、退出管理文本切换
const isManage = ref(true)
const manageText = ref(new Map([
    [true, '管理'],
    [false, '退出']
]))
const onManage = () => {
    console.log('manage');

    isManage.value = !isManage.value
}
//查询商品
const getProductF = async (ids: any, countS: any) => {
    const { error, success } = await getProduct({ ids })
    const { items } = success
    console.log(success, items);

    if (success && items) {
        cartCommodities.value = items.map((i: any) => {

            return {
                ...i,
                checkbox: false,
                maxBuy: 10000,
                count: 1
            }
        })
        countS.forEach((i: { product_id: number; product_qty: number; }) => {
            // 在counts数组中寻找匹配的项
            const matchedItem = cartCommodities.value.find(item => item.id === i.product_id);

            // 如果找到了匹配的项，则更新其count属性
            if (matchedItem) {
                matchedItem.count = i.product_qty;
            }
        });
        console.log('cartCommodities.value!!!!', cartCommodities.value);


    }
}
//移除购物车
const onRmCartF = async () => {
    let checkCartCommodities = cartCommodities.value
        .filter(x => x.checkbox)
    console.log(checkCartCommodities, '~~~');
    checkCartCommodities = checkCartCommodities.map(i =>
        [
            -i.id,
            i.count
        ]
    )
    let resultStr = '[' + checkCartCommodities.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
    const { error, success } = await updateFindCart({
        product_ids: resultStr
    })
    if (error === null) {
        Taro.showToast({
            title: '操作成功',
            icon: 'success',
            duration: 2000
        })
        await getFindCartF()
        countTotalPrice()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}
//更新购物车数量
const onudCartF = async () => {


    let resultStr = '[' + updateCartCommodities.value.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
    const { error, success } = await updateFindCart({
        product_ids: resultStr
    })
    if (error === null) {
        Taro.showToast({
            title: '操作成功',
            icon: 'success',
            duration: 2000
        })
        //清空变动量
        updateCartCommodities.value = []
        await getFindCartF()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }
}
const deleteProduct = async (id: number | string, count: number) => {

    const { error, success } = await updateFindCart({
        product_id: -id,
        product_qty: count
    })
    if (error === null) {
        Taro.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
        })
        await getFindCartF()
        countTotalPrice()
    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'none',
            duration: 2000
        })
    }

}
definePageConfig({
    navigationBarTitleText: '购物车'
});

</script>
<template>
    <nut-sticky top="0" style="background-color:#FAFAFA;height: 2.1875rem;line-height: 2.1875rem;">
        <view class="flex justify-between pl-3% pr-3%" style="background-color: #fff;border-bottom: 1px solid #ccc;">
            <view class="font-size-18px font-bold">购物车( <span class="font-300 font-size-16px color-#F93F63">{{
                cartNum }}</span> )</view>
            <view @click="onManage"><i class="i-bx-cog"></i>{{ manageText.get(isManage) }}</view>

        </view>
    </nut-sticky>
    <basic-layout show-tab-bar>
        <!-- <custom-navbar title="购物车" /> -->

        <nut-dialog title="提交订单" content="确认提交订货信息并生成订单吗？" v-model:visible="visibleSubmit" @ok="onSubmit" />
        <card class="card">
            <nut-swipe-group lock style="width: 100vw" v-if="cartCommodities.length > 0">
                <!-- <nut-backtop height="90vh" style="margin-bottom: 60rpx;" :distance="30" :bottom="250">
                    <template #content> -->
                <scroll-view :scroll-y="true" style="height: calc(100%-58rpx);margin-bottom: 60rpx;"
                    @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll" :scroll-top="scrollTop">
                    <nut-swipe v-for="item in cartCommodities" ref="swipeRefs" style="margin-bottom: 10px;"
                        :name="item.id.toString()" :key="item.id">
                        <view
                            style="display: flex; gap: 5px; width: 96vw;border-top-left-radius: 10px;border-bottom-left-radius: 10px;box-sizing: border-box;text-align: center;"
                            class="my-boxShad">
                            <i class="i-bx-trash absolute top-1.25rem right-1.25rem font-size-1.25rem color-#CC463D"
                                v-show="!isManage" @click="deleteProduct(item.id, item.count)"></i>
                            <nut-checkbox @change="cCheckboxChange" v-model="item.checkbox" icon-size="20"
                                style="margin-left: .625rem;margin-right: 0 !important;">
                                <image :src="item.image"
                                    style="width: 80px; height: 80px;border-radius: 8px;box-shadow: rgba(0, 0, 0, 0.35) 5px -26px 45px -25px inset;">
                                </image>
                                <!-- <nut-tag plain type="primary">{{ item.name.split('/')[0] }}</nut-tag> -->
                            </nut-checkbox>
                            <view
                                style="display: flex; flex-direction: column; gap: 10px;padding: 5%;padding-bottom: 7%;">
                                <view
                                    style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;text-align: left;">

                                    <!-- <nut-tag color="#F7AA71" class="ml-3% mr-3%">{{ item.model }}</nut-tag> -->
                                    <div
                                        style="color:#DD5F73;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #DD5F73;margin-bottom: 1%;margin-right: 3%;">
                                        <i class="i-bx-crown"
                                            style="vertical-align: text-bottom;color: #DD5F73;font-weight: bold;"></i>
                                        {{ item.model }}
                                    </div>
                                    <div
                                        style="color:#4D74FA;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #4D74FA;margin-bottom: 1%;">

                                        {{ item.name.split('/')[0] }}
                                    </div>
                                    <nut-tag color="#F7AA71" v-show="item.name.split('/')[1]">{{
                                        item.name.split('/')[1]
                                        }}</nut-tag>

                                </view>

                                <nut-tag color="#F2F2F2" text-color="#909090"
                                    class="text-ellipsis w-11.25rem break-all">{{ item.spec
                                    }}</nut-tag>
                                <view class="flex justify-between mb-3%">
                                    <span>
                                        <nut-price :price="item.price_unit"
                                            style="color: #F36409;font-weight: 900;"></nut-price>
                                        <span class="unit color-#ED7976 pl-5px  font-900">/</span>
                                        <span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
                                    </span>
                                    <nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }} </nut-tag>
                                </view>
                            </view>
                        </view>

                        <view class="quantity-button-group">
                            <!-- <IconFont @click="() => minus(item.id)" name="minus"
                                        class="button-cell button-minus">
                                    </IconFont>
                                    <view class="button-cell button-cell-middle ">{{ item.count }}</view>
                                    <IconFont @click="() => plus(item.id)" class="button-cell button-plus" name="plus">
                                    </IconFont> -->

                            <nut-input-number v-model="item.count" :min="1" :max="200000" input-width="55"
                                @blur="countTotalPrice()" @add="() => plus(item.id)" @reduce="() => minus(item.id)" />
                        </view>
                        <!-- <template #right>
                                    <nut-button shape="square" style="height: 100%" type="danger"
                                        @click="() => { showRmDialog(item.id) }">删除</nut-button>
                                </template> -->
                    </nut-swipe>
                    <!-- 底部信息栏 -->
                    <div class="user-button-text">
                        <span>{{ '技术支持 © 广东左向科技有限公司' }}</span>
                    </div>
                </scroll-view>
                <!-- </template>
                </nut-backtop> -->
            </nut-swipe-group>
            <nut-empty v-else description="购物车空空如也" />
        </card>
        <view class="bottom-card" v-show="isManage">
            <view style="display: flex; gap: 5px">
                <view><nut-checkbox v-model="allSelected" @click="checkAll">全选</nut-checkbox></view>
                <view style="font-weight: bold;">合计:
                    <nut-price :price="totalPrice"></nut-price>
                </view>
            </view>
            <view style="margin-right: 10px">
                <nut-button type="primary" @click="goMakeOrder" :disabled="(cartCommodities.length === 0) || (cartCommodities
                    .filter(x => x.checkbox).length === 0)">{{ '创建订单' }}</nut-button>
            </view>
        </view>
        <view class="bottom-card" v-show="!isManage">
            <view style="display: flex; gap: 5px">
                <view><nut-checkbox v-model="allSelected" @click="checkAll">全选</nut-checkbox></view>

            </view>
            <view style="margin-right: 10px" class="flex">
                <nut-button type="warning" @click="onudCartF" class="border-rd-5%! mr-2%! ml-2%! color-#EA7841!"
                    color="#FFF0EB">{{ '保存修改' }}</nut-button>
                <nut-button type="primary" @click="onRmCartF" class="border-rd-5%! mr-2%!">{{ '移除购物车' }}</nut-button>
            </view>
        </view>
        <nut-dialog content="确定将商品从购物车移除吗？" v-model:visible="isRmCommodityDialogShow" @cancel="reCCard"
            @ok="removeCommodity" />
    </basic-layout>
</template>


<style lang="scss">
.test {
    padding: 12px 0 12px 20px;
    border-top: 1px solid #eee;
}

.my-boxShad {
    box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}


.button-minus {
    // border: 1px solid #aba8a8;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    background-color: #F9FAFC;
}

.button-plus {
    // border: 1px solid #aba8a8;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    background-color: #F9FAFC;
}

.button-cell-middle {
    // border-top: 1px solid #aba8a8;
    // border-bottom: 1px solid #aba8a8;
    background-color: #F9FAFC;
    text-align: center;
    line-height: 17px;
    margin: 0 5%;
}

.card {
    margin: 0 0;
}

.button-cell {
    min-width: 25px;
    height: 17px;
}

.quantity-button-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    bottom: 5px;
    right: 30px;
}

.bottom-card {
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    position: fixed;
    margin-bottom: 5rpx;
    bottom: var(--nut-tabbar-height, 100rpx);

    width: 100vw;
    align-items: center;
    padding: .625rem;
    padding-bottom: env(safe-area-inset-bottom);
    border-top: 1px solid #dcdcdc;
    
}
.user-button-text {
	font-size: 12px;
	color: #A6A6A6;
	text-align: center;
	margin: 2rem auto;
}
</style>