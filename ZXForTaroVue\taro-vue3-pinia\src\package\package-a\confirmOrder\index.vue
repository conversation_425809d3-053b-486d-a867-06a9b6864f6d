<template>

    <div>
        <nut-action-sheet v-model:visible="show" class="myAction">
            <div style="height: 340px;">
                <nut-searchbar v-model="searchValue" shape="square" label="请输入楼层以搜索" input-background="#F0F0F0"
                    @change="GetInputFocus" id="pop-target">
                    <template #leftin>
                        <Search2 />
                    </template>
                </nut-searchbar>

                <div :class="{ toShowDiv: !showSelect }"
                    style="width: 80%;height: 300px;background-color: #FFFFFF;background-color: #FFFFFF;position: absolute;left: 50%;transform:translateX(-50%); z-index:999;">
                    <div>
                        <div v-if="searchList.length > 0">
                            <nut-list :list-data="searchList" :container-height="300" @scroll-bottom="onScrollBottom">
                                <template #default="{ item }">
                                    <div class="list-item" @click="clickItem(item)">
                                        {{ item.name }}
                                    </div>

                                    <nut-divider :dashed="true" />
                                </template>
                            </nut-list>
                        </div>

                        <div v-else style="margin-top: 5%;color: #333333;font-weight: 900;">
                            {{ '请输入相应正确楼层编号后进行查询' }}
                        </div>
                    </div>

                </div>
            </div>
        </nut-action-sheet>
        <!-- {{ cartCommodities }} -->
        <nut-cell>
            <div class="harvest-box">
                <nut-address-list :data="harvestData" :show-bottom-button="false" :data-options="harvestOptions">
                    <template #item-icon>
                        <i class="i-bx-edit-alt font-size-20px p-3px" @click="editClick"></i>
                    </template>
                </nut-address-list>
            </div>
        </nut-cell>
        <!-- 备注及特殊发货要求 -->
        <nut-cell class="flex flex-col">
            <div class="flex  flex-col">
                <div class="line-height-40px font-bold color-#ED7976!">{{ '*项目名称：' }}</div>
                <nut-textarea v-model="project_Name" limit-show :max-length="50" autosize class="w60%!"
                    placeholder-style="color:#ED7976;" />
            </div>
            <div class="flex  flex-col">
                <div class="line-height-40px font-bold">{{ '备注：' }}</div>
                <nut-textarea v-model="note" limit-show :max-length="50" autosize class="w60%!  myTextarea" />
            </div>
        </nut-cell>
        <nut-popup class="flex flex-col justify-evenly" v-model:visible="popupShow" position="bottom" round
            :style="{ height: '65vh' }" :safe-area-inset-bottom="true">
            <Address @click-item="(val) => { popupShow = val.boolean; harvestData[0] = val.item }" :showDefault="true"
                @defauult-emit="(val) => {
                    popupShow = false; harvestData[0].addressName = '采用U9默认联系地址'; harvestData[0].fullAddress = ' '; harvestData[0].phone = ' '; harvestData[0].defaultAddress = false
                }"></Address>






            <!-- <nut-form ref="formRef">
                <nut-form-item label="联系人" required prop="testaddressName" :rules="[
                    {
                        validator: validatorName,
                        message: '请输入正确的人名'
                    }
                ]">
                    <nut-input v-model="formData.testaddressName" placeholder="请输入联系人" type="text" />
                </nut-form-item>
                <nut-form-item label="联系电话" required prop="phone" :rules="[
                    {
                        validator: validatorPhone,
                        message: '请输入正确的手机号'

                    }
                ]">
                    <nut-input v-model="formData.phone" placeholder="请输入联系电话" type="text" />
                </nut-form-item>
                <nut-form-item label="联系地址" required prop="fullAddress" :rules="[
                    {
                        validator: validatorAddress,
                        message: '地址应尽量详细'

                    }

                ]">
                    <nut-input v-model="formData.fullAddress" placeholder="请输入地址" type="text" />
                </nut-form-item>

            </nut-form>
            <nut-button type="primary" size="large" class="block! verifi-button w-80%! ml-auto! mr-auto!" block
                @click="submitAddress">{{
                    '提交' }}</nut-button> -->

        </nut-popup>
        <nut-cell>
            <card class="card">
                <nut-swipe-group lock style="width: 100vw" v-if="cartCommodities.length > 0">
                    <nut-swipe v-for="(item, index) in cartCommodities" ref="swipeRefs" class="lastChild"
                        style="margin-bottom: 10px" :name="item.id.toString()" :key="item.id" disabled>
                        <view style="display: flex; gap: 5px; width: 100vw">
                            <div class="text-center">
                                <image :src="item.image" style="width: 100px; height: 100px;display: inline-table;">
                                </image>
                                <!-- <nut-tag plain type="primary">{{ item.name.split('/')[0] }}</nut-tag> -->
                            </div>
                            <view
                                style="display: flex; flex-direction: column; gap: 8px;padding: 5%;padding-bottom: none">
                                <view style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;">
                                    <div
                                        style="color:#DD5F73;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #DD5F73;margin-bottom: 1%;margin-right: 3%;">
                                        <i class="i-bx-crown"
                                            style="vertical-align: text-bottom;color: #DD5F73;font-weight: bold;"></i>
                                        {{ item.model }}
                                    </div>
                                    <div
                                        style="color:#4D74FA;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #4D74FA;margin-bottom: 1%;">

                                        {{ item.name.split('/')[0] }}
                                    </div>

                                </view>


                                <nut-tag color="#F2F2F2" text-color="#909090"
                                    class="text-ellipsis w-11.25rem break-all">{{
                                        item.spec
                                    }}</nut-tag>
                                <view class="flex justify-between">
                                    <span>
                                        <nut-price :price="item.price_unit"
                                            style="color: #F36409;font-weight: 900;"></nut-price>
                                        <span class="unit color-#ED7976 pl-5px  font-900">/</span>
                                        <span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
                                    </span>
                                    <nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }} </nut-tag>
                                </view>

                                <DesignCell :design="item.seiban" :id="item.id" :index="index" :name="item.name"
                                    @update:design="showDialog"
                                    :class="{ noShow: !item.name.split('/')[0].includes('特殊图案') }">
                                </DesignCell>
                            </view>
                        </view>
                        <div class="flex justify-start h40px">
                            <div class="line-height-40px">{{ '备注：' }}</div>
                            <div class="w60%!">
                                <nut-input v-model="item.leaveMsg" :max-length="50" placeholder="去留言(下单后无法追加)"
                                    class="w60%!">
                                    <template #right>
                                        <i class="i-bx-edit-alt"></i>
                                    </template>
                                </nut-input>
                            </div>
                        </div>

                        <view class="quantity-button-group">
                            <IconFont name="minus" class="button-cell button-minus">
                            </IconFont>
                            <view class="button-cell button-cell-middle ">{{ item.count }}</view>
                            <IconFont class="button-cell button-plus" name="plus">
                            </IconFont>
                        </view>
                    </nut-swipe>
                </nut-swipe-group>
                <nut-empty v-else description="空空如ye~~" />
            </card>
        </nut-cell>

        <div class="h-50px w-100%!"></div>

        <view class="bottom-card">
            <view style="display: flex; gap: 5px">
                <view  style="font-weight: bold;">合计:
                    <nut-price :price="totalPrice"></nut-price>
                </view>
                <view style="font-weight: bold;">共:<span style="font-weight: bold;color: #F36409;"> {{ countJS }}</span>件
                </view>
            </view>
            <view style="margin-right: 10px">
                <nut-button type="primary" @click="submitOrderF" :disabled="project_Name.length <= 0">{{ '确认订单'
                    }}</nut-button>
            </view>
        </view>
    </div>
</template>

<script setup lang="ts">
import { getDefaultAddress, submitOrder, getProductByNumber, updateFindCart } from '@/service/index'
import Taro from '@tarojs/taro';
import { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { onMounted, onUnmounted, reactive, ref, Ref } from 'vue';
import { IconFont } from "@nutui/icons-vue-taro";
import { useAppStore, useThemeStore } from '@/store';
import { markRaw, toRaw } from "@vue/reactivity";
import Address from '@/components/address/index.vue'
const cartCommodities: Ref<Array<{
    id: number,
    code: string,
    name: string,
    spec: string,
    model: string,
    price_unit: number,
    image: string,
    count: number,
    maxBuy: number,
    checkbox: boolean,
    design: string | undefined,
    leaveMsg: string | undefined,
    fanhao_id: string | undefined,
    seiban: string | undefined,
    line_id: number,
    project: string
}>> = ref([
])
interface IharvestData {
    addressName: string,
    phone: string,
    defaultAddress: boolean,
    fullAddress: string
    testid: string | number | null,
}
onMounted(() => {
    cartCommodities.value.push(...preloadMessage!.message)
    eventCenter.on(getCurrentInstance().router!.onShow, () => {

        console.log('gotoback' in toRaw(harvestData.value)[0], 'dada');
        // if (!('gotoback' in toRaw(harvestData.value)[0])) {
        //     getDefaultAddressF(Taro.getStorageSync('userInfo').userId)
        // }

        countTotalPrice()
    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)
})
//备注及项目名称
const note = ref('')
const project_Name = ref('')

//控制弹出层
const popupShow = ref(false)
const formRef = ref(null)
const editClick = () => {
    console.log('Click To Edit')
    popupShow.value = true
}
const formData: Ref<{
    addressName: string,
    phone: string,
    fullAddress: string
}> = ref({
    addressName: '',
    phone: '',
    fullAddress: ''
})


// const goToAddress = () => {
//     Taro.navigateTo({
//         url: '/package/package-a/harvestAddress/index',
//         events: {
//             defaultEvent: (data: any) => {
//                 console.log(harvestData.value, 'harsetDataBBBBBBBBB');
//                 harvestData.value = [JSON.parse(JSON.stringify(data)) as IharvestData]
//                 console.log(harvestData.value, 'harvestData');
//                 console.log(data, 'data');


//             }
//         }
//     })
// }
//创建要货订单
const createOrder = () => {
}
const appStore = useAppStore();
const submitOrderF = async () => {
    // console.log("====");
    // console.log("address", formData.value)
    // console.log("note", note.value);
    // console.log("special_note", special_note.value);
    // console.log("cartCommodities", cartCommodities.value);
    let designConfirm = cartCommodities.value.find(el =>
        el.name.includes('特殊图案') && !('seiban' in el)
    )
    console.log(designConfirm, 'designConfirm');

    if (designConfirm) {

        Taro.showModal({
            title: '提示',
            content: '特殊图案产品需选择楼层图案后才可下单',
            success: function (res) {
            }
        })
    }
    else {

        let reduceArray = cartCommodities.value.map(item => {
            let { id: product_id, count: product_qty, price_unit, fanhao_id, leaveMsg: zx_line_notes } = item
            return {
                product_id,
                product_qty,
                price_unit,
                fanhao_id,
                zx_line_notes
            }
        })
        console.log('reduceArray', JSON.stringify(reduceArray));
        console.log("====");
        const { error, success } = await submitOrder({
            ...(!['请输入姓名', '采用U9默认联系地址'].includes(harvestData.value?.[0]?.addressName) && { address: `${harvestData.value[0].fullAddress}${harvestData.value[0].phone}${harvestData.value[0].addressName}` }),
            note: note.value,
            project: project_Name.value,
            order_lines: JSON.stringify(reduceArray)
        })
        if (error === null) {
            Taro.showToast({
                title: '提交成功',
                success: async () => {
                    console.log(Taro.getCurrentInstance().preloadData?.fromSCartPage);
                    if (Taro.getCurrentInstance().preloadData?.fromSCartPage !== undefined) {

                        let remove = reduceArray.map(i =>
                            [
                                -i.product_id,
                                i.product_qty
                            ]
                        )
                        let resultStr = '[' + remove.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
                        try {
                            const res = await updateFindCart({
                                product_ids: resultStr
                            })
                        } catch (error: any) {
                            Taro.showLoading({
                                title: error
                            })
                        }
                    }

                    setTimeout(() => {
                        Taro.switchTab({
                            url: '/pages/orders/index',
                            success: () => {
                                appStore.setActiveTab('/pages/orders/index');
                            }
                        })
                    }, 2000)

                },
                duration: 2000
            })
        } else {
            Taro.showToast({
                title: '网络异常',
                icon: 'error',
                duration: 2000
            })
        }
    }
}
const preloadMessage = Taro.getCurrentInstance().preloadData
const harvestData: Ref<Array<IharvestData>> = ref([{
    addressName: '请输入姓名',
    phone: '手机号',
    defaultAddress: true,
    fullAddress: '请输入详细地址',
    testid: null
}
])
const harvestOptions = {
    addressName: 'addressName'
}

//获取默认地址
// const getDefaultAddressF = async (userId: string) => {
//     const { error, success } = await getDefaultAddress({ userId })
//     if (error === null) {
//         Taro.setStorageSync('defaultAddress', success)

//         harvestData.value = [success as IharvestData]
//     }
// }


const countTotalPrice = () => {
    totalPrice.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) => acc + curr.price_unit * curr.count, 0);
    countJS.value = cartCommodities.value
        .filter(x => x.checkbox)
        .reduce((acc, curr) =>acc+curr.count, 0);
}
const totalPrice = ref(0)
const countJS=ref(0)
/** 设置页面属性 */
definePageConfig({
    navigationBarTitleText: '确认订单',
});


//楼层字段变更
const show = ref(false)
const searchValue = ref('')
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)
//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
    console.log('子组件返回值:id', msg.id);
    console.log('子组件返回值:index', msg.index);
    ClickshowDialogItem = msg.id
    click()

}
const click = () => {
    show.value = true
}
const GetInputFocus = () => {
    if (searchValue.value.length >= 0) {
        show.value = true
        if (searchValue.value.length > 0) {
            getProductByNumberF(searchValue)
        } else {
            searchList.value = []
            DQsearchListIndex.value = 1
        }

    } else {
        show.value = false
    }

}
const showSelect = ref(false)
const searchList: Ref<any[]> = ref([])
const clickItem = (item: any) => {

    console.log(item, ClickshowDialogItem, 'test');


    const { name, id } = item
    if (ClickshowDialogItem) {
        cartCommodities.value.find(item => {
            if (item.id === ClickshowDialogItem) {
                item.seiban = name
                item.fanhao_id = id
            }
        })
        ClickshowDialogItem = null
        searchValue.value = ''
        console.log('cartCommodities', cartCommodities.value);
        show.value = false
        searchList.value = []


    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'error',
            duration: 2000
        })
    }

}
const onScrollBottom = async () => {
    console.log('触底了');
    console.log('searchListIndex', searchListIndex.value);
    console.log('DQsearchListIndex', DQsearchListIndex.value);


    if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
        searchListIndex.value++
        const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
        if (error === null) {
            const { items, psize } = success as { items: Array<any>, psize: number }
            if (success && items) {
                searchList.value = searchList.value.concat(items)
                DQsearchListIndex.value = psize
            }
        }
    } else {
        Taro.showToast({
            title: '加载完毕了~',
            icon: 'error',
            duration: 2000
        })
    }

}
const getProductByNumberF = async (item: Ref<string>) => {
    const { error, success } = await getProductByNumber({ name: item.value, per_page: '20' })
    console.log('success                  ~~~', success);
    const { items, psize } = success as { items: Array<any>, psize: number }
    if (success && items) {
        searchList.value = items
        DQsearchListIndex.value = psize
    }
}

</script>

<style lang="scss">
page {
    background-color: #F0F0F0;
}

.myTextarea::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #9e9e9e;
}

.myTextarea:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #9e9e9e;
}

.myTextarea::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #9e9e9e;
}

.myTextarea::-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #9e9e9e;
}

.button-minus {
    // border: 1px solid #aba8a8;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    background-color: #F9FAFC;
}

.button-plus {
    // border: 1px solid #aba8a8;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    background-color: #F9FAFC;
}

.button-cell-middle {
    // border-top: 1px solid #aba8a8;
    // border-bottom: 1px solid #aba8a8;
    background-color: #F9FAFC;
    text-align: center;
    line-height: 17px;
    margin: 0 5%;
}

.card {
    margin: 0 0;
}

.button-cell {
    min-width: 25px;
    height: 17px;
}

.nut-cell-group__wrap {
    box-shadow: none;
}

.quantity-button-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    bottom: 10px;
    right: 30px;
}

.bottom-card {
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100vw;
    align-items: center;
    padding: .625rem;
    border-top: 1px solid #dcdcdc;
}

.nut-address-list {
    width: 100% !important;
}

.nut-address-list:last-child {
    padding-bottom: 0 !important;
}

.harvest-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.nut-input .input-text {
    color: #333333 !important;
}

.noShow {
    visibility: hidden !important;
}
</style>