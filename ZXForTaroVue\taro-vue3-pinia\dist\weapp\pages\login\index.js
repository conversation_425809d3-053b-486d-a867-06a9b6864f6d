"use strict";(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[3],{2384:function(e,n,t){var a=t(1065),o=t(6944),l=t(3191),r=t(3091),u=t(3221),i=t(7011),c=t(6821),s=t(2e3),d=t(5969),f=t(4421),v=t(6249),p=Object.defineProperty,m=Object.defineProperties,w=Object.getOwnPropertyDescriptors,g=Object.getOwnPropertySymbols,b=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable,k=function(e,n,t){return n in e?p(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},_=function(e,n){for(var t in n||(n={}))b.call(n,t)&&k(e,t,n[t]);if(g){var a,o=(0,r.Z)(g(n));try{for(o.s();!(a=o.n()).done;){t=a.value;y.call(n,t)&&k(e,t,n[t])}}catch(e){o.e(e)}finally{o.f()}}return e},h=function(e,n){return m(e,w(n))},x={class:"nut-switch-button"},V=(0,u.aZ)(h(_({},{name:"NutSwitch"}),{__name:"switch.taro",props:{modelValue:{type:[String,Number,Boolean],default:!1},disabled:{type:Boolean,default:!1},activeColor:{default:""},inactiveColor:{default:""},activeText:{default:""},inactiveText:{default:""},activeValue:{type:[String,Number,Boolean],default:!0},inactiveValue:{type:[String,Number,Boolean],default:!1},loading:{type:Boolean,default:!1},disable:{type:Boolean,default:!1}},emits:["change","update:modelValue","update:loading"],setup:function(e,n){var t=n.emit,a=e,o=t,r=(0,u.Fl)((function(){return a.disabled||a.disable})),v=(0,f.u)(r),p=(0,u.Fl)((function(){return a.modelValue===a.activeValue})),m=(0,u.Fl)((function(){var e="nut-switch";return(0,l.Z)((0,l.Z)((0,l.Z)((0,l.Z)({},e,!0),p.value?"nut-switch-open":"nut-switch-close",!0),"".concat(e,"-disabled"),v.value),"".concat(e,"-base"),!0)})),w=(0,u.Fl)((function(){return{backgroundColor:p.value?a.activeColor:a.inactiveColor}})),g="",b=function(e){if(!a.loading&&!v.value){var n=p.value?a.inactiveValue:a.activeValue;g="click",o("update:modelValue",n),o("change",n,e)}};return(0,u.YP)((function(){return a.modelValue}),(function(e){"click"==g?g="":o("change",e)})),function(e,n){return(0,u.wg)(),(0,u.iD)("view",{class:(0,i.C_)(m.value),style:(0,i.j5)(w.value),onClick:b},[(0,u._)("view",x,[e.loading?(0,u.WI)(e.$slots,"icon",{key:0},(function(){return[(0,u.Wm)((0,c.SU)(d.vG),{name:"loading1",color:e.activeColor},null,8,["color"])]})):(0,u.kq)("",!0),(0,u.Uk)(),e.activeText?((0,u.wg)(),(0,u.iD)(u.HY,{key:1},[(0,u.wy)((0,u._)("view",{class:"nut-switch-label open"},(0,i.zw)(e.activeText),513),[[s.F8,p.value]]),(0,u.Uk)(),(0,u.wy)((0,u._)("view",{class:"nut-switch-label close"},(0,i.zw)(e.inactiveText),513),[[s.F8,!p.value]])],64)):(0,u.kq)("",!0)])],6)}}}));(0,v.w)(V);t(3939);var Z=t(889),T=(t(9932),t(8743)),F=(t(3588),t(6538)),j=(t(1808),t(3496)),C=(t(5279),t(2419)),O=t(2810),S=t(1959),U=t.n(S),W=t(6144),z=t(9886),B=t(4081),P={class:"login-container"},q={class:"login-form"},H={style:{display:"flex","justify-content":"center","align-items":"baseline","margin-top":"5%"}},I=(0,u.aZ)({__name:"index",setup:function(e){var n=(0,z.t)(),t=(0,B.qr)(),a=function(){var e;null===(e=r.value)||void 0===e||e.validate().then((function(e){var a=e.valid,o=e.errors;a&&!v.value?(console.log("success:",a),U().login({success:function(){var e=(0,O.Z)((0,C.Z)().mark((function e(a){var o,r,u,i;return(0,C.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,W.x4)({username:l.value.username,password:l.value.password,db:"odoo17"});case 2:if(o=e.sent,r=o.error,u=o.success,console.log("\u8fd4\u56de\u503c",r,u),null!=r){e.next=17;break}return console.log(u,"success20241130"),n.token=u.token,U().setStorageSync("token",u.token),e.next=12,(0,W.bG)({token:u.token});case 12:i=e.sent,U().setStorageSync("userInfo",i.success.user),U().showToast({title:"\u767b\u5f55\u6210\u529f",icon:"success",duration:2e3,success:function(){setTimeout((function(){t.setActiveTab("/pages/products/index"),U().switchTab({url:"/pages/products/index"})}),2e3)}}),e.next=18;break;case 17:console.log("error~~~~~~~~~~`");case 18:case"end":return e.stop()}}),e)})));function a(n){return e.apply(this,arguments)}return a}(),fail:function(e){U().showToast({title:"\u7f51\u7edc\u5f02\u5e38",icon:"error",duration:2e3})},timeout:5e3})):console.warn("error:",a,o)}))},l=(0,c.iH)({username:"",password:""}),r=(0,c.iH)(null),s=function(){U().navigateTo({url:"/package/package-a/termsUse/index"})},f=(0,c.iH)(!1),v=(0,c.iH)(!0),p=function(){v.value=!v.value};return function(e,n){var t=j.Z,m=F.Z,w=T.Z,g=Z.Z,b=V,y=o.Z;return(0,u.wg)(),(0,u.j4)(y,null,{default:(0,u.w5)((function(){return[(0,u._)("div",P,[n[7]||(n[7]=(0,u._)("div",{class:"ava-card flex justify-center flex-col items-center"},[(0,u._)("Image",{mode:"aspectFit",src:"https://img01.71360.com/w3/w426o/20240402/17d52803bad6c4c72e6f647e58791345.jpg"}),(0,u._)("view",{class:"ava-card-title"},(0,i.zw)("\u5de6\u5411\u8ba2\u8d27\u7cfb\u7edf")),(0,u._)("view",{class:"ava-card-desc"},(0,i.zw)("\u4e3a\u4eba\u7c7b\u7684\u6d88\u9632\u5b89\u5168\u4e8b\u4e1a\uff0c\u594b\u6597\u4e0d\u606f\uff01"))],-1)),(0,u._)("div",q,[(0,u.Wm)(w,{class:"","model-value":l.value,ref_key:"loginFormRef",ref:r},{default:(0,u.w5)((function(){return[(0,u.Wm)(m,{prop:"username",required:"",rules:[{required:!0,message:"\u8bf7\u8f93\u5165\u7528\u6237\u540d"}]},{default:(0,u.w5)((function(){return[(0,u.Wm)(t,{modelValue:l.value.username,"onUpdate:modelValue":n[0]||(n[0]=function(e){return l.value.username=e}),placeholder:"\u8bf7\u8f93\u5165\u7528\u6237\u540d",type:"text"},{left:(0,u.w5)((function(){return[(0,u.Wm)((0,c.SU)(d.My))]})),_:1},8,["modelValue"])]})),_:1}),(0,u.Wm)(m,{prop:"password",rules:[{required:!0,message:"\u8bf7\u8f93\u5165\u5bc6\u7801"}]},{default:(0,u.w5)((function(){return[(0,u.Wm)(t,{modelValue:l.value.password,"onUpdate:modelValue":n[1]||(n[1]=function(e){return l.value.password=e}),placeholder:"\u8bf7\u8f93\u5165\u5bc6\u7801",type:"password",onBlur:n[2]||(n[2]=function(){})},{left:(0,u.w5)((function(){return n[4]||(n[4]=[(0,u._)("i",{class:"i-bx-key",style:{"font-size":"20px"}},null,-1)])})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["model-value"])]),(0,u._)("div",null,[(0,u.Wm)(g,{size:"large",block:"",color:"#1890FF",class:"login-button",disabled:v.value,onTap:a},{default:(0,u.w5)((function(){return n[5]||(n[5]=[(0,u.Uk)((0,i.zw)("\u70b9\u51fb\u767b\u5f55"))])})),_:1},8,["disabled"]),(0,u._)("div",H,[(0,u.Wm)(b,{modelValue:f.value,"onUpdate:modelValue":n[3]||(n[3]=function(e){return f.value=e}),onChange:p,"active-color":"#1890FF",style:{"margin-right":"2%"}},null,8,["modelValue"]),(0,u._)("view",{class:"annotation-desc"},[n[6]||(n[6]=(0,u.Uk)((0,i.zw)("\u767b\u5f55\u5373\u4ee3\u8868\u9605\u8bfb\u5e76\u540c\u610f"))),(0,u._)("span",{class:"desc-span",onClick:s},(0,i.zw)("\u4f7f\u7528\u534f\u8bae\u548c\u9690\u79c1"))])])])])]})),_:1})}}});const N=I;var D=N,G={navigationBarTitleText:"\u767b\u5f55"};Page((0,a.createPageConfig)(D,"pages/login/index",{root:{cn:[]}},G||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[107,216,592],(function(){return n(2384)}));e.O()}]);