SELECT *
FROM (
    SELECT 
        A.[ID] AS [ID],
        A.[SysVersion] AS [SysVersion],
        '' AS [ItemMasterID],
        '' AS [AbleUserNumber],
        '' AS [CurrentUseNumber],
        '' AS [StoreQty],
        '' AS [ResvStQty],
        '' AS [CurResvStQty],
        '' AS [ResvOccupyStQty],
        '' AS [ResvStMainQty],
        A2.[ReserveMode] AS [ItemMaster_InventoryInfo_ReserveMode],
        A3.[Round_Precision] AS [StoreUOM_Precision],
        A3.[Round_RoundType] AS [StoreUOM_RoundType],
        A3.[Round_RoundValue] AS [StoreUOM_RoundValue],
        A.[ID] AS [PickListID],
        A1.[Code] AS [Code],
        A1.[Name] AS [Name],
        A.[ItemMaster] AS [ItemMaster],
        A.[FromGrade] AS [FromGrade],
        A.[ToGrade] AS [ToGrade],
        A.[FromElement] AS [FromElement],
        A.[ToElement] AS [ToElement],
        A.[Project] AS [Project],
        A.[Task] AS [Task],
        A.[IsControlSupplier] AS [IsControlSupplier],
        A.[RcvUOM] AS [RcvUOM],
        A.[MO] AS [MO],
        A.[SupplyOrg] AS [SupplyOrg],
        A.[ItemVersion] AS [ItemVersion],
        A4.[Version] AS [ItemVersionVersion],
        A.[JIT] AS [JIT],
        A.[SupplyWh] AS [SupplyWh],
        A.[SupplyBin] AS [SupplyBin],
        A1.[IsSpecialItem] AS [ItemIsSpecialItem],
        A5.[DesignationRule] AS [ItemMfgInfoDesignationRule],
        A5.[IsInheritBomMasterNo] AS [ItemMfgInfoIsInheritBomMasterNo],
        A6.[Seiban] AS [MOSeiban],
        A1.[IsGradeControl] AS [ItemIsGradeControl],
        A1.[IsPotencyControl] AS [ItemIsPotencyControl],
        A1.[StartGrade] AS [ItemStartGrade],
        A1.[EndGrade] AS [ItemEndGrade],
        A1.[StartPotency] AS [ItemStartPotency],
        A1.[EndPotency] AS [ItemEndPotency],
        A.[SUToSBURate] AS [SUToSBURate],
        A.[IBUToSBURate] AS [IBUToSBURate],
        A7.[Code] AS [ProjectCode],
        A8.[Code] AS [MOProjectCode],
        '' AS [IsTrans],
        A6.[DocNo] AS [MODocNo],
        A1.[InventorySecondUOM] AS [ItemStoreUOM],
        A9.[Round_Precision] AS [ItemStoreUOM_Precision],
        A9.[Round_RoundType] AS [ItemStoreUOM_RoundType],
        A9.[Round_RoundValue] AS [ItemStoreUOM_RoundValue],
        A6.[DocNo] AS [MO_DocNo],
        A1.[Code] AS [ItemMaster_Code],
        A1.[Name] AS [ItemMaster_Name],
        A4.[Version] AS [ItemVersion_Version],
        A.[OperationNum] AS [OperationNum],
        A11.[Name] AS [SupplyOrg_Name],
        A14.[Name] AS [SupplyWh_Name],
        A16.[Name] AS [SupplyBin_Name],
        A17.[Name] AS [ItemMaster_InventorySecondUOM_Name],
        A.[ActualReqQty] AS [ActualReqQty],
        '' AS [Custom_StoreQty],
        '' AS [Custom_ResvStQty],
        A.[IssueUOM] AS [IssueUOM],
        A18.[Round_Precision] AS [IssueUOM_Round_Precision],
        A18.[Round_RoundType] AS [IssueUOM_Round_RoundType],
        A18.[Round_RoundValue] AS [IssueUOM_Round_RoundValue],
        A12.[Code] AS SysMlFlag,
        ROW_NUMBER() OVER (
            ORDER BY 
                A.[MO] ASC,
                (A.[ID] + 17) ASC
        ) AS rownum
    FROM MO_MOPickList AS A
    INNER JOIN [CBO_ItemMaster] AS A1 
        ON A.[ItemMaster] = A1.[ID]
    LEFT JOIN [CBO_InventoryInfo] AS A2 
        ON A1.[InventoryInfo] = A2.[ID]
    LEFT JOIN [Base_UOM] AS A3 
        ON A.[RcvUOM] = A3.[ID]
    LEFT JOIN [CBO_ItemMasterVersion] AS A4 
        ON A.[ItemVersion] = A4.[ID]
    LEFT JOIN [CBO_MfgInfo] AS A5 
        ON A1.[MfgInfo] = A5.[ID]
    LEFT JOIN [MO_MO] AS A6 
        ON A.[MO] = A6.[ID]
    LEFT JOIN [CBO_Project] AS A7 
        ON A.[Project] = A7.[ID]
    LEFT JOIN [CBO_Project] AS A8 
        ON A6.[Project] = A8.[ID]
    LEFT JOIN [Base_UOM] AS A9 
        ON A1.[InventorySecondUOM] = A9.[ID]
    LEFT JOIN [Base_Organization] AS A10 
        ON A.[SupplyOrg] = A10.[ID]
    LEFT JOIN Base_Language AS A12 
        ON A12.Code = 'zh-CN' 
        AND A12.Effective_IsEffective = 1
    LEFT JOIN [Base_Organization_Trl] AS A11 
        ON A11.SysMlFlag = 'zh-CN' 
        AND A11.SysMlFlag = A12.Code 
        AND A10.[ID] = A11.[ID]
    LEFT JOIN [CBO_Wh] AS A13 
        ON A.[SupplyWh] = A13.[ID]
    LEFT JOIN [CBO_Wh_Trl] AS A14 
        ON A14.SysMlFlag = 'zh-CN' 
        AND A14.SysMlFlag = A12.Code 
        AND A13.[ID] = A14.[ID]
    LEFT JOIN [CBO_Bin] AS A15 
        ON A.[SupplyBin] = A15.[ID]
    LEFT JOIN [CBO_Bin_Trl] AS A16 
        ON A16.SysMlFlag = 'zh-CN' 
        AND A16.SysMlFlag = A12.Code 
        AND A15.[ID] = A16.[ID]
    LEFT JOIN [Base_UOM_Trl] AS A17 
        ON A17.SysMlFlag = 'zh-CN' 
        AND A17.SysMlFlag = A12.Code 
        AND A9.[ID] = A17.[ID]
    LEFT JOIN [Base_UOM] AS A18 
        ON A.[IssueUOM] = A18.[ID]
    LEFT JOIN [CBO_BOMComponent] AS A19 
        ON A.[BOMComponent] = A19.[ID]
    LEFT JOIN [CBO_ItemMaster] AS A20 
        ON A19.[ItemMaster] = A20.[ID]
    WHERE 
        (A20.[Code] = N'3.05.01.0122') 
        AND (1 = 1)
) T
WHERE 
    T.rownum > @PageLowerBound 
    AND T.rownum <= @PageUpperBound
