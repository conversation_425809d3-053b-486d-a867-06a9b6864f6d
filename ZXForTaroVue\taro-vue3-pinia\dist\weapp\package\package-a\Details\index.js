"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[642],{9648:function(e,n,t){var o=t(1065),a=t(6944),i=t(889),r=(t(9932),t(7643)),l=(t(3939),t(1845)),c=(t(3505),t(9841)),u=(t(30),t(5841)),s=t(9803),p=t(8751),v=(t(2148),t(3091)),d=t(6821),f=t(3221),g=t(2e3),m=t(7011),h=t(1959),w=t.n(h),b=t(5969),y=t(139),_=t(8140),x=t(2739),k=function(e,n){var t=n.args,o=void 0===t?[]:t,a=n.done,i=n.canceled;if(e){var r=e.apply(void 0,(0,_.Z)(o));(0,x.d)(r)?r.then((function(e){e?a(e):i&&i()})).catch((function(){})):r?a():i&&i()}else a()},Z=t(9046),P=t(7866),C=t(8978),S=t(4884),T=t(1065)["document"],D=Object.defineProperty,H=Object.defineProperties,O=Object.getOwnPropertyDescriptors,j=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable,W=function(e,n,t){return n in e?D(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},z=function(e,n){for(var t in n||(n={}))N.call(n,t)&&W(e,t,n[t]);if(j){var o,a=(0,v.Z)(j(n));try{for(a.s();!(o=a.n()).done;){t=o.value;U.call(n,t)&&W(e,t,n[t])}}catch(e){a.e(e)}finally{a.f()}}return e},F=function(e,n){return H(e,O(n))},B=(0,y.c)("image-preview"),I=B.create,V=I({props:{show:{type:Boolean,default:!1},images:{type:Array,default:function(){return[]}},contentClose:{type:Boolean,default:!0},initNo:{type:Number,default:0},paginationVisible:{type:Boolean,default:!1},paginationColor:{type:String,default:"#fff"},autoplay:{type:[Number,String],default:3e3},showIndex:{type:Boolean,default:!0},closeable:{type:Boolean,default:!1},closeIconPosition:{type:String,default:"top-right"},beforeClose:Function,isLoop:{type:Boolean,default:!0}},emits:["close","change","longPress"],components:{NutPopup:Z.N,NutSwiper:P.Z,NutSwiperItem:C.Z,CircleClose:b.K4},setup:function(e,n){var t=n.emit,o=(0,d.qj)({showPop:!1,active:0,options:{muted:!0,controls:!0},eleImg:null,store:{scale:1,moveable:!1,originScale:1,oriDistance:1},lastTouchEndTime:0,ENV:w().getEnv(),ENV_TYPE:w().ENV_TYPE}),a=(0,f.Fl)((function(){var n={};return"top-right"==e.closeIconPosition?n.right="10px":n.left="10px",n})),i=function(e){e!==o.active&&(o.active=e,t("change",o.active))},r=function(){e.contentClose&&l()},l=function(){k(e.beforeClose,{args:[o.active],done:function(){return c()}})},c=function(){o.showPop=!1,o.store.scale=1,s(),t("close")},u=function(e,n){return Math.hypot(Math.abs(n.x-e.x),Math.abs(n.y-e.y))},s=function(){null!=o.eleImg&&(o.eleImg.style.transform="scale("+o.store.scale+")")},p=function(e){var n=(new Date).getTime(),t=e.touches,a=t[0],i=t[1],r=o.store;n-o.lastTouchEndTime<300&&(r.scale>1?r.scale=1:1==r.scale&&(r.scale=2),s()),r.moveable=!0,i&&(r.oriDistance=u({x:a.pageX,y:a.pageY},{x:i.pageX,y:i.pageY})),r.originScale=r.scale||1},v=function(e){if(o.store.moveable){var n=o.store,t=e.touches,a=t[0],i=t[1];if(i){var r=u({x:a.pageX,y:a.pageY},{x:i.pageX,y:i.pageY}),l=r/n.oriDistance;n.scale=n.originScale*l,o.store.scale=Math.min(o.store.scale,3),s()}}},g=function(){o.lastTouchEndTime=(new Date).getTime();var e=o.store;e.moveable=!1,(e.scale<1.1&&e.scale>1||e.scale<1)&&(e.scale=1,s())},m=function(e){t("longPress",e)},h=function(){o.eleImg=T.querySelector(".nut-image-preview"),T.addEventListener("touchmove",v),T.addEventListener("touchend",g),T.addEventListener("touchcancel",g)};return(0,f.YP)((function(){return e.show}),(function(n){o.showPop=n,n&&(i(e.initNo),h())})),(0,f.YP)((function(){return e.initNo}),(function(e){e!=o.active&&i(e)})),(0,f.bv)((function(){i(e.initNo)})),F(z({},(0,d.BK)(o)),{setActive:i,onClose:l,closeOnImg:r,onTouchStart:p,onTouchMove:v,onTouchEnd:g,getDistance:u,scaleNow:s,longPress:m,styles:a})}}),E=["src","on:longPress","on:longTap"],q={key:0,class:"nut-image-preview-index"};function L(e,n,t,o,a,i){var r=(0,f.up)("nut-swiper-item"),l=(0,f.up)("nut-swiper"),c=(0,f.up)("CircleClose"),u=(0,f.up)("nut-popup");return(0,f.wg)(),(0,f.j4)(u,{visible:e.showPop,"onUpdate:visible":n[3]||(n[3]=function(n){return e.showPop=n}),"pop-class":"nut-image-preview-custom-pop"},{default:(0,f.w5)((function(){return[(0,f._)("view",{class:"nut-image-preview",onTouchstartCapture:n[1]||(n[1]=function(){return e.onTouchStart&&e.onTouchStart.apply(e,arguments)})},[e.showPop?((0,f.wg)(),(0,f.j4)(l,{key:0,"auto-play":e.autoplay,class:"nut-image-preview-swiper",loop:e.isLoop,"is-prevent-default":!1,direction:"horizontal","init-page":e.initNo,"pagination-visible":e.paginationVisible,"pagination-color":e.paginationColor,onChange:e.setActive},{default:(0,f.w5)((function(){return[((0,f.wg)(!0),(0,f.iD)(f.HY,null,(0,f.Ko)(e.images,(function(t,o){return(0,f.wg)(),(0,f.j4)(r,{key:o},{default:(0,f.w5)((function(){return[(0,f._)("img",{src:t.src,mode:"aspectFit",class:"nut-image-preview-img","on:longPress":function(n){return e.longPress(t)},"on:longTap":function(n){return e.longPress(t)},onClick:n[0]||(n[0]=(0,g.iM)((function(){return e.closeOnImg&&e.closeOnImg.apply(e,arguments)}),["stop"]))},null,40,E)]})),_:2},1024)})),128))]})),_:1},8,["auto-play","loop","init-page","pagination-visible","pagination-color","onChange"])):(0,f.kq)("",!0)],32),(0,f.Uk)(),e.showIndex?((0,f.wg)(),(0,f.iD)("view",q,(0,m.zw)(e.active+1)+" / "+(0,m.zw)(e.images.length),1)):(0,f.kq)("",!0),(0,f.Uk)(),e.closeable?((0,f.wg)(),(0,f.iD)("view",{key:1,class:"nut-image-preview-close-icon",style:(0,m.j5)(e.styles),onClick:n[2]||(n[2]=function(){return e.onClose&&e.onClose.apply(e,arguments)})},[(0,f.Wm)(c,{color:"#ffffff"})],4)):(0,f.kq)("",!0)]})),_:1},8,["visible"])}var Y=(0,S._)(V,[["render",L]]),M=t(2344),A=(t(2240),t(8187)),R=(t(3796),t(8400)),X=(t(6e3),t(3191)),K=t(6249),J=Object.defineProperty,$=Object.defineProperties,G=Object.getOwnPropertyDescriptors,Q=Object.getOwnPropertySymbols,ee=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable,te=function(e,n,t){return n in e?J(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},oe=function(e,n){for(var t in n||(n={}))ee.call(n,t)&&te(e,t,n[t]);if(Q){var o,a=(0,v.Z)(Q(n));try{for(a.s();!(o=a.n()).done;){t=o.value;ne.call(n,t)&&te(e,t,n[t])}}catch(e){a.e(e)}finally{a.f()}}return e},ae=function(e,n){return $(e,G(n))},ie=(0,f.aZ)(ae(oe({},{name:"NutDivider"}),{__name:"divider.taro",props:{contentPosition:{default:"center"},dashed:{type:Boolean,default:!1},hairline:{type:Boolean,default:!0},direction:{default:"horizontal"}},setup:function(e){var n=e,t=(0,f.Rr)(),o=(0,f.Fl)((function(){var e="nut-divider";return"horizontal"===n.direction?(0,X.Z)((0,X.Z)((0,X.Z)((0,X.Z)((0,X.Z)((0,X.Z)({},e,!0),"".concat(e,"-center"),t.default),"".concat(e,"-left"),"left"===n.contentPosition),"".concat(e,"-right"),"right"===n.contentPosition),"".concat(e,"-dashed"),n.dashed),"".concat(e,"-hairline"),n.hairline):(0,X.Z)((0,X.Z)({},e,!0),"".concat(e,"-vertical"),"vertical"===n.direction)}));return function(e,n){return(0,f.wg)(),(0,f.iD)("view",{class:(0,m.C_)(o.value)},["horizontal"===e.direction?(0,f.WI)(e.$slots,"default",{key:0}):(0,f.kq)("",!0)],2)}}}));(0,K.w)(ie);var re=t(9909),le=(t(3362),t(9328)),ce=(t(8277),t(3193)),ue=(t(9157),t(2419)),se=t(2810),pe=t(4081),ve=t(4733),de=t(7534),fe=t.n(de),ge={class:"search-header"},me={class:"from-box"},he={style:{height:"340px"}},we={key:0},be=["onClick"],ye={key:1,style:{"margin-top":"5%",color:"#333333","font-weight":"900"}},_e={key:0,class:"product-section"},xe={key:0,class:"product-container"},ke={class:"product-list-wrapper"},Ze=["scroll-top"],Pe={class:"product-card-wrapper"},Ce={class:"product-checkbox"},Se={class:"product-image-section"},Te={class:"image-container"},De=["src","onLongpress"],He={key:0,class:"image-overlay"},Oe={class:"product-info-section"},je={class:"product-title-area"},Ne={class:"model-tag"},Ue={class:"name-tag"},We={class:"product-spec"},ze={class:"price-code-section"},Fe={class:"price-area"},Be={class:"price"},Ie={class:"code-area"},Ve={class:"quantity-control"},Ee={key:0,class:"loading-more"},qe={key:1,class:"load-complete"},Le={key:1,class:"empty-state"},Ye={class:"bottom-action-bar"},Me={class:"action-bar-content"},Ae={class:"left-section"},Re={class:"price-section"},Xe={class:"total-price"},Ke={class:"button-group"},Je=(0,f.aZ)({__name:"index",setup:function(e){(0,h.useRouter)(),(0,pe.tN)();var n=(0,d.iH)(!1),t=fe().throttle((function(e){var t=e.detail||e,o=t.scrollTop,a=t.scrollHeight;console.log("\u6eda\u52a8\u4e8b\u4ef6\u89e6\u53d1:",{scrollTop:o,scrollHeight:a,isLoading:v.value,currentPage:x.value,totalPage:k.value}),n.value=o>=300}),300),o=fe().throttle((function(){console.log("\u89e6\u5e95\u4e8b\u4ef6\u89e6\u53d1",{isLoading:v.value,currentPage:x.value,totalPage:k.value}),!v.value&&x.value<k.value?(console.log("\u5f00\u59cb\u52a0\u8f7d\u66f4\u591a\u6570\u636e"),y()):x.value>=k.value&&console.log("\u5df2\u52a0\u8f7d\u5168\u90e8\u6570\u636e")}),500),v=(0,d.iH)(!1),y=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(){return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("onScrollBottomPage \u88ab\u8c03\u7528",{currentPage:x.value,totalPage:k.value,isLoading:v.value}),!(x.value<k.value)||v.value){e.next=8;break}return x.value=Number(x.value)+1,console.log("\u5f00\u59cb\u52a0\u8f7d\u7b2c",x.value,"\u9875"),e.next=6,Z(x.value);case 6:e.next=10;break;case 8:console.log("\u65e0\u6cd5\u52a0\u8f7d\u66f4\u591a:",{reachedEnd:x.value>=k.value,isLoading:v.value}),x.value>=k.value&&w().showToast({title:"\u5168\u90e8\u52a0\u8f7d\u5b8c\u6bd5~",icon:"success",duration:2e3});case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),x=(0,d.iH)(1),k=(0,d.iH)(1),Z=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(){var n,t,o,a,i,r,l,c,u,s,p=arguments;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=p.length>0&&void 0!==p[0]?p[0]:1,v.value=!0,e.prev=2,!C.value){e.next=12;break}return e.next=6,(0,ve.wv)({name:C.value,page:Number(n).toString()});case 6:t=e.sent,o=t.error,a=t.success,null===o&&(console.log("\u6570\u636e\u52a0\u8f7d\u7ed3\u679c",a),r=a,l=r.items,c=r.psize,u=r.cur_page,k.value=c,x.value=u,s=[],s=l.map((function(e){return Object.assign(e,{count:1,maxBuy:1e4,checkbox:!1,design:"",leaveMsg:""})})),(i=D.value).push.apply(i,(0,_.Z)(s)),s=null),e.next=14;break;case 12:return e.next=14,P(C.model);case 14:e.next=20;break;case 16:e.prev=16,e.t0=e["catch"](2),console.error("\u52a0\u8f7d\u4ea7\u54c1\u6570\u636e\u5931\u8d25:",e.t0),w().showToast({title:"\u52a0\u8f7d\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5",icon:"error",duration:2e3});case 20:return e.prev=20,v.value=!1,e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[2,16,20,23]])})));return function(){return e.apply(this,arguments)}}();(0,d.iH)();(0,f.bv)((0,se.Z)((0,ue.Z)().mark((function e(){return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("\u9875\u9762\u6302\u8f7d\u5f00\u59cb"),e.next=3,Z();case 3:return e.next=5,Je();case 5:console.log("\u9875\u9762\u6302\u8f7d\u5b8c\u6210");case 6:case"end":return e.stop()}}),e)})))),(0,f.Ah)((function(){D.value=[]}));var P=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(n){var t,o,a,i,r,l,c,u,s;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ve.wv)({name:n,page:Number(1).toString()});case 2:t=e.sent,o=t.error,a=t.success,null===o&&(r=a,l=r.items,c=r.psize,u=r.cur_page,ee.value=1,ne.value=c,k.value=c,x.value=u,console.log("\u6570\u636e\u52a0\u8f7d\u7ed3\u679c~~",c,ne.value),s=[],s=l.map((function(e){return Object.assign(e,{count:1,maxBuy:1e4,checkbox:!1,design:"",leaveMsg:"",cartCommodities:!1})})),(i=D.value).push.apply(i,(0,_.Z)(s)),s=null);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),C=(0,d.qj)(w().getCurrentInstance().preloadData),S=((0,d.iH)(1),function(){w().switchTab({url:"/pages/shoppingCart/index"})}),T=(0,d.iH)(!1),D=((0,d.iH)(!1),(0,d.iH)([]));(0,f.wF)((function(){}));var H=(0,d.iH)(0),O=((0,d.iH)([]),function(){F.value=!0}),j=function(){try{var e=D.value.filter((function(e){return!0===e.checkbox})),n=e.reduce((function(e,n){var t=parseFloat(String(n.price_unit))||0,o=parseInt(String(n.count))||0;return e+t*o}),0);H.value=n,console.log("\u4ef7\u683c\u8ba1\u7b97:",{checkedItems:e.length,total:n,items:e.map((function(e){return{name:e.name,price:e.price_unit,count:e.count,subtotal:(parseFloat(String(e.price_unit))||0)*(parseInt(String(e.count))||0)}}))})}catch(e){console.error("\u4ef7\u683c\u8ba1\u7b97\u9519\u8bef:",e),H.value=0}},N=function(e,n){n.checkbox=e,U(),j()},U=function(){var e=D.value.filter((function(e){return e.checkbox})).length;T.value=e===D.value.length&&D.value.length>0},W=function(e){D.value.forEach((function(n){e===n.id&&(1!==n.count?n.count-=1:w().showToast({title:"\u6700\u5c11\u8d2d\u4e70\u4e00\u4e2a\u5546\u54c1~",icon:"none",duration:2e3}))})),j()},z=function(e){D.value.forEach((function(n){e===n.id&&(n.count!==n.maxBuy?n.count=Number(n.count)+1:w().showToast({title:"\u5f53\u524d\u6700\u591a\u4ec5\u80fd\u8d2d\u4e70"+n.maxBuy+"\u4efd\u8be5\u5546\u54c1~",icon:"none",duration:2e3}))})),j()},F=((0,d.iH)(0),(0,d.iH)(1),(0,d.iH)(!1)),B=function(){var e=D.value.filter((function(e){return e.checkbox}));console.log("\u8ba2\u5355\u4fe1\u606f",e),w().navigateTo({url:"/package/package-a/confirmOrder/index"}),w().preload({message:e})},I=(0,d.iH)(0),V=(0,d.iH)(),E=function(){console.log("\u6eda\u52a8\u5230\u9876\u90e8")},q=function(){console.log("\u8fd4\u56de\u9876\u90e8\u6309\u94ae\u70b9\u51fb"),I.value=Math.random(),setTimeout((function(){I.value=0}),50)},L=null,X=function(e){console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:id",e.id),console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:index",e.index),L=e.id,J()},K=(0,d.iH)(!1),J=function(){K.value=!0},$=(0,d.iH)(""),G=(0,d.iH)(!1),Q=(0,d.iH)([]),ee=(0,d.iH)(1),ne=(0,d.iH)(1),te=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(n){var t,o,a,i,r;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ve.yS)({name:n.value,per_page:"20"});case 2:t=e.sent,t.error,o=t.success,a=o,i=a.items,r=a.psize,o&&i&&(Q.value=i,ne.value=r);case 7:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),oe=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(){var n,t,o,a,i,r;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("\u89e6\u5e95\u4e86"),!(Number(ee.value)<Number(ne.value))){e.next=11;break}return ee.value++,e.next=5,(0,ve.yS)({name:$.value,page:ee.value.toString(),per_page:"20"});case 5:n=e.sent,t=n.error,o=n.success,null===t&&(a=o,i=a.items,r=a.psize,o&&i&&(Q.value=Q.value.concat(i),ne.value=r)),e.next=12;break;case 11:w().showToast({title:"\u52a0\u8f7d\u5b8c\u6bd5\u4e86~",icon:"error",duration:2e3});case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ae=function(){$.value.length>=0?(K.value=!0,$.value.length>0?te($):(Q.value=[],ne.value=1)):K.value=!1},de=function(e){var n=e.name,t=e.id;L?(D.value.find((function(e){e.id===L&&(e.design=n,e.fanhao_id=t)})),L=null,$.value="",console.log("cartCommodities",D.value),K.value=!1,Q.value=[]):w().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"error",duration:2e3})},Je=function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(){var n,t,o,a;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,ve.hT)();case 2:n=e.sent,t=n.error,o=n.success,null===t&&o&&(console.log(o),$e.value=(null===(a=o.items)||void 0===a?void 0:a.length)||0);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$e=(0,d.iH)(0),Ge=((0,d.iH)(!1),function(){var e=(0,se.Z)((0,ue.Z)().mark((function e(){var n,t,o,a,i,r;return(0,ue.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=D.value.filter((function(e){return e.checkbox})),console.log(n,"~~~"),t=n.map((function(e){return[e.id,e.count]})),!(t.length>0)){e.next=22;break}return o="["+t.map((function(e){return"("+e.join(",")+")"})).join(",")+"]",console.log(o),e.next=8,(0,ve.tB)({product_ids:o});case 8:if(a=e.sent,i=a.error,r=a.success,null!==i){e.next=17;break}return w().showToast({title:"\u64cd\u4f5c\u6210\u529f",icon:"success",duration:2e3}),e.next=15,Je();case 15:e.next=18;break;case 17:w().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"error",duration:2e3});case 18:console.log("~~~",i,r),console.log(t),e.next=23;break;case 22:w().showToast({title:"\u8fd8\u672a\u9009\u8d2d\u5546\u54c1",icon:"error",duration:2e3});case 23:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()),Qe=((0,d.iH)(""),(0,d.qj)({showPreview:!1,imgData:[]})),en=function(e){Qe.showPreview=!0,Qe.imgData=[{src:e}]};(0,d.iH)(""),(0,d.iH)("");return function(e,h){var w=ce.Z,y=le.Z,_=re.Z,Z=ie,T=R.Z,U=A.Z,L=M.Z,J=Y,ee=p.Z,ne=s.Z,te=u.Z,ue=c.Z,se=l.Z,pe=r.Z,ve=i.Z,fe=a.Z;return(0,f.wg)(),(0,f.j4)(fe,{style:{"background-color":"#f7f7f7"}},{default:(0,f.w5)((function(){return[(0,f.Wm)(y,{top:"0",class:"search-sticky"},{default:(0,f.w5)((function(){return[(0,f._)("div",ge,[(0,f.Wm)(w,{modelValue:C.value,"onUpdate:modelValue":h[1]||(h[1]=function(e){return C.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u4ea7\u54c1\u578b\u53f7/\u540d\u79f0/\u89c4\u683c/\u6599\u53f7",shape:"round","input-background":"#f5f5f5",class:"custom-searchbar"},{rightout:(0,f.w5)((function(){return[(0,f._)("div",{class:"search-btn",onClick:h[0]||(h[0]=function(){x.value=1,D.value.length=0,P(C.value)})},h[9]||(h[9]=[(0,f._)("i",{class:"i-bx-search mr-1"},null,-1),(0,f.Uk)(" \u67e5\u8be2 ")]))]})),rightin:(0,f.w5)((function(){return[(0,f.Wm)((0,d.SU)(b.LP))]})),_:1},8,["modelValue"])])]})),_:1}),(0,f._)("div",me,[(0,f.wy)((0,f.Wm)(_,{type:"left",position:{top:"140px"},onClick:q,class:"back-to-top"},{btn:(0,f.w5)((function(){return h[10]||(h[10]=[(0,f._)("div",{class:"back-btn"},[(0,f._)("i",{class:"i-bx-up-arrow-alt"}),(0,f._)("span",{class:"text"},"\u9876\u90e8")],-1)])})),_:1},512),[[g.F8,n.value]]),(0,f.Wm)(U,{visible:K.value,"onUpdate:visible":h[3]||(h[3]=function(e){return K.value=e}),class:"myAction"},{default:(0,f.w5)((function(){return[(0,f._)("div",he,[(0,f.Wm)(w,{modelValue:$.value,"onUpdate:modelValue":h[2]||(h[2]=function(e){return $.value=e}),shape:"square",label:"\u8bf7\u8f93\u5165\u697c\u5c42\u4ee5\u641c\u7d22","input-background":"#F0F0F0",onChange:ae,id:"pop-target"},{leftin:(0,f.w5)((function(){return[(0,f.Wm)((0,d.SU)(b.LP))]})),_:1},8,["modelValue"]),(0,f._)("div",{class:(0,m.C_)({toShowDiv:!G.value}),style:{width:"80%",height:"300px","background-color":"#FFFFFF",position:"absolute",left:"50%",transform:"translateX(-50%)","z-index":"999"}},[(0,f._)("div",null,[Q.value.length>0?((0,f.wg)(),(0,f.iD)("div",we,[(0,f.Wm)(T,{"list-data":Q.value,"container-height":300,onScrollBottom:oe},{default:(0,f.w5)((function(e){var n=e.item;return[(0,f._)("div",{class:"list-item",onClick:function(e){return de(n)}},(0,m.zw)(n.name),9,be),(0,f.Wm)(Z,{dashed:!0})]})),_:1},8,["list-data"])])):((0,f.wg)(),(0,f.iD)("div",ye,(0,m.zw)("\u8bf7\u8f93\u5165\u76f8\u5e94\u6b63\u697c\u5c42\u7f16\u53f7\u540e\u8fdb\u884c\u67e5\u8be2")))])],2)])]})),_:1},8,["visible"]),((0,f.wg)(),(0,f.iD)("div",_e,[(0,f.Wm)(L,{title:"\u63d0\u4ea4\u8ba2\u5355",content:"\u786e\u8ba4\u63d0\u4ea4\u8ba2\u8d27\u4fe1\u606f\u5e76\u751f\u6210\u8ba2\u5355\u5417\uff1f",visible:F.value,"onUpdate:visible":h[4]||(h[4]=function(e){return F.value=e}),onOk:B,class:"custom-dialog"},null,8,["visible"]),D.value.length>0?((0,f.wg)(),(0,f.iD)("div",xe,[(0,f.Wm)(J,{show:Qe.showPreview,images:Qe.imgData,onClose:h[5]||(h[5]=function(e){return Qe.showPreview=!1})},null,8,["show","images"]),(0,f._)("div",ke,[(0,f._)("scroll-view",{class:"product-list","scroll-y":!0,onScroll:h[7]||(h[7]=function(){return(0,d.SU)(t)&&(0,d.SU)(t).apply(void 0,arguments)}),onScrolltolower:h[8]||(h[8]=function(){return(0,d.SU)(o)&&(0,d.SU)(o).apply(void 0,arguments)}),onScrolltoupper:E,ref_key:"scrollContainer",ref:V,"lower-threshold":100,"upper-threshold":50,"enable-back-to-top":!1,"scroll-top":I.value,"scroll-with-animation":!0},[((0,f.wg)(!0),(0,f.iD)(f.HY,null,(0,f.Ko)(D.value,(function(e,n){return(0,f.wg)(),(0,f.iD)("div",{key:e.id,class:"product-item"},[(0,f._)("div",Pe,[(0,f._)("div",Ce,[(0,f.Wm)(ee,{onChange:function(n){return N(n,e)},modelValue:e.checkbox,"onUpdate:modelValue":function(n){return e.checkbox=n},"icon-size":"25"},null,8,["onChange","modelValue","onUpdate:modelValue"])]),(0,f._)("div",Se,[(0,f._)("div",Te,[(0,f._)("image",{src:e.image,class:"product-image",onLongpress:function(n){return en(e.image)}},null,40,De),e.image?(0,f.kq)("",!0):((0,f.wg)(),(0,f.iD)("div",He,h[11]||(h[11]=[(0,f._)("i",{class:"i-bx-image-alt"},null,-1)])))])]),(0,f._)("div",Oe,[(0,f._)("div",je,[(0,f._)("div",Ne,[h[12]||(h[12]=(0,f._)("i",{class:"i-bx-crown"},null,-1)),(0,f._)("span",null,(0,m.zw)(e.model),1)]),(0,f._)("div",Ue,(0,m.zw)(e.name.split("/")[0]),1),(0,f.wy)((0,f.Wm)(ne,{color:"rgba(18, 47, 56, 0.1)","text-color":"#122F38",class:"sub-name-tag"},{default:(0,f.w5)((function(){return[(0,f.Uk)((0,m.zw)(e.name.split("/")[1]),1)]})),_:2},1536),[[g.F8,e.name.split("/")[1]]])]),(0,f._)("div",We,[(0,f.Wm)(ne,{color:"#f8f9fa","text-color":"#666",class:"spec-tag"},{default:(0,f.w5)((function(){return[(0,f.Uk)((0,m.zw)(e.spec),1)]})),_:2},1024)]),(0,f._)("div",ze,[(0,f._)("div",Fe,[h[13]||(h[13]=(0,f._)("span",{class:"currency"},"\xa5",-1)),(0,f._)("span",Be,(0,m.zw)(e.price_unit),1),h[14]||(h[14]=(0,f._)("span",{class:"unit"},"/PCS",-1))]),(0,f._)("div",Ie,[(0,f.Wm)(ne,{color:"#FF6B35","text-color":"#fff",class:"code-tag"},{default:(0,f.w5)((function(){return[(0,f.Uk)((0,m.zw)(e.code),1)]})),_:2},1024)])]),(0,f.Wm)(te,{design:e.design,id:e.id,index:n,"onUpdate:design":X,class:"noShow"},null,8,["design","id","index"])]),(0,f._)("div",Ve,[(0,f.Wm)(ue,{modelValue:e.count,"onUpdate:modelValue":function(n){return e.count=n},min:1,max:2e5,"input-width":"55",onBlur:h[6]||(h[6]=function(e){return j()}),onAdd:function(){return z(e.id)},onReduce:function(){return W(e.id)},class:"custom-input-number"},null,8,["modelValue","onUpdate:modelValue","onAdd","onReduce"])])])])})),128)),v.value&&D.value.length>0?((0,f.wg)(),(0,f.iD)("div",Ee,h[15]||(h[15]=[(0,f._)("div",{class:"loading-spinner"},null,-1),(0,f._)("span",{class:"loading-text"},"\u52a0\u8f7d\u4e2d...",-1)]))):(0,f.kq)("",!0),!v.value&&x.value>=k.value&&D.value.length>0?((0,f.wg)(),(0,f.iD)("div",qe,h[16]||(h[16]=[(0,f._)("span",null,"\u5df2\u52a0\u8f7d\u5168\u90e8\u6570\u636e",-1)]))):(0,f.kq)("",!0)],40,Ze)])])):((0,f.wg)(),(0,f.iD)("div",Le,[(0,f.Wm)(se,{description:"\u6682\u65e0\u6570\u636e",class:"custom-empty"})]))]))]),h[21]||(h[21]=(0,f._)("div",{class:"user-button-text"},[(0,f._)("span",null,(0,m.zw)("\u6280\u672f\u652f\u6301 \xa9 \u5e7f\u4e1c\u5de6\u5411\u79d1\u6280\u6709\u9650\u516c\u53f8"))],-1)),(0,f._)("div",Ye,[(0,f._)("div",Me,[(0,f._)("div",Ae,[(0,f._)("div",{class:"cart-section",onClick:S},[(0,f.Wm)(pe,{value:$e.value,class:"cart-badge"},{default:(0,f.w5)((function(){return h[17]||(h[17]=[(0,f._)("div",{class:"cart-icon-wrapper"},[(0,f._)("i",{class:"i-bx-cart-alt"}),(0,f._)("span",{class:"cart-text"},"\u8d2d\u7269\u8f66")],-1)])})),_:1},8,["value"])])]),(0,f._)("div",Re,[h[18]||(h[18]=(0,f._)("span",{class:"total-label"},"\u5408\u8ba1:",-1)),(0,f._)("span",Xe,"\xa5"+(0,m.zw)(H.value.toFixed(2)),1)]),(0,f._)("div",Ke,[(0,f.Wm)(ve,{class:"add-cart-btn",onClick:Ge,disabled:0===D.value.filter((function(e){return e.checkbox})).length},{default:(0,f.w5)((function(){return h[19]||(h[19]=[(0,f._)("i",{class:"i-bx-cart-add"},null,-1),(0,f._)("span",null,"\u52a0\u5165\u8d2d\u7269\u8f66",-1)])})),_:1},8,["disabled"]),(0,f.Wm)(ve,{class:"buy-now-btn",type:"primary",onClick:O,disabled:0===D.value.length||0===D.value.filter((function(e){return e.checkbox})).length},{default:(0,f.w5)((function(){return h[20]||(h[20]=[(0,f.Uk)(" \u7acb\u5373\u8d2d\u4e70 ")])})),_:1},8,["disabled"])])])])]})),_:1})}}});const $e=Je;var Ge=$e,Qe={navigationBarTitleText:"\u4ea7\u54c1\u8be6\u60c5",enablePullDownRefresh:!0,onReachBottomDistance:250};Page((0,o.createPageConfig)(Ge,"package/package-a/Details/index",{root:{cn:[]}},Qe||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(9648)}));e.O()}]);