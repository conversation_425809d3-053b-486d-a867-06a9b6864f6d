<template>
    <nut-card
      :img-url="state.imgUrl"
      :title="state.title"
      :price="state.price"
      :shop-desc="state.shopDesc"
      class="m-3% w-92%! flex flex-row flex-center"
      style="box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;"
    >
      <template #shop-tag>
      </template>
      <template #price>
        <!-- <div></div> -->
        <template></template>
      </template>
      <template #origin>
        <div></div>
      </template>
    </nut-card>
  </template>
  <script setup lang="ts">
  import {defineProps, onMounted, watchEffect} from 'vue';
  const props=defineProps({
    item:{
        type:Object,
        required:true
    }
  })
//   const emit=defineEmits(['click-card'])
//   const clickCard=()=>{
//     emit('click-card',)
//   }
  onMounted(()=>{

  })
 
  import { ref } from 'vue'
  const state = ref({
    imgUrl:'',
    title: '',
    shopDesc: '自营',
  })
  watchEffect(()=>{
    state.value.title=props.item.model,
    state.value.imgUrl=props.item.image
    state.value.shopDesc=props.item.name.split('/')[0]
  })
  </script>
  <style>
  .card-tag {
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    margin-left: 2px;
    height: 14px;
  }
  .nut-card__right{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 27px;
  }
  .nut-card .nut-card__right .nut-card__right__other .nut-tag{
    font-size: 12px;
    max-width: 100px;
  }
  .nut-card__right__title{
    font-weight: bold;
  }
  .nut-card__right__title{
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    margin-right: 5%;
  }
  .nut-card .nut-card__right .nut-card__right__price{
    height: 0 !important;
  }
  </style>
  