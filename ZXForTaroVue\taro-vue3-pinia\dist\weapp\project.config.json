{"miniprogramRoot": "", "projectname": "Taro3", "description": "Taro3", "appid": "wx3c57cd258296a7fc", "setting": {"urlCheck": false, "es6": false, "postcss": false, "minified": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "compileType": "miniprogram", "libVersion": "3.8.7", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}