<script setup lang="ts">
import { eventCenter, navigateTo, getCurrentInstance } from '@tarojs/taro';
import { useAppStore, useThemeStore } from '@/store';
import { reactive, toRefs, onMounted, Ref, ref, onUnmounted } from 'vue'
import Taro from '@tarojs/taro';
import { getCategory, getProduct } from '@/service/api/index';
import Card from './components/card.vue';
import _ from 'lodash'; // 引入lodash库


const data = reactive({
	categoryInfo: {
		"category": [
			{
				"catName": "自电集控",
				"children": [
					{
						"catName": "筒灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/41/AC/rBwBHmTXNfuADYKfAADZ4kbWiyc008.png?w=600",

						},
					},
					{
						"catName": "双头灯",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/2A/76/wKj0iWCTVU-AYR1jAAO8-bzf0OE737.png?w=600",

						},


					},
					{
						"catName": "吸顶灯",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/2A/74/wKj0iWCTUWqASgeZAAITI6rd2zY352.png",

						},


					},
					{
						"catName": "标志灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/E7/E9/wKj0iWGtq42AGSw1AACclsDES2A878.png?w=600",

						},
					},
				]
			},
			{
				"catName": "集电集控",
				"children": [
					{
						"catName": "筒灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/32/39/wKj0iWCvDDuAeYBmAAGEfHN3_Ow040.jpg?w=600",

						},
					},
					{
						"catName": "标志灯",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/2A/73/wKj0iWCTUHaAMSN0AAOAU8D6Mek626.png?w=600",

						},
					},
					{
						"catName": "壁灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www2/M00/9F/7C/rBwBEmTWCFeADdEtAADs4WJrv2U815.png?w=600",

						},
					},
					{
						"catName": "吸顶灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/F7/68/wKj0iWHAMiGAc68YAABYvBEVQU4089.png?w=600",

						},
					},
				]
			},
			{
				"catName": "常规",
				"children": [
					{
						"catName": "应急筒灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/2A/27/wKj0iWCLv5iAH91iAAG2zEAE0pM778.png?w=600",

						},


					},
					{
						"catName": "防爆标志灯",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/2A/24/wKj0iWCLufmAK9kPAATAz0Qit1c844.png?w=600",

						},


					},
					{
						"catName": "应急吸顶灯",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/2A/74/wKj0iWCTUi2AOdOVAAL067XhHw4749.png",

						},


					},
				]
			},
			{
				"catName": "电源",
				"children": [
					{
						"catName": "应急电源",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/2A/31/wKj0iWCL1SuAdl9RAAV870RGeiA704.png?w=600",

						},


					},
					{
						"catName": "集中电源",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/41/62/rBwBHmTWFwqAPWFlAAG1temu5fc199.png?w=600",
						},
					},
					{
						"catName": "锂电电源",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www2/M00/9D/8C/rBwBEmTTDjKAbtxCAAG1fS9Q-A8827.png?w=600",
						},
					},
				]
			},
			{
				"catName": "火报",
				"children": [
					{
						"catName": "探测器",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/2A/31/wKj0iWCL1SuAdl9RAAV870RGeiA704.png?w=600",

						},
					},
					{
						"catName": "控制器",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/37/64/rBwBHmTHI6OACy4KAABmX2YrUOk378.png?w=600",

						},
					},
					{
						"catName": "警报器",
						"childCateList":
						{
							"backImg": "https://img03.71360.com/file/read/www/M00/37/C6/rBwBHmTHftyAVfbCAAGBVz9mhbk875.png?w=600",

						},
					},

					{
						"catName": "模块",
						"childCateList":
						{
							"backImg": "https://img01.71360.com/file/read/www/M00/37/D0/rBwBHmTHiU2ADSxEAACWLVX6OAk889.png?w=600",

						},
					},
				]
			}
		]
	},
	category: [
		{
			"catName": "自电集控",
			"children": [
				{
					"catName": "筒灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/41/AC/rBwBHmTXNfuADYKfAADZ4kbWiyc008.png?w=600",

					},
				},
				{
					"catName": "双头灯",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/2A/76/wKj0iWCTVU-AYR1jAAO8-bzf0OE737.png?w=600",

					},
				},
				{
					"catName": "吸顶灯",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/2A/74/wKj0iWCTUWqASgeZAAITI6rd2zY352.png",

					},
				},
			]
		},
		{
			"catName": "集电集控",
			"children": [
				{
					"catName": "筒灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/32/39/wKj0iWCvDDuAeYBmAAGEfHN3_Ow040.jpg?w=600",

					},


				},
				{
					"catName": "标志灯",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/2A/73/wKj0iWCTUHaAMSN0AAOAU8D6Mek626.png?w=600",

					},
				},
				{
					"catName": "壁灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www2/M00/9F/7C/rBwBEmTWCFeADdEtAADs4WJrv2U815.png?w=600",

					},
				},
				{
					"catName": "吸顶灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/F7/68/wKj0iWHAMiGAc68YAABYvBEVQU4089.png?w=600",

					},
				},
			]
		},
		{
			"catName": "常规",
			"children": [
				{
					"catName": "应急筒灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/2A/27/wKj0iWCLv5iAH91iAAG2zEAE0pM778.png?w=600",

					},


				},
				{
					"catName": "标志灯",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/2A/24/wKj0iWCLufmAK9kPAATAz0Qit1c844.png?w=600",

					},
				},
				{
					"catName": "应急吸顶灯",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/2A/74/wKj0iWCTUi2AOdOVAAL067XhHw4749.png",

					},
				},
			]
		},
		{
			"catName": "电源",
			"children": [
				{
					"catName": "应急电源",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/2A/31/wKj0iWCL1SuAdl9RAAV870RGeiA704.png?w=600",

					},


				},
				{
					"catName": "集中电源",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/41/62/rBwBHmTWFwqAPWFlAAG1temu5fc199.png?w=600",
					},
				},
				{
					"catName": "锂电电源",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www2/M00/9D/8C/rBwBEmTTDjKAbtxCAAG1fS9Q-A8827.png?w=600",
					},
				},
			]
		},
		{
			"catName": "火报",
			"children": [
				{
					"catName": "探测器",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/2A/31/wKj0iWCL1SuAdl9RAAV870RGeiA704.png?w=600",

					},
				},
				{
					"catName": "控制器",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/37/64/rBwBHmTHI6OACy4KAABmX2YrUOk378.png?w=600",

					},
				},
				{
					"catName": "警报器",
					"childCateList":
					{
						"backImg": "https://img03.71360.com/file/read/www/M00/37/C6/rBwBHmTHftyAVfbCAAGBVz9mhbk875.png?w=600",

					},
				},
				{
					"catName": "模块",
					"childCateList":
					{
						"backImg": "https://img01.71360.com/file/read/www/M00/37/D0/rBwBHmTHiU2ADSxEAACWLVX6OAk889.png?w=600",

					},
				},
			]
		}
	],

	categoryChild: [
		{
			"catName": "筒灯",
			"childCateList":
			{
				"backImg": "https://img01.71360.com/file/read/www/M00/41/AC/rBwBHmTXNfuADYKfAADZ4kbWiyc008.png?w=600",

			},
		},
		{
			"catName": "双头灯",
			"childCateList":
			{
				"backImg": "https://img03.71360.com/file/read/www/M00/2A/76/wKj0iWCTVU-AYR1jAAO8-bzf0OE737.png?w=600",

			},


		},
		{
			"catName": "吸顶灯",
			"childCateList":
			{
				"backImg": "https://img03.71360.com/file/read/www/M00/2A/74/wKj0iWCTUWqASgeZAAITI6rd2zY352.png",

			},


		},
		{
			"catName": "标志灯",
			"childCateList":
			{
				"backImg": "https://img01.71360.com/file/read/www/M00/E7/E9/wKj0iWGtq42AGSw1AACclsDES2A878.png?w=600",

			},
		},
	],
})




onMounted(() => {
	eventCenter.on(getCurrentInstance().router!.onShow, async () => {
		console.log('preloadData----', Taro.getStorageSync('tab'));
		if (Taro.getStorageSync('tab')) {
			await getCategoryF(changeItemMap.get(Taro.getStorageSync('tab'))![active.value])
			Taro.removeStorageSync('tab')
		} else {
			await getCategoryF(changeItemMap.get(MainClassItem.value)![active.value])

		}




	})
})
onUnmounted(() => {
	eventCenter.off(getCurrentInstance().router!.onShow)
})
const changeMAp = new Map([
	[0, '自电集控'],
	[1, '集电集控'],
	[2, '常规'],
	[3, '电源'],
	[4, '火报']
])
const changeItemMap = new Map([
	['自电集控', ['筒灯', '双头灯', '吸顶灯', '标志灯']],
	['集电集控', ['筒灯', '标志灯', '壁灯', '吸顶灯']],
	['常规', ['应急筒灯', '标志灯', '应急吸顶灯']],
	['电源', ['应急电源', '集中电源', '锂电电源']],
	['火报', ['探测器', '控制器', '警报器', '模块']]
])
const MainClassItem = ref('自电集控')
const ClassItem = ref('双头灯')
const change = async (index: string | number) => {
	console.log('change', changeMAp.get(index as number));
	active.value = 0
	MainClassItem.value = changeMAp.get(index as number) as string
	data.categoryChild = [].concat(data.categoryInfo.category[index].children)
	console.log(changeItemMap.get(MainClassItem.value)![0]);

	await getCategoryF(changeItemMap.get(MainClassItem.value)![0])
}





const appStore = useAppStore();


/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '分类'
});

const getData = async () => {



};


//跳转登录页函数
const toLogin = () => {
	Taro.reLaunch({
		url: '/pages/login/index'
	})
}
const searchValue = ref('')

Taro.showShareMenu({
	withShareTicket: true,
	showShareItems: ['shareAppMessage', 'shareTimeline']
})

const active = ref(0)
//产品列表

const state = ref({
	list: [

	]
})
//获取产品列表事件
const getCategoryF = async (msg?: string) => {

	//发起网络请求
	const { error, success }: { error: any, success: any } = await getCategory(['电源', '火报'].includes(MainClassItem.value) ? { name: `${msg ?? ClassItem.value}` } : { name: `%${MainClassItem.value}%${msg ?? ClassItem.value}%` })
	console.log("试试", error);
	if (error == null) {
		state.value.list = success.items
	} else {
		Taro.showToast({
			title: '加载失败',
			icon: 'error',
			duration: 2000
		})
	}

}
const clickClassItem = async (item: any, index: number) => {
	console.log(item.catName);
	ClassItem.value = item.catName
	active.value = index

	await getCategoryF()
}

//下拉触底事件
const onReachBottom = _.throttle(function () {
	console.log('下拉触底事件');

}, 300)

//产品事件
const getProductF = async (item: any) => {
	console.log(item);

	//发起网络请求
	const { error, success }: { error: any, success: any } = await getProduct({ model: item.model })
	console.log(error, success);
	if (error == null) {
		//跳转产品详情页
		Taro.preload({ model: item.model })
		Taro.navigateTo({
			url: '/package/package-a/Details/index?model=' + item.model,
			fail: (res: any) => {
				console.log(res)
			}


		})
		console.log('当前分类数据')
	} else {
		Taro.showToast({
			title: '暂无数据',
			icon: 'error',
			duration: 2000
		})
	}


}
</script>

<template>
	<basic-layout show-tab-bar style="background-color: #f7f7f7;">




		<div class="m-2">
			<nut-cell style="box-shadow: 0 0 black;">
				<nut-category :category="data.category" @change="change" style="width: 100%;">

					<nut-backtop height="75vh" style="width: 100%;" :distance="30" :bottom="150">
						<template #content>

							<scroll-view scroll-x="true"
								style='display: flex;white-space:nowrap;padding: 0 3% !important;' class="scroll-Y">
								<template v-for="(item, index) in data.categoryChild" style="height:80px;weight:80px"
									:key="item.catName">
									<div style="display: inline-block;border-radius: 3px;" class="myClickItem"
										@click="[clickClassItem(item, index)]" :class="{ hideItem: active == index }">
										<div class="flex flex-col flex-center ml-5%! mr-5%!">
											<image :src="item.childCateList.backImg" class="w-75px h-75px"></image>
											<span class="w-100%! text-center font-600">{{ item.catName }}</span>
										</div>
									</div>
								</template>
							</scroll-view>
							<scroll-view :scroll-y="true" style="height: 440px" class="p-3%!"
								@ScrollToLower.stop="onReachBottom">

								<template v-for="(item, index) in state.list" :key="index">
									<Card :item="item" @click="getProductF(item)" />

								</template>
							</scroll-view>

						</template>
					</nut-backtop>
				</nut-category>
			</nut-cell>
		</div>


		<!-- 底部信息栏 -->
		<div class="user-button-text">
			<span>{{ '技术支持 © 广东左向科技有限公司' }}</span>
		</div>
	</basic-layout>
</template>
<style lang="scss">
.hideItem {
	filter: brightness(0.9);
	color: #E53935;
}

.hideItem::before {
	content: " ";
	position: absolute;
	top: 0;
	left: 0;
	width: 75px;
	height: 75px;
	/*最后一个参数是半透明度，可以透过调整0-1的数值，调整到满意的透明度*/
	background-color: rgba(0, 0, 0, 0.2);
}


.nut-category__cateListItem {
	font-weight: 900;
}

.user-button-text {
	font-size: 12px;
	color: #A6A6A6;
	text-align: center;
	margin: 2rem auto;
}

.toLogin {
	position: fixed;
	bottom: 13%;
	background-color: rgba($color: #3a3a3a, $alpha: .7);
	border-radius: 5px;
	left: 50%;
	transform: translate(-50%, 0);
	padding: 2% 4%;
	line-height: 100%;
	color: #D0D0D0;
	font-size: 0.75rem;
	line-height: 1.875rem;
	width: 85%;
	display: flex;
	justify-content: space-between;

	.toLogin-button {
		color: #EAEEF9;
	}
}

.cpxg {
	box-shadow: none;
	background: transparent;
	padding-left: 0;
	font-weight: 600;
	font-size: 1.125rem;

	.cpxg-link {
		color: #070707;
		font-size: 12px;
		font-weight: 100;
	}
}

::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}

.nut-fixed-nav__btn {
	width: 60px !important;
}

.myClickItem:last-child {
	padding-right: 80px;
}
</style>
