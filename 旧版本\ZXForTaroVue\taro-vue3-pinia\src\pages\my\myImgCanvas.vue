<template>
    <div>
        {{ '测试' }}
      <!-- Canvas用于绘制文本 -->
      <canvas ref="textCanvas" width="500" height="200"></canvas>
      <!-- 显示生成的图片 -->
      <img :src="imageDataUrl" alt="Generated Image" v-if="imageDataUrl" />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue';
  
  // 定义props
  const props = defineProps({
    text: {
      type: String,
      required: true
    }
  });
  
  // 创建响应式变量保存图像数据URL
  const imageDataUrl = ref(null);
  
  // 引用canvas元素
  const textCanvas = ref(textCanvas);
  
  // 方法：绘制文字并转换为图片
  const drawTextAndConvert = () => {
    const canvas = textCanvas.value;
    if (!canvas) return;
  
    const ctx = canvas.getContext('2d');
  
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  
    // 设置字体、颜色等样式属性
    ctx.font = '40px Arial';
    ctx.fillStyle = '#333';  // 文字颜色
  
    // 绘制文字到canvas上
    ctx.fillText(props.text, 50, 100);  // (x, y)坐标指定文字位置
  
    // 将canvas内容转换为image/png格式的数据URL
    imageDataUrl.value = canvas.toDataURL('image/png');
  };
  
  // 组件挂载后调用方法
  onMounted(drawTextAndConvert);
  
  // 监听text prop的变化，重新绘制和转换
  watch(() => props.text, drawTextAndConvert);
  </script>
  
  <style scoped>
  /* 添加一些样式 */
  canvas, img {
    display: block;
    margin: 0 auto; /* 居中显示 */
  }
  </style>