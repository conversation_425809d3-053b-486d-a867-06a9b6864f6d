/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Address: typeof import('./../components/address/index.vue')['default']
    BasicLayout: typeof import('./../components/basic-layout/index.vue')['default']
    Card: typeof import('./../components/Card/index.vue')['default']
    CustomNavbar: typeof import('./../components/custom-navbar/index.vue')['default']
    DesignCell: typeof import('./../components/design-cell/index.vue')['default']
    NutActionSheet: typeof import('@nutui/nutui-taro')['ActionSheet']
    NutAddressList: typeof import('@nutui/nutui-taro')['AddressList']
    NutAvatar: typeof import('@nutui/nutui-taro')['Avatar']
    NutBacktop: typeof import('@nutui/nutui-taro')['Backtop']
    NutBadge: typeof import('@nutui/nutui-taro')['Badge']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCategory: typeof import('@nutui/nutui-taro')['Category']
    NutCell: typeof import('@nutui/nutui-taro')['Cell']
    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']
    NutConfigProvider: typeof import('@nutui/nutui-taro')['ConfigProvider']
    NutDialog: typeof import('@nutui/nutui-taro')['Dialog']
    NutDivider: typeof import('@nutui/nutui-taro')['Divider']
    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
    NutFixedNav: typeof import('@nutui/nutui-taro')['FixedNav']
    NutForm: typeof import('@nutui/nutui-taro')['Form']
    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
    NutGrid: typeof import('@nutui/nutui-taro')['Grid']
    NutGridItem: typeof import('@nutui/nutui-taro')['GridItem']
    NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutInputNumber: typeof import('@nutui/nutui-taro')['InputNumber']
    NutList: typeof import('@nutui/nutui-taro')['List']
    NutNavbar: typeof import('@nutui/nutui-taro')['Navbar']
    NutNoticebar: typeof import('@nutui/nutui-taro')['Noticebar']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRadio: typeof import('@nutui/nutui-taro')['Radio']
    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
    NutSticky: typeof import('@nutui/nutui-taro')['Sticky']
    NutSwipe: typeof import('@nutui/nutui-taro')['Swipe']
    NutSwipeGroup: typeof import('@nutui/nutui-taro')['SwipeGroup']
    NutSwiper: typeof import('@nutui/nutui-taro')['Swiper']
    NutSwiperItem: typeof import('@nutui/nutui-taro')['SwiperItem']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTabbar: typeof import('@nutui/nutui-taro')['Tabbar']
    NutTabbarItem: typeof import('@nutui/nutui-taro')['TabbarItem']
    NutTabPane: typeof import('@nutui/nutui-taro')['TabPane']
    NutTabs: typeof import('@nutui/nutui-taro')['Tabs']
    NutTag: typeof import('@nutui/nutui-taro')['Tag']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
