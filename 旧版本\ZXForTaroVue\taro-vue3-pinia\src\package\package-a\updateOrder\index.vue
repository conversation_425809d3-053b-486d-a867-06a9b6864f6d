<template>

    <div>

        <nut-action-sheet v-model:visible="show" class="myAction">
            <div style="height: 340px;">
                <nut-searchbar v-model="searchValue" shape="square" label="请输入楼层以搜索" input-background="#F0F0F0"
                    @change="GetInputFocus" id="pop-target">
                    <template #leftin>
                        <Search2 />
                    </template>
                </nut-searchbar>

                <div :class="{ toShowDiv: !showSelect }"
                    style="width: 80%;height: 300px;background-color: #FFFFFF;background-color: #FFFFFF;position: absolute;left: 50%;transform:translateX(-50%); z-index:999;">
                    <div>
                        <div v-if="searchList.length > 0">
                            <nut-list :list-data="searchList" :container-height="300" @scroll-bottom="onScrollBottom">
                                <template #default="{ item }">
                                    <div class="list-item" @click="clickItem(item)">
                                        {{ item.name }}
                                    </div>

                                    <nut-divider :dashed="true" />
                                </template>
                            </nut-list>
                        </div>
                        <div v-else style="margin-top: 5%;color: #333333;font-weight: 900;">
                            {{ '请输入相应正确楼层编号后进行查询' }}
                        </div>
                    </div>

                </div>
            </div>
        </nut-action-sheet>

        <nut-cell>
            <div class="harvest-box">
                <nut-address-list :data="harvestData" :show-bottom-button="false" :data-options="harvestOptions">
                    <template #item-icon>
                        <i class="i-bx-edit-alt font-size-20px p-3px" @click="editClick"
                            :class="{ jinyon: !(stateMap.get(params!.state as string) ?? true) }"></i>
                    </template>
                </nut-address-list>
            </div>
        </nut-cell>
        <!-- 备注及特殊发货要求 -->

        <nut-cell class="flex flex-col">
            <!-- <div class="flex  h40px justify-between">
                <div class="line-height-40px font-bold">{{ '备注：' }}</div>
                <nut-input v-model="note" :max-length="50" :show-word-limit="true" class="w60%!" :disabled="!(stateMap.get(params!.state as string)??true)">
                    <template #right>
                        <i class="i-bx-edit-alt"></i>
                    </template>
                </nut-input>
            </div>
            <div class="flex  h40px justify-between">
                <div class="line-height-40px font-bold">{{ '特殊发货要求：' }}</div>
                <nut-input v-model="special_note" :max-length="50" :show-word-limit="true" class="w60%!" :disabled="!(stateMap.get(params!.state as string)??true)">
                    <template #right>
                        <i class="i-bx-edit-alt"></i>
                    </template>
                </nut-input>
            </div> -->


            <div class="flex  flex-col">
                <div class="line-height-40px font-bold">{{ '备注：' }}</div>
                <nut-textarea v-model="note" limit-show :max-length="50" class="w-80%! h-80px!"
                    :placeholder="((stateMap.get(params!.state as string) ?? true)) ? '请输入' : '未填写'"
                    :disabled="!(stateMap.get(params!.state as string) ?? true)" />
            </div>
            <!-- <div class="flex  flex-col">
                <div class="line-height-40px font-bold ">{{ '特殊发货要求：' }}</div>
                <nut-textarea v-model="special_note" limit-show :max-length="50"
                    :placeholder="((stateMap.get(params!.state as string) ?? true)) ? '请输入' : '未填写'" autosize
                    class="w60%!" :disabled="!(stateMap.get(params!.state as string) ?? true)" />
            </div> -->
        </nut-cell>
        <nut-popup v-model:visible="popupShow" position="bottom" round :style="{ height: '65vh' }"
            class="flex flex-col justify-evenly">
            <Address @click-item="(val) => { console.log(val); popupShow = val.boolean; harvestData[0] = val.item }" :showDefault="true" @defauult-emit="(val)=>{popupShow=false;harvestData[0].addressName = '采用U9默认联系地址';harvestData[0].fullAddress=' ';harvestData[0].phone=' ';harvestData[0].defaultAddress=false
            }">
            </Address>
            <!-- <nut-form ref="formRef">
                <nut-form-item label="联系人" prop="testaddressName" :rules="[
                    {
                        validator: validatorName,
                        message: '请输入正确的人名'
                    }
                ]">
                    <nut-input v-model="formData.testaddressName" placeholder="请输入联系人" type="text" />
                </nut-form-item>
                <nut-form-item label="联系电话" prop="phone" :rules="[
                    {
                        validator: validatorPhone,
                        message: '请输入正确的手机号'

                    }
                ]">
                    <nut-input v-model="formData.phone" placeholder="请输入联系电话" type="text" />
                </nut-form-item>
                <nut-form-item label="联系地址" prop="fullAddress" :rules="[
                    {
                        validator: validatorAddress,
                        message: '地址应尽量详细'

                    }

                ]">
                    <nut-input v-model="formData.fullAddress" placeholder="请输入地址" type="text" />
                </nut-form-item>
                <nut-button type="primary" size="large" class="verifi-button w-60% " block @click="submitAddress">{{
                    '保存' }}</nut-button>
            </nut-form> -->
            <nut-input>

            </nut-input>
        </nut-popup>
        <nut-dialog content="确定移除商品吗？" v-model:visible="isRmCommodityDialogShow" @cancel="reCCard"
            @ok="removeCommodity" />
        <nut-cell class="flex flex-center">
            <card class="card">

                <nut-swipe-group lock style="width: 100vw" v-if="cartCommodities.length > 0">
                    <div style="display: flex;flex-direction: row;width: 100%;height: 30px;padding: 3% 1%;">
                        <div style="height: 100%;width: 6px;margin-left: 1%;background-color: #1B9FFF;"></div>
                        <div
                            style="margin-left: 5%;font-weight: bold;font-size: 36rpx;color: #122F38;line-height: 30px;">
                            {{
                                '订单详情'
                            }}</div>
                    </div>
                    <nut-swipe v-for="(item, index) in cartCommodities" ref="swipeRefs" class="lastChild"
                        style="margin-bottom: 10px" :name="item.line_id.toString()" :key="item.line_id">
                        <view style="display: flex; gap: 5px; width: 100vw" @longpress="getHarvestData(item.line_id)">
                            <div class="text-center">
                                <image
                                    :src="item.product_image ?? 'https://tse2-mm.cn.bing.net/th/id/OIP-C.exoIucrWcex80_QKk3z5DAAAAA?w=181&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7'"
                                    style="width: 100px; height: 100px;padding: 5px;display: inline-table;"></image>
                                <!-- <nut-tag plain type="primary">{{ item.name.split('/')[0] }}</nut-tag> -->
                            </div>

                            <view style="display: flex; flex-direction: column; gap: 8px;padding: 5%">
                                <view
                                    style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;text-align: left;">

                                    <!-- <nut-tag color="#F7AA71" class="ml-3% mr-3%">{{ item.model }}</nut-tag> -->
                                    <div
                                        style="color:#DD5F73;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #DD5F73;margin-bottom: 1%;margin-right: 3%;">
                                        <i class="i-bx-crown"
                                            style="vertical-align: text-bottom;color: #DD5F73;font-weight: bold;"></i>
                                        {{ item.model }}
                                    </div>
                                    <div
                                        style="color:#4D74FA;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #4D74FA;margin-bottom: 1%;">

                                        {{ item.name.split('/')[0] }}
                                    </div>
                                    <nut-tag color="#F7AA71" v-show="item.name.split('/')[1]">{{
                                        item.name.split('/')[1]
                                        }}</nut-tag>

                                </view>
                                <nut-tag color="#F2F2F2" text-color="#909090"
                                    class="text-ellipsis w-11.25rem break-all">{{
                                        item.spec
                                    }}</nut-tag>
                                <view class="flex justify-between">
                                    <span>
                                        <nut-price :price="item.price_unit"
                                            style="color: #F36409;font-weight: 900;"></nut-price>
                                        <span class="unit color-#ED7976 pl-5px  font-900">/</span>
                                        <span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
                                    </span>
                                    <nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }} </nut-tag>

                                </view>
                                <DesignCell :design="item.seiban" :id="item.line_id" :index="index"
                                    :name="item.name.split('/')[0]"
                                    :class="{ jinyon: !(stateMap.get(params!.state as string) ?? true), noShow: !item.name.split('/')[0].includes('特殊图案') }"
                                    @update:design="showDialog">
                                </DesignCell>
                            </view>
                        </view>
                        <div class="flex justify-left h40px">
                            <div class="line-height-40px pl-5px!">{{ '备注：' }}</div>
                            <div class="w60%!">
                                <nut-input v-model="item.zx_line_notes" :max-length="50" placeholder="去留言(下单后无法追加)"  
                                :disabled="!(stateMap.get(params!.state as string) ?? true)" class="w45%! pl-3px!">
                                <template #right>
                                    <i class="i-bx-edit-alt"></i>
                                </template>
                            </nut-input>
                            </div>
                        </div>


                        <view class="quantity-button-group">
                            <!-- <IconFont name="minus" class="button-cell button-minus" @click="() => minus(item.line_id)">
                            </IconFont>
                            <view class="button-cell button-cell-middle ">{{ item.product_qty }}</view>
                            <IconFont class="button-cell button-plus" name="plus" @click="() => plus(item.line_id)">
                            </IconFont> -->
                            <nut-input-number v-model="item.product_qty" :min="1" :max="200000" input-width="55"
                                @blur="countTotalPrice()" @add="() => plus(item.line_id)"
                                @reduce="() => minus(item.line_id)" />
                        </view>
                        <template #right>
                            <nut-button shape="square" style="height: 100%" type="danger"
                                @click="() => { showRmDialog(item.line_id) }">删除</nut-button>
                        </template>
                    </nut-swipe>
                </nut-swipe-group>

                <nut-empty v-else description="空空如ye~~" />
            </card>
        </nut-cell>

        <!-- 新增商品列表 -->
        <!-- <template v-if="checkList.length > 0">

            <div class="w-100% border-1% mt-3% mb-3%" style="background-color: #FFF;">
                <div style="display: flex;flex-direction: row;width: 100%;height: 30px;padding: 3% 1%;">
                    <div style="height: 100%;width: 6px;margin-left: 1%;background-color: #1B9FFF;"></div>
                    <div style="margin-left: 5%;font-weight: bold;font-size: 36rpx;color: #122F38;line-height: 30px;">{{
                        '新增'
                    }}</div>
                </div>
                <div v-for="(i, index) in checkList" class="mt-3% mb-3%">
                    <view class="text-center font-size-14px font-bold color-#383838">用户第{{ index + 1 }}次新增</view>
                    <nut-cell class="flex flex-center">
                        <card class="card">
                            <nut-swipe-group lock style="width: 100vw">
                                <nut-swipe v-for="(item, index) in i" ref="swipeRefs" class="lastChild"
                                    style="margin-bottom: 10px" :name="item.id.toString()" :key="item.id" disabled>
                                    <view style="display: flex; gap: 5px; width: 100vw">
                                        <image
                                            :src="item.product_image || 'https://tse2-mm.cn.bing.net/th/id/OIP-C.exoIucrWcex80_QKk3z5DAAAAA?w=181&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7'"
                                            style="width: 100px; height: 100px;padding: 5px;"></image>

                                        <view style="display: flex; flex-direction: column; gap: 8px;padding: 5%">
                                            <view
                                                style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;">
                                                <nut-tag color="#F7AA71">{{ item.name.split('/')[0] }}</nut-tag>
                                                <nut-tag color="#F7AA71" class="ml-3% mr-3%">{{ item.model }}</nut-tag>
                                                <nut-tag color="#F7AA71" v-show="item.name.split('/')[1]"
                                                    class="mt-2%!">{{
                                                        item.name.split('/')[1] }}</nut-tag>
                                            </view>
                                            <nut-tag color="#F2F2F2" text-color="#909090" class="text-ellipsis">{{
                                                item.spec
                                            }}</nut-tag>
                                            <view class="flex justify-between">
                                                <span>
                                                    <nut-price :price="item.price_unit"
                                                        style="color: #F36409;font-weight: 900;"></nut-price>
                                                    <span class="unit color-#ED7976 pl-5px  font-900">/</span>
                                                    <span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
                                                </span>
                                                <nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }}
                                                </nut-tag>
                                            </view>
                                            
                                            <DesignCell :design="item.design" :id="item.fanhao_id" :index="index">
                                            </DesignCell>
                                        </view>
                                    </view>
                                    <div class="flex justify-center h40px">
                                        <div class="line-height-40px">{{ '备注：' }}</div>
                                        <nut-input v-model="item.zx_line_notes" :max-length="50" class="w45%! pl-3px!"
                                            disabled>
                                            <template #right>
                                                <i class="i-bx-edit-alt"></i>
                                            </template>
                                        </nut-input>
                                    </div>

                                    <view class="quantity-button-group">
                                        <IconFont name="minus" class="button-cell button-minus">
                                        </IconFont>
                                        <view class="button-cell button-cell-middle ">{{ item.product_qty }}</view>

                                        <IconFont class="button-cell button-plus" name="plus">
                                        </IconFont>
                                    </view>

                                </nut-swipe>
                            </nut-swipe-group>

                        </card>
                    </nut-cell>
                </div>
            </div>
        </template> -->

        <!-- <nut-button plain class="SJ_FX ml-auto mr-auto"
            style="display: flex;border-radius: 5%;height: 20vh;border: 1px dashed #E0E0E6;box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;"
            @click="addOrder">
            <template #icon>
                <i class="i-bx-cloud-upload" style="font-size: 3.25rem;"></i>
            </template>
            <div>
                <div style="font-weight: 600;font-size: 18px;">{{ '产品新增' }}</div>
                <div style="color: #767C82;"><span style="line-height: 100%;padding-left: 3px;">{{ '点击前往选购其他产品'
                        }}</span></div>
            </div>
        </nut-button> -->
        <nut-fixed-nav type="right" :position="{ top: '240px' }" @click="addOrder"
            v-show="stateMap.get(params!.state as string) ?? true">
            <template #btn>
                <span class="text">{{ "新增产品" }}</span>
            </template>
        </nut-fixed-nav>

        <div class="h-75px w-100%!"></div>



        <view class="bottom-card" v-show="stateMap.get(params!.state as string) ?? true">
            <view style="display: flex; gap: 5px">
                <view>合计:
                    <nut-price :price="totalPrice"></nut-price>
                </view>
            </view>
            <view style="margin-right: 10px">
                <nut-button type="primary" @click="submitOrderF">{{ '保存修改' }}</nut-button>
            </view>
        </view>
    </div>
</template>

<script setup lang="ts">
import { getDefaultAddress, getOrderMessage, getProduct, getProductByNumber, submitOrder, updateOrder } from '@/service/index'
import Taro from '@tarojs/taro';
import { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { onMounted, onUnmounted, reactive, ref, Ref, watch } from 'vue';
import { IconFont } from "@nutui/icons-vue-taro";
import { useAppStore } from '@/store';
const appStore = useAppStore();
import { markRaw, toRaw } from "@vue/reactivity";
import Address from '@/components/address/index.vue'
import AddressParse from '@/package/package-a/harvestAddress/dist/zh-address-parse.min.js'
const cartCommodities: Ref<Array<{
    line_id: number,
    code: string,
    name: string,
    spec: string,
    model: string,
    price_qty: number,
    product_image: string,
    product_qty: number,
    checkbox: boolean,
    price_unit: number,
    seiban: string | undefined,
    zx_line_notes: string | undefined,
    fanhao_id: string | undefined,
    price_total: number | string,
    note: string,
    product_id: string

}>> = ref([
])
interface IharvestData {
    addressName: string,
    phone: string,
    fullAddress: string
}
//长按事件
const getHarvestData = (item) => {
    // console.log('aaa');
    showRmDialog(item)


}
//楼层字段变更
const show = ref(false)
const click = () => {
    show.value = true
}
const searchValue = ref('')
const showSelect = ref(false)
const searchList: Ref<any[]> = ref([])
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)

//删除订单事件
let tempId = -1
const isRmCommodityDialogShow = ref(false)
const showRmDialog = (id: number) => {
    console.log("id", id);

    tempId = id
    isRmCommodityDialogShow.value = true
}
let removeItemArray: Ref<Array<object>> = ref([])
const removeCommodity = () => {

    cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.line_id === tempId), 1)
    removeItemArray.value.push({ "line_id": tempId })
}


const GetInputFocus = () => {
    if (searchValue.value.length >= 0) {
        show.value = true
        if (searchValue.value.length > 0) {
            getProductByNumberF(searchValue)
        } else {
            searchList.value = []
            DQsearchListIndex.value = 1
        }

    } else {
        show.value = false
    }

}
const getProductByNumberF = async (item: Ref<string>) => {
    const { error, success } = await getProductByNumber({ name: item.value, per_page: '20' })
    console.log('success                  ~~~', success);
    const { items, psize } = success as { items: Array<any>, psize: number }
    if (success && items) {
        searchList.value = items
        DQsearchListIndex.value = psize
    }
}
//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
    console.log('子组件返回值:id', msg.id);
    console.log('子组件返回值:index', msg.index);
    ClickshowDialogItem = msg.id
    click()
}

const clickItem = (item: any) => {
    const { name, id } = item
    if (ClickshowDialogItem) {
        cartCommodities.value.find(item => {
            if (item.line_id === ClickshowDialogItem) {
                item.seiban = name
                item.fanhao_id = id
            }
        })
        ClickshowDialogItem = null
        searchValue.value = ''
        console.log('cartCommodities', cartCommodities.value);
        show.value = false
        searchList.value = []


    } else {
        Taro.showToast({
            title: '操作失败',
            icon: 'error',
            duration: 2000
        })
    }

}
const onScrollBottom = async () => {
    console.log('触底了');
    console.log('searchListIndex', searchListIndex.value);
    console.log('DQsearchListIndex', DQsearchListIndex.value);


    if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
        searchListIndex.value++
        const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
        if (error === null) {
            const { items, psize } = success as { items: Array<any>, psize: number }
            if (success && items) {
                searchList.value = searchList.value.concat(items)
                DQsearchListIndex.value = psize
            }
        }
    } else {
        Taro.showToast({
            title: '加载完毕了~',
            icon: 'error',
            duration: 2000
        })
    }

}

const stateMap = ref(new Map([
    ['null', true],
    ['Done', false]
]))

const addOrder = () => {
    let existingProductList = cartCommodities.value.map(i => { return { id: i.product_id, name: i.name } })


    // if(toRaw(checkList.value)[0]!=undefined){
    //     existingProductList.push(...toRaw(checkList.value)[0].map((i: { id: any; name: any; })=>{
    //     console.log(i,'1111');

    //     return {
    //         id:i.id,
    //         name:i.name
    //     }
    // }))
    // }
    // console.log(toRaw(checkList.value)[0],'1234');



    Taro.preload({
        existingProductList,
        OrderId: OrderId.value
    })
    Taro.navigateTo(
        {
            url: '/package/package-a/search/index'
        }
    )

}



const params = getCurrentInstance().router!.params

const minus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            if (id === item.line_id) {
                if (item.product_qty !== 1) {
                    item.product_qty -= 1
                    item.price_total = item.product_qty * item.price_unit
                } else {
                    Taro.showToast({
                        title: '最少购买一个商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const plus = (id: number) => {
    cartCommodities.value.forEach(
        item => {
            console.log("item------", id, item.line_id, item.product_qty);

            if (id === item.line_id) {
                if (item.product_qty !== 999) {

                    item.product_qty = Number(item.product_qty) + 1
                    item.price_total = item.product_qty * item.price_unit
                } else {
                    Taro.showToast({
                        title: '当前最多仅能购买' + 999 + '份该商品~',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        }
    )
    countTotalPrice()
}
const OrderId = ref('')
const addressOrder = ref('')
//备注及特殊发货要求
const note = ref('')
const special_note = ref('')
// options为可选参数，不传默认使用正则查找
const options = {
    type: 0, // 哪种方式解析，0：正则，1：树查找
    textFilter: [], // 预清洗的字段
    nameMaxLength: 4, // 查找最大的中文名字长度
}

const getOrderMessageF = async (id: string | number) => {
    console.log("------", id);
    const { error, success }: { error: any, success: any } = await getOrderMessage({ id })

    console.log('======', error, success);
    if (error == null && success) {
        //地址赋值

        if (success.address.length > 0) {
            const parseResult = AddressParse(success.address, options)
            harvestData.value[0].addressName = parseResult.name
            harvestData.value[0].phone = parseResult.phone
            harvestData.value[0].fullAddress = parseResult.province + parseResult.city + parseResult.area + parseResult.detail

        } else {
            harvestData.value[0].addressName = '采用U9默认联系地址'
        }

        //备注赋值
        note.value = success.notes
        OrderId.value = success.id
        addressOrder.value = success.address
        //商品列表渲染
        // cartCommodities
        cartCommodities.value = success.order_lines

        note.value = success.notes

        // cartCommodities.value.map(async i => {

        //         // console.log(success);
        //         i.image = success.order_lines.product_image





        // })

        countTotalPrice()

    }
}
const item = ref()
// let checkList: Ref<Array<any>> = ref([])
let addItemArray: Ref<Array<any>> = ref([])
let updateItemArray: Ref<Array<any>> = ref([])


const enable = (updateAddress: boolean = false) => {
    // console.log('~~~~~', removeItemArray.value.length, addItemArray.value.length, updateItemArray.value.length);

    if (removeItemArray.value.length > 0 || addItemArray.value.length > 0 || updateItemArray.value.length > 0 || updateAddress) {
        Taro.enableAlertBeforeUnload({
            message: '您有修改数据还未保存，确定要直接离开吗？',
            complete: () => {
            }
        })
    }


}
let watchResult = null
onMounted(() => {
    // cartCommodities.value.push(...preloadMessage!.message)
    eventCenter.on(getCurrentInstance().router!.onShow, async () => {



        await getOrderMessageF(params.id as any)
        // let pages = Taro.getCurrentPages();
        // let currentPage = pages[pages.length - 1]; // 获取当前页面
        // if (currentPage.__data__.checkList && currentPage.__data__.checkList.length > 0) { // 获取值


        //     checkList.value.push(JSON.parse(JSON.stringify(currentPage.__data__.checkList)))
        //     console.log('checkList', checkList);
        //     //清空
        //     currentPage.__data__.checkList = []




        // }
        //拿到新增数组
        // if (checkList.value.length > 0) {
        //     addItemArray.value = checkList.value.reduce((a, b) => a.concat(b)).map(i => {
        //         return {
        //             product_id: i.id,
        //             product_qty: i.product_qty,
        //             price_unit: i.price_unit,
        //             fanhao_id: i.fanhao_id,
        //             zx_line_notes: i.zx_line_notes,
        //             zx_mfplx: i.zx_mfplx

        //         }
        //     })
        // }
        countTotalPrice()
        // enable(true)
        watchResult = watch([cartCommodities, note, special_note, removeItemArray, updateItemArray, addItemArray, harvestData], (n, o) => {
            console.log('变化了', n, o);
            enable(true)


        }, { deep: true })
        Taro.disableAlertBeforeUnload({
            success: function (res) {
                console.log("成功&&：", res);
            },
            fail: function (err) {
                console.log("失败&&：", err);
            }
        })




    })
})


onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router!.onShow)


})


//控制弹出层
const popupShow = ref(false)
const formRef = ref(null)
const editClick = () => {
    console.log('Click To Edit')
    popupShow.value = true
}
const formData: Ref<{
    addressName: string,
    phone: string,
    fullAddress: string
}> = ref({
    addressName: '',
    phone: '',
    fullAddress: ''
})
const validatorName = () => {
    if (/^([\u4e00-\u9fa5]{2,6})$/gi.test(formData.value.addressName)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的姓名')
    }
}
const validatorPhone = () => {
    console.log('formData.value.phone', formData.value.phone, /^1[3-9]\d{9}$/.test(formData.value.phone));

    if (/^1[3-9]\d{9}$/.test(formData.value.phone)) {
        return Promise.resolve()
    } else {
        return Promise.reject('请输入正确的11位手机号')
    }
}
const validatorAddress = () => {
    console.log('formData.value.fullAddress', formData.value.fullAddress, /^[\u4e00-\u9fa5]{2,10}$/gi.test(formData.value.fullAddress));
    if (/^[\u4e00-\u9fa5a-zA-Z0-9!@#\$%\^&*()_+\-=\[\]{};':"\\|,.<>\/?~` ]{2,30}$/.test(formData.value.fullAddress)) {
        return Promise.resolve()
    } else {
        return Promise.reject('联系地址应尽量详实')
    }
}
const submitAddress = () => {

    formRef.value?.validate().then(({ valid, errors }) => {
        if (valid) {
            Taro.showToast({
                title: '暂存成功',
                success: () => {
                    console.log(Object.assign(harvestData.value[0], formData.value));
                    formData.value as Object
                    popupShow.value = false
                },
                duration: 2000
            })

        } else {
            console.warn('error:', errors)
        }
    })

}

//创建要货订单
const createOrder = () => {
}
// {
//     "product_id": 1564,
//     "product_qty": 10,
//     "price_unit": 30,
//     "fanhao_id": 1,
//     "zx_line_notes": "行内备注",
//     "zx_mfplx": "1"
// },
const submitOrderF = async () => {
    //拿到删除数组
    console.log('删除数组', removeItemArray.value);

    //拿到更新数组
    //提交前校验
    updateItemArray.value = cartCommodities.value.map(i => {
        return {
            product_id: i.product_id,
            product_qty: i.product_qty,
            price_unit: i.price_unit,
            fanhao_id: i.fanhao_id,
            zx_line_notes: i.zx_line_notes,
            zx_mfplx: '',
            line_id: i.line_id

        }
    })
    console.log('更新数组', updateItemArray.value);


    console.log('新增数组', addItemArray.value, JSON.stringify(addItemArray.value))

    //拿到行内备注及特殊发货要求
    console.log('拿到行内备注及特殊发货要求', note.value, special_note.value);
    let designConfirm = cartCommodities.value.find(el =>
        el.name.includes('特殊图案') && !('seiban' in el)
    )
    if (designConfirm) {
        Taro.showModal({
            title: '提示',
            content: '特殊图案产品需选择楼层图案后才可下单',
            success: function (res) {
            }
        })


    }
    else {
        const { error, success } = await updateOrder({
            ...(!['请输入姓名','采用U9默认联系地址'].includes(harvestData.value?.[0]?.addressName)&&{address: `${harvestData.value[0].fullAddress}${harvestData.value[0].phone}${harvestData.value[0].addressName}`}),
            note: note.value,
            special_note: special_note.value,
            add_lines: JSON.stringify(addItemArray.value),
            mod_lines: JSON.stringify(updateItemArray.value),
            del_lines: JSON.stringify(removeItemArray.value)
        }, {
            id: OrderId.value

        })
        if (error === null && success) {
            console.log('success---', success);
            Taro.showToast({
                title: '提交成功',
                success: () => {
                    setTimeout(() => {
                        Taro.switchTab({
                            url: '/pages/orders/index',
                            success: () => {
                                appStore.setActiveTab('/pages/orders/index');
                            }
                        })
                    }, 2000)

                },
                duration: 2000
            })

        } else {
            Taro.showToast({
                title: error!.message,
                icon: 'error',
                duration: 2000
            })
        }
    }





}

const harvestData: Ref<Array<IharvestData>> = ref([{
    addressName: '',
    phone: '',
    fullAddress: '',
}
])
const harvestOptions = {
    addressName: 'addressName',
    phone: 'phone',
    fullAddress: 'fullAddress'
}



const countTotalPrice = () => {
    totalPrice.value = cartCommodities.value.reduce((acc, curr) => acc + curr.price_unit * curr.product_qty, 0);
    if (addItemArray.value.length > 0) {
        totalPrice.value += addItemArray.value.reduce((a, b) => a + b.product_qty * b.price_unit, 0)
    }
}
const totalPrice = ref(0)
/** 设置页面属性 */
definePageConfig({
    navigationBarTitleText: '修改要货订单',
});

</script>

<style lang="scss">
page {
    background-color: #F0F0F0;
}

.button-minus {
    // border: 1px solid #aba8a8;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    background-color: #F9FAFC;
}

.button-plus {
    // border: 1px solid #aba8a8;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    background-color: #F9FAFC;
}

.button-cell-middle {
    // border-top: 1px solid #aba8a8;
    // border-bottom: 1px solid #aba8a8;
    background-color: #F9FAFC;
    text-align: center;
    line-height: 17px;
    margin: 0 5%;
}

.card {
    margin: 0 0;
}

.button-cell {
    min-width: 25px;
    height: 17px;
}

.quantity-button-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    bottom: 30px;
    right: 30px;
}

.jinyon {
    pointer-events: none;
}

.bottom-card {
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100vw;
    align-items: center;
    padding: .625rem;
    border-top: 1px solid #dcdcdc;
}

.nut-address-list {
    width: 100% !important;
}

.nut-address-list:last-child {
    padding-bottom: 0 !important;
}

.harvest-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.nut-fixed-nav__btn {
    width: 60px !important;
}

.noShow {
    visibility: hidden !important;
}
</style>