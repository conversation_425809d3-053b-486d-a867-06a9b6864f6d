{"name": "taro3-vue3-pinia", "version": "1.0.2", "private": true, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:aliapy-dd": "taro build --type dd --watch", "dev:weapp": "npm run build:weapp -- --watch --env production", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "tsx": "tsx", "format": "soy prettier --write", "cleanup": "soy cleanup", "commit": "git pull && pnpm format && pnpm lint && git add -A && soy git-commit && git push", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "release": "soy release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "soy update-pkg"}, "dependencies": {"@babel/runtime": "^7.23.6", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-cat": "^0.2.0", "@nutui/nutui-taro": "^4.3.13", "@tarojs/cli": "3.6.21", "@tarojs/components": "3.6.21", "@tarojs/helper": "3.6.21", "@tarojs/plugin-html": "3.6.21", "@tarojs/plugin-mock": "^0.0.9", "@tarojs/plugin-platform-alipay": "3.6.21", "@tarojs/plugin-platform-alipay-dd": "0.3.1", "@tarojs/plugin-platform-h5": "3.6.21", "@tarojs/plugin-platform-jd": "3.6.21", "@tarojs/plugin-platform-qq": "3.6.21", "@tarojs/plugin-platform-swan": "3.6.21", "@tarojs/plugin-platform-tt": "3.6.21", "@tarojs/plugin-platform-weapp": "3.6.21", "@tarojs/runtime": "3.6.21", "@tarojs/shared": "3.6.21", "@tarojs/taro": "3.6.21", "@tarojs/webpack5-runner": "3.6.21", "@vueuse/core": "^10.7.0", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "pinia": "^2.1.7", "qs": "^6.11.2", "vue": "^3.3.13", "vue-draggable-next": "^2.2.1", "zh-address-parse": "^1.3.16"}, "devDependencies": {"@babel/core": "^7.23.6", "@iconify/json": "^2.2.161", "@iconify/utils": "^2.1.13", "@soybeanjs/cli": "1.0.0-beta.7", "@tarojs/plugin-framework-vue3": "3.6.21", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.5", "@types/qs": "^6.9.11", "@types/webpack-env": "^1.18.4", "@unocss/webpack": "^0.58.0", "@vue/babel-plugin-jsx": "^1.1.5", "babel-preset-taro": "3.6.21", "eslint": "^8.56.0", "eslint-config-soybeanjs": "0.5.8", "simple-git-hooks": "^2.9.0", "taro-plugin-pinia": "^1.0.0", "tsx": "^4.7.0", "typescript": "5.3.3", "unocss": "^0.58.0", "unocss-preset-weapp": "^0.58.0", "unplugin-vue-components": "^0.26.0", "vue-loader": "^17.4.0", "vue-tsc": "^1.8.27", "webpack": "^5.89.0"}, "simple-git-hooks": {"commit-msg": "pnpm soy git-commit-verify", "pre-commit": "pnpm typecheck && pnpm soy lint-staged"}}