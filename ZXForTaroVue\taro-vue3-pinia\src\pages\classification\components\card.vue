<template>
    <div class="modern-product-card">
      <!-- 产品图片区域 -->
      <div class="product-image-section">
        <div class="image-container">
          <image
            :src="state.imgUrl || '/static/images/default-product.png'"
            class="product-image"
            mode="aspectFit"
          />
          <!-- 图片加载状态 -->
          <div v-if="!state.imgUrl" class="image-placeholder">
            <i class="i-bx-image placeholder-icon"></i>
          </div>
        </div>
        <!-- 产品标签 -->
        <div class="product-badges">
          <div class="badge self-operated">
            <i class="i-bx-check-circle"></i>
            <span>自营</span>
          </div>
        </div>
      </div>

      <!-- 产品信息区域 -->
      <div class="product-info-section">
        <!-- 产品型号 -->
        <div class="product-header">
          <div class="model-tag">
            <i class="i-bx-cube model-icon"></i>
            <span class="model-text">{{ state.title }}</span>
          </div>
        </div>

        <!-- 产品名称 -->
        <div class="product-name">
          <h3 class="name-text">{{ state.shopDesc }}</h3>
        </div>

        <!-- 底部操作区域 -->
        <div class="product-footer">
          <div class="product-status">
            <div class="status-indicator available">
              <i class="i-bx-check-circle"></i>
              <span>现货</span>
            </div>
          </div>
          <div class="view-details">
            <span class="details-text">查看详情</span>
            <i class="i-bx-right-arrow-alt details-arrow"></i>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script setup lang="ts">
  import {defineProps, onMounted, watchEffect} from 'vue';
  const props=defineProps({
    item:{
        type:Object,
        required:true
    }
  })
//   const emit=defineEmits(['click-card'])
//   const clickCard=()=>{
//     emit('click-card',)
//   }
  onMounted(()=>{

  })
 
  import { ref } from 'vue'
  const state = ref({
    imgUrl:'',
    title: '',
    shopDesc: '自营',
  })
  watchEffect(()=>{
    state.value.title=props.item.model,
    state.value.imgUrl=props.item.image
    state.value.shopDesc=props.item.name.split('/')[0]
  })
  </script>
  <style lang="scss">
  // 主色调变量
  $primary-color: #122F38;
  $primary-light: rgba(18, 47, 56, 0.1);
  $primary-dark: #0d252c;
  $accent-color: #FF6B35;
  $success-color: #4CAF50;
  $warning-color: #FF9800;
  $error-color: #F44336;
  $info-color: #2196F3;
  $text-primary: #122F38;
  $text-secondary: #666;
  $text-light: #999;
  $background-light: #f8f9fa;
  $border-color: rgba(18, 47, 56, 0.1);

  // 现代化产品卡片
  .modern-product-card {
    display: flex;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(18, 47, 56, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin: 12px 12px 12px 0;
    width: calc(100% - 12px);
    min-height: 120px;

    // 悬停效果
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 24px rgba(18, 47, 56, 0.12);

      .product-image-section {
        .image-container {
          .product-image {
            transform: scale(1.03);
          }
        }
      }

      .product-info-section {
        .product-footer {
          .view-details {
            .details-arrow {
              transform: translateX(3px);
            }
          }
        }
      }
    }

    // 点击效果
    &:active {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(18, 47, 56, 0.1);
    }
  }

  // 产品图片区域
  .product-image-section {
    position: relative;
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, $background-light 0%, #fff 100%);

    .image-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;

      .product-image {
        width: 90px;
        height: 90px;
        border-radius: 12px;
        transition: all 0.3s ease;
        object-fit: cover;
      }

      .image-placeholder {
        width: 90px;
        height: 90px;
        background: $primary-light;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-icon {
          font-size: 32px;
          color: $primary-color;
          opacity: 0.6;
        }
      }
    }

    // 产品标签
    .product-badges {
      position: absolute;
      top: 8px;
      left: 8px;

      .badge {
        display: inline-flex;
        align-items: center;
        gap: 3px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;

        &.self-operated {
          background: rgba(76, 175, 80, 0.15);
          color: $success-color;
          border: 1px solid rgba(76, 175, 80, 0.3);

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 产品信息区域
  .product-info-section {
    flex: 1;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;

    // 产品型号标签
    .product-header {
      margin-bottom: 8px;

      .model-tag {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        background: linear-gradient(135deg, $primary-light 0%, rgba(18, 47, 56, 0.05) 100%);
        color: $primary-color;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        border: 1px solid rgba(18, 47, 56, 0.15);
        max-width: fit-content;

        .model-icon {
          font-size: 12px;
        }

        .model-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100px;
        }
      }
    }

    // 产品名称
    .product-name {
      flex: 1;
      margin-bottom: 8px;

      .name-text {
        font-size: 14px;
        font-weight: 500;
        color: $text-primary;
        line-height: 1.3;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        height: auto;
        max-height: 36px;
      }
    }

    // 底部操作区域
    .product-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: auto;

      .product-status {
        .status-indicator {
          display: inline-flex;
          align-items: center;
          gap: 3px;
          font-size: 10px;
          font-weight: 500;

          &.available {
            color: $success-color;

            i {
              font-size: 11px;
            }
          }
        }
      }

      .view-details {
        display: flex;
        align-items: center;
        gap: 3px;
        color: $primary-color;
        font-size: 11px;
        font-weight: 500;

        .details-text {
          white-space: nowrap;
        }

        .details-arrow {
          font-size: 12px;
          transition: all 0.3s ease;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 375px) {
    .modern-product-card {
      margin: 8px 8px 8px 0;
      width: calc(100% - 8px);
      border-radius: 12px;
      min-height: 100px;

      &:hover {
        transform: translateY(-1px);
      }
    }

    .product-image-section {
      width: 90px;
      height: 100px;

      .image-container {
        .product-image {
          width: 65px;
          height: 65px;
          border-radius: 8px;
        }

        .image-placeholder {
          width: 65px;
          height: 65px;
          border-radius: 8px;

          .placeholder-icon {
            font-size: 20px;
          }
        }
      }

      .product-badges {
        top: 4px;
        left: 4px;

        .badge {
          padding: 2px 4px;
          font-size: 8px;

          &.self-operated {
            i {
              font-size: 9px;
            }
          }
        }
      }
    }

    .product-info-section {
      padding: 8px 12px;

      .product-header {
        margin-bottom: 6px;

        .model-tag {
          padding: 3px 6px;
          font-size: 10px;
          border-radius: 8px;

          .model-icon {
            font-size: 10px;
          }

          .model-text {
            max-width: 70px;
          }
        }
      }

      .product-name {
        margin-bottom: 6px;

        .name-text {
          font-size: 13px;
          max-height: 32px;
          line-height: 1.2;
        }
      }

      .product-footer {
        .product-status {
          .status-indicator {
            font-size: 9px;

            &.available {
              i {
                font-size: 10px;
              }
            }
          }
        }

        .view-details {
          font-size: 10px;

          .details-arrow {
            font-size: 11px;
          }
        }
      }
    }
  }

  // 超小屏幕优化
  @media (max-width: 320px) {
    .modern-product-card {
      margin: 6px 6px 6px 0;
      width: calc(100% - 6px);
      min-height: 90px;
    }

    .product-image-section {
      width: 75px;
      height: 90px;

      .image-container {
        .product-image {
          width: 55px;
          height: 55px;
        }

        .image-placeholder {
          width: 55px;
          height: 55px;

          .placeholder-icon {
            font-size: 18px;
          }
        }
      }
    }

    .product-info-section {
      padding: 6px 10px;

      .product-header {
        margin-bottom: 4px;

        .model-tag {
          padding: 2px 4px;
          font-size: 9px;

          .model-icon {
            font-size: 9px;
          }

          .model-text {
            max-width: 60px;
          }
        }
      }

      .product-name {
        margin-bottom: 4px;

        .name-text {
          font-size: 12px;
          max-height: 28px;
          line-height: 1.1;
        }
      }

      .product-footer {
        .product-status {
          .status-indicator {
            font-size: 8px;

            &.available {
              i {
                font-size: 9px;
              }
            }
          }
        }

        .view-details {
          font-size: 9px;

          .details-arrow {
            font-size: 10px;
          }
        }
      }
    }
  }
  </style>
  