// 导入unocss
import UnoCSS from 'unocss/webpack';
import ComponentsPlugin from 'unplugin-vue-components/webpack';
import Mock from 'mockjs';
const path = require('path');

const NutUIResolver = () => {
  // eslint-disable-next-line consistent-return
  return name => {
    if (name.startsWith('Nut')) {
      const partialName = name.slice(3);
      return {
        name: partialName,
        from: '@nutui/nutui-taro',
        sideEffects: `@nutui/nutui-taro/dist/packages/${partialName.toLowerCase()}/style`
      };
    }
  };
};

const config = {
  projectName: 'Taro3',
  date: '2021-12-18',
  designWidth(input) {
    // 配置 NutUI 375 尺寸
    if (input?.file?.replace(/\\+/g, '/').indexOf('@nutui') > -1) {
      return 375;
    }
    // 设计稿尺寸
    return 375;
  },
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
    375: 2 / 1
  },
  cssModules:{
    enable: true
  },
  alias: {
    '@': path.resolve(__dirname, '..', 'src')
  },
  compiler: 'webpack5',
  sourceRoot: 'src',
  outputRoot: `dist/${process.env.TARO_ENV}`,
  plugins: ['@tarojs/plugin-html', 'taro-plugin-pinia',
    //  [
    // "@tarojs/plugin-mock",
    // {   //Mock 插件可以接受如下参数
    //     host: "localhost",  //	设置数据 mock 服务地址，默认为 127.0.0.1
    //     port: 9999, //设置数据 mock 服务端口，默认为 9527
    //     mocks: { //设置数据 mock 接口
    //         "POST /api/user/login":{
    //           code:200,
    //           message:"success login",
    //           result:{
    //             token:"123456789"
    //           }
    //         },
    //         "GET /api/user/userInfo":{
    //           code:200,
    //           message:"success userInfo",
    //           result:{
    //             nickName:'mockjs用户名',
    //             avatarUrl:'//srv.carbonads.net/static/30242/774d77f91e928e2ec56dcdf8768f7118df35a47a',
    //             userId:'1'
                
    //           }
    //         },
            
    //         "GET /api/address/get":{
    //           code:200,
    //           message:"get address",
    //           result:[
    //             {
    //               testid: 1,
    //               testaddressName: '李敏',
    //               phone: '15279710095',
    //               defaultAddress: true,
    //               fullAddress: '广东省中山市古镇镇盛丰街12号1号楼2-9楼'
    //           },
    //           {
    //               testid: 2,
    //               testaddressName: '刘琦',
    //               phone: '13217074303',
    //               defaultAddress: false,
    //               fullAddress: '江西省九江市都昌县温州工业园'
    //           }
    //         ] 
    //         },
    //         "GET /api/address/getDefault":{
    //           code:200,
    //           message:"get default address",
    //           result:{
    //             testid: 1,
    //             testaddressName: '李敏',
    //             phone: '15279710095',
    //             defaultAddress: true,
    //             fullAddress: '广东省中山市古镇镇盛丰街12号1号楼2-9楼'
    //           }
    //         },
    //         "POST /api/address/add":{
    //             code:200,
    //             message:"add address",
    //             result:{testid:3}
    //         },
    //         "PUT /api/address/update":{
    //             code:200,
    //             message:"update address",
    //         },
    //         "DELETE /api/address/delete":{
    //           code:200,
    //           message:'delete address'
    //         },
    //         "GET /api/stock/get":{
    //           code:200,
    //           message:"get stock",
    //           result:[	
    //           {
    //             id: 1,
    //             name: '双双/常规330彩钢标志灯',
    //             model: 'Z0311',
    //             num: '1',
    //             price:'100',
    //             status: '正常',
    //             operation: (row) => {
    //               return h(
    //                 Button,
    //                 {
    //                   type: "success",
    //                   style: {
    //                     background: '#27C631',
    //                     color: '#fff',
    //                     fontSize: '12px',
    //                     padding: '0 8px',
    //                     borderRadius: '4px',
    //                     marginLeft: '10px'
    //                   },
    //                   onClick: (event) => checkView(row)
    //                 },
    //                 '查看'
    //               )
    //             },
    //           },
    //           {
    //             id: 2,
    //             name: '双单/常规330彩钢标志灯',
    //             model: 'Z0311',
    //             num: '150',
    //             price:'66.7',
    //             status: '正常',
    //             operation: (row) => {
    //               return h(
    //                 Button,
    //                 {
    //                   type: "success",
    //                   style: {
    //                     background: '#27C631',
    //                     color: '#fff',
    //                     fontSize: '12px',
    //                     padding: '0 8px',
    //                     borderRadius: '4px',
    //                     marginLeft: '10px'
    //                   },
    //                   onClick: (event) => checkView(row)
    //                 },
    //                 '查看'
    //               )
    //             },
    //           },
    //           {
    //             id: 3,
    //             name: '常规亚银双头灯',
    //             model: 'Z0151',
    //             num: '150',
    //             price:'66.7',
    //             status: '正常',
    //             operation: (row) => {
    //               return h(
    //                 Button,
    //                 {
    //                   type: "success",
    //                   style: {
    //                     background: '#27C631',
    //                     color: '#fff',
    //                     fontSize: '12px',
    //                     padding: '0 8px',
    //                     borderRadius: '4px',
    //                     marginLeft: '10px'
    //                   },
    //                   onClick: (event) => checkView(row)
    //                 },
    //                 '查看'
    //               )
    //             },
    //           },
              
    //         ]
    //         },
    //         "GET /api/stock/list":{
    //           code:200,
    //           message:"get stock list",
    //           result:[	
    //           {
    //             value: '自电非集控',
    //             text: '自电非集控',
    //             children: [
    //               {
    //                 value: '自电非集控标志灯',
    //                 text: '自电非集控标志灯',
    //                 children: [
    //                   { value: 'Z0311', text: 'Z0311' },
    //                   { value: 'Z0351', text: 'Z0351' }
    //                 ]
    //               },
    //               {
    //                 value: '自电非集控双头灯',
    //                 text: '自电非集控双头灯',
    //                 children: [
    //                   { value: 'Z0111B', text: 'Z0111B' },
    //                   { value: 'Z0112', text: 'Z0112' },
    //                   {value:'Z0151',text:'Z0151'}
    //                 ]
    //               },
    //               {
    //                 value: '自电非集控壁灯',
    //                 text: '自电非集控壁灯',
    //                 children: [
    //                   { value: 'Z0711B', text: 'Z0711B' },
    //                 ]
    //               },
            
    //             ]
    //           },
    //           {
    //             value: '集电集控',
    //             text: '集电集控',
    //             children: [
            
    //             ]
    //           }]
    //         },
    //         "GET /api/stock/getCount":{
    //           code: 200,
    //           message: "success",
    //           result: {
    //             count:45
    //           }
    //         },
    //         "GET /api/order/get":{
    //           code: 200,
    //           message: "success order",
    //           result:[
              //   {
              //     id: 1,
              //     commodities: [
              //         {
              //             image: '//img01.71360.com/file/read/www/M00/32/3E/wKj0iWCvDsCAX902AAJFkTXYj78878.jpg?w=600',
              //             title: 'Z1410L',
              //             unit: '壁挂/352*138*6.5/DC36V/15cm/背出线/30pcs',
              //             price: 27.7,
              //             count: 1,
              //         },
              //         {
              //             image: '//img01.71360.com/file/read/www/M00/32/3E/wKj0iWCvDsCAX902AAJFkTXYj78878.jpg?w=600',
              //             title: 'zx-0809',
              //             unit: '352*138*4mm/DC24-36V/吊装/15cm/上出线/40pcs',
              //             price: 33.2,
              //             count: 1,
              //         }
              //     ],
              //     status: 3,
              //     actualPay: 59.9,  // 实际付款
              // },
              // {
              //     id: 2,
              //     commodities: [
              //         {
              //             image: 'https://img01.71360.com/file/read/www/M00/32/3D/wKj0iWCvDheAWnkyAAIp1TGXJKI384.jpg?w=600',
              //             title: '260欧款吸顶灯',
              //             unit: '352*138*4mm/DC24-36V/吊装/15cm/上出线/40pcs',  //规格
              //             price: 30,
              //             count: 1,
              //         },
              //     ],
              //     status: 1,
              //     // statusDescription: '订单已确认, 开始配货',
              //     actualPay: 30,  // 实际付款
              // },
    //           ]
    //         },
    //         "DELETE /api/order/delete":{
    //           code:200,
    //           message:"delete order",
    //           result:{
    //             ordersId:999
    //           }
    //         },
    //         "POST /api/order/submit":{
    //           code:200,
    //           message:"submit order",
    //           result:{
    //             ordersId:999
    //           }
    //         },
    //         "GET /api/cart/get":{
    //           code:200,
    //           message:"get cart",
    //           result:[ { image: '//img01.71360.com/file/read/www/M00/37/C6/rBwBHmTHftyAVfbCAAGBVz9mhbk875.png?w=600', id: 1, title: 'Z-1150火灾声光警报器', price: 10.90, count: 10, maxBuy: 10000, checkbox: false },
    //             { image: '//img01.71360.com/file/read/www/M00/2A/75/wKj0iWCTU_qAWdE7AATXhREln38244.png?w=600', id: 2, title: 'ZX0151亚银双头灯', price: 66.60, count: 1, maxBuy: 10000, checkbox: false },
    //           ]
    //         },
    //         "GET /api/product/detail":{
    //           code:200,
    //           message:"get product detail",
    //           result:{
    //             '产品名称': '测试接口使用',
    //             '产品型号': 'Z0311',
    //             '外形尺寸': '82x25x23mm',
    //             '开启照度值': '--',
    //             '感应角度': '--',
    //             '延时': '--',
    //             '负载功率': '<=200w',
    //             '待机功率': '0.5w'
    //           }
    //         }
            
            
    //     },
    // },
// ]
],
  sass: {
    data: `@import "@nutui/nutui-taro/dist/styles/variables.scss";`
  },
  defineConstants: {},
  copy: {
    patterns: [],
    options: {}
  },
  framework: 'vue3',
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      url: {
        enable: true,
        config: {
          /** 设定转换尺寸上限 */
          limit: 1024
        }
      },
      cssModules: {
        /** 默认为 false，如需使用 css modules 功能，则设为 true  */
        enable: false,
        config: {
          /** 转换模式，取值为 global/module */
          namingPattern: 'module',
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    // 合并webpack配置
    webpackChain(chain) {
      chain.plugin('unocss').use(UnoCSS());
      chain.plugin('unplugin-vue-components').use(
        ComponentsPlugin({
          dts: 'src/typings/components.d.ts',
          resolvers: [NutUIResolver()]
        })
      );
    }
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    esnextModules: ['nutui-taro'],
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      autoprefixer: {
        enable: true,
        config: {}
      },
      cssModules: {
        /** 默认为 false，如需使用 css modules 功能，则设为 true */
        enable: false,
        config: {
          /** 转换模式，取值为 global/module */
          namingPattern: 'module',
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    // 合并webpack配置
    webpackChain(chain) {
      chain.plugin('unocss').use(UnoCSS());
      chain.plugin('unplugin-vue-components').use(
        ComponentsPlugin({
          dts: 'src/typings/components.d.ts',
          resolvers: [NutUIResolver()]
        })
      );
    },
    devServer: {
      proxy: {
        '/api': {
          target: 'https://getman.cn/mock',
          changeOrigin: true,
          pathRewrite: {
            '^/api': ''
          }
        }
      }
    }
  },
  rn: {
    appName: 'taro3',
    postcss: {
      cssModules: {
        enable: false // 默认为 false，如需使用 css modules 功能，则设为 true
      }
    }
  }
};

module.exports = merge => {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'));
  }
  return merge({}, config, require('./prod'));
};
