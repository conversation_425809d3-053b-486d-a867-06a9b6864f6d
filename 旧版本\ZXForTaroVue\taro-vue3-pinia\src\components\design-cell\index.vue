<template>
    <div>
        <nut-cell @click="showDialog"
            style="color:#909090;padding-top: 1%;padding-bottom: 1%;background: #F2F2F2;margin-top: 0;font-size: 12px;">
            <template #title>
                <span style="color:#777" >{{ '楼层:' }} </span>
            </template>
            <template #desc>
                <span style="font-size: 12px;" :class="{'color-red font-900':(props.name!.includes('特殊图案'))}">{{ props.design.length>0?props.design:'未选择'  }}
                    <i class="i-bx-bxs-chevron-right" style="vertical-align: text-bottom;color: #636363;"></i>
                </span>
            </template>
        </nut-cell>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props=defineProps({
    design: {
        type: String,
        required: false,
        default:''
    },
    id:{
        type: Number,
        required: true
    },
    index:{
        type: Number,
        required: true
    
    },
    name:{
        type: String,
        required: false,
        default:''

    }
})
const emit=defineEmits(['update:design'])
const showDialog = () => {
	emit('update:design',{id:props.id,index:props.index})
}
const noShow=props.name!.includes('特殊图案')
// console.log(noShow,'noShow',props.name);

</script>

<style scoped>
.noShow{
    color: transparent;
    background: transparent;
}
</style>