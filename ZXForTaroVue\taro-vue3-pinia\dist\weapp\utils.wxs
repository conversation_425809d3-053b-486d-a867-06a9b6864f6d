module.exports = {
  a: function (l, n, s) {
    var a = ["7","0","21","5","2","12","6","4","55","56","29","23","52","60","61"]
    var b = ["4","55","56","29","23","52","60","61"]
    if (a.indexOf(n) === -1) {
      l = 0
    }
    if (b.indexOf(n) > -1) {
      var u = s.split(',')
      var depth = 0
      for (var i = 0; i < u.length; i++) {
        if (u[i] === n) depth++
      }
      l = depth
    }
    if (l >= 15) {
      return 'tmpl_15_container'
    }
    return 'tmpl_' + l + '_' + n
  },
  b: function (a, b) {
    return a === undefined ? b : a
  },
  c: function(i, prefix) {
    var s = i.focus !== undefined ? 'focus' : 'blur'
    return prefix + i.nn + '_' + s
  },
  e: function (n) {
    return 'tmpl_' + n + '_container'
  },
  f: function (l, n) {
    var b = ["4","55","56","29","23","52","60","61"]
    if (b.indexOf(n) > -1) {
      if (l) l += ','
      l += n
    }
    return l
  }
}