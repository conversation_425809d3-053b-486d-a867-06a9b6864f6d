<script setup lang="ts">
import Taro from '@tarojs/taro';
import { useRouter } from '@tarojs/taro';
import { onMounted, Ref, ref } from 'vue';
import { My, Ask2 } from '@nutui/icons-vue-taro'
import { login, getUserInfo } from '@/service/api/index';
import { useAuthStore } from '@/store/modules/auth'
import { localStg } from '~/src/utils';
import { useAppStore } from '@/store';
const auth = useAuthStore()
definePageConfig({
	navigationBarTitleText: '登录'
});
const appStore = useAppStore();
// 登录
const getUserProfile = () => {
	loginFormRef.value?.validate().then(({ valid, errors }: { valid: string, errors: any }) => {
		if (valid&& !buttonClick.value) {
			console.log('success:', valid)
			Taro.login({
				success: async (res) => {
					//发起网络请求
					const { error, success }: { error: any, success: any } = await login({ username: loginFormData.value.username, password: loginFormData.value.password, db: 'odoo17' })
					console.log("返回值", error, success);

					if (error == null) {
						console.log(success, 'success20241130')
						auth.token = success.token
						Taro.setStorageSync('token', success.token)


						const res = await getUserInfo({ token: success.token })
						Taro.setStorageSync('userInfo', (res.success as any)!.user)
						Taro.showToast({
							title: '登录成功',
							icon: 'success',
							duration: 2000,
							success: () => {


								setTimeout(() => {
									appStore.setActiveTab('/pages/products/index');

									Taro.switchTab({
										url: '/pages/products/index'
									})
								}, 2000);
							}
						});
					} else {
						console.log('error~~~~~~~~~~`');

						// Taro.showToast({
						// 	title: error,
						// 	icon: 'error',
						// 	duration: 2000
						// })
					}

				},
				fail: (res) => {
					Taro.showToast({
						title: '网络异常',
						icon: 'error',
						duration: 2000
					})
				},
				timeout: 5000
			})
		} else {
			console.warn('error:', valid, errors)
		}
	})


	// Taro.getUserProfile({
	// 	desc: '用于获取用户头像和昵称', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
	// 	success: (res) => {
	// 		const { avatarUrl, nickName } = res.userInfo;
	// 		// localStorage
	// 		Taro.setStorageSync('userAvatar', avatarUrl);
	// 		Taro.setStorageSync('userNickName', nickName);
	// 		console.log(avatarUrl, nickName);

	// 		Taro.showToast({
	// 			title: "欢迎您，" + nickName,
	// 			icon: 'none',
	// 			duration: 2000,
	// 			success: () => {
	// 				localStg.set('token','123')
	// 				setTimeout(() => {
	// 					Taro.switchTab({
	// 						url:'/pages/my/index'
	// 					})
	// 				}, 2000);
	// 			}
	// 		});
	// 	}
	// })
}
const loginFormData: Ref<{
	username: string,
	password: string
}> = ref({
	username: '',
	password: '',
})
const loginFormRef = ref(null)
const goToTermsUse = () => {
	Taro.navigateTo({
		url: '/package/package-a/termsUse/index'
	})

}
const val = ref(false)
const buttonClick = ref(true)
const changeVal = () => {
    buttonClick.value = !buttonClick.value
}
</script>
<template>
	<basic-layout>
		<!-- <custom-navbar title="登录" left-show/> -->
		<div class="login-container">
			<div class="ava-card flex justify-center flex-col items-center">
				<Image mode="aspectFit"
					src="https://img01.71360.com/w3/w426o/20240402/17d52803bad6c4c72e6f647e58791345.jpg" />

				<view class="ava-card-title">
					{{ '左向订货系统' }}
				</view>
				<view class="ava-card-desc">
					{{ '为人类的消防安全事业，奋斗不息！' }}
				</view>
			</div>
			<div class="login-form">
				<nut-form class="" :model-value="loginFormData" ref="loginFormRef">
					<nut-form-item prop="username" required :rules="[{ required: true, message: '请输入用户名' }]">
						<nut-input v-model="loginFormData.username" placeholder="请输入用户名" type="text">
							<template #left>
								<My></My>
							</template>
						</nut-input>
					</nut-form-item>
					<nut-form-item prop="password" :rules="[
						{
							required: true,
							message: '请输入密码',
						}
					]">
						<nut-input v-model="loginFormData.password" placeholder="请输入密码" type="password" @blur="">
							<template #left>
								<!-- <Ask2></Ask2> -->
								<i class="i-bx-key" style="font-size: 20px;"></i>
							</template>
						</nut-input>
					</nut-form-item>
				</nut-form>
			</div>
			<div>
            <nut-button size="large" block color="#1890FF" class="login-button" :disabled="buttonClick"
                @tap="getUserProfile">{{
                    '点击登录' }}
            </nut-button>
            <div style="display: flex;justify-content: center;align-items: baseline;margin-top: 5%;">
                <nut-switch v-model="val" @change="changeVal" active-color="#1890FF" style="margin-right: 2%;" />
                <view class="annotation-desc">{{ '登录即代表阅读并同意' }}<span class="desc-span" @click="goToTermsUse">{{
                    '使用协议和隐私'
                        }}</span>
                </view>

            </div>
        </div>


		</div>
	</basic-layout>
</template>

<style lang="scss">
.nut-input {
	color: #000000;
}

.login-container {
	.ava-card {
		.ava-card-title {
			color: #1F1F1F;
			font-weight: 400;
			font-size: 1rem;
		}

		.ava-card-desc {
			font-size: .75rem;
			margin-top: 3%;
			color: #9B9B9B;
		}


	}

	.login-button {
		width: 80%;
		margin-top: 20%;
		margin-left: auto;
		margin-right: auto;
	}

	.login-form {
		.nut-cell-group__wrap {
			box-shadow: none !important;
		}

		margin: 0 3rem;
	}

	.annotation-desc {
		font-size: .75rem;
		margin-top: 3%;
		color: #9B9B9B;
		text-align: center;

	}

	.desc-span {
		color: #1B9FFF;
		font-size: .75rem;
	}
}
</style>
