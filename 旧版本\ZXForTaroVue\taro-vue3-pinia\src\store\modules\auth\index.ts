import { defineStore } from 'pinia';
import { getUserInfo, getToken, clearAuthStorage } from './helpers';
import Taro from '@tarojs/taro';

interface AuthState {
	/** 用户信息 */
	userInfo:()=> {
		nickName:string,
		avator:string,
		userId:number|null
	};
	/** 用户token */
	token: ()=>string
}

export const useAuthStore = defineStore('auth-store', {
	state: (): AuthState => ({
		userInfo: ()=>Taro.getStorageSync('userInfo'),
		token: ()=>Taro.getStorageSync('token')
	}),
	getters: {
		/** 是否登录 */
		isLogin: state => {
			console.log('执行getters');
			// state.token=getToken()

			
			
			return Boolean(state.token)
		}
	},
	actions: {
		/** 重置auth状态 */
		resetAuthStore() {
			console.log('1');
			
			clearAuthStorage();
			console.log('2');
			
			// this.$reset();
			console.log('3');
			
		},
		setUserInfo(userInfo: Auth.UserInfoProp) {
			this.userInfo = userInfo
		},
	}
});
