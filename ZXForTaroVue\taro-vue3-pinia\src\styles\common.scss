html,
body,
#app {
	width: 100%;
	height: 100%;
}

body {
	box-sizing: border-box;
	font-size: 16px;
	font-family: "Helvetica Neue", PingFang-SC-Regular, miui, Helvetica,
		sans-serif;
	user-select: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-overflow-scrolling: touch;
	-webkit-text-size-adjust: 100% !important;
}

div {
	-webkit-tap-highlight-color: transparent;
}

a {
	text-decoration: none;
	-webkit-tap-highlight-color: transparent;
}

input {
	padding-left: 0;
	border: 0;
	border-radius: 0;
	outline: none;
	box-shadow: none;
	appearance: none;
}

page {
	font-size: 16px;
	font-family:
		-apple-system,
		BlinkMacSystemFont,
		"Helvetica Neue",
		Helvetica,
		Segoe UI,
		Arial,
		Roboto,
		"PingFang SC",
		"miui",
		"Hiragino Sans GB",
		"Microsoft Yahei",
		sans-serif;
}

// 解决iPhone x 以后的机型 底部安全区域的问题 https://jelly.jd.com/article/6006b1055b6c6a01506c87fd
.safe-area-bottom {
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
	padding-top: constant(safe-area-inset-top);
	padding-top: env(safe-area-inset-top);
}

.layout-screen {
	min-height: calc(100vh - constant(safe-area-inset-bottom));
	min-height: calc(100vh - env(safe-area-inset-bottom));
}

.layout-tabbar-screen {
	min-height: calc(100vh - 50px - constant(safe-area-inset-bottom));
	min-height: calc(100vh - 50px - env(safe-area-inset-bottom));
}

.layout-tabbar-safe-bottom {
	padding-bottom: calc(50px + constant(safe-area-inset-bottom));
	padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

.nut-theme-dark {
	color: var(--nut-dark-color, var(--nut-white, #fff));
}
