<script setup lang="ts">
import Taro, { eventCenter, getCurrentInstance, getCurrentPages } from '@tarojs/taro';
import { ref, reactive, onMounted, computed, Ref, onUnmounted } from 'vue'
import { getAddress, deleteAddress } from '@/service/index'
import { useAuthStore } from '@/store/index';

definePageConfig({
    navigationBarTitleText: '收货地址'
});
enum Estate {
    添加 = 'add',
    编辑 = 'update',
    删除 = 'delete'

}
interface IharvestData {
    testaddressName: string,
    phone: string,
    defaultAddress: false,
    address: string
    id: string | number | null
}

const auth = useAuthStore()


const onShowPage = async () => {
    console.log('aaaaaa');
    //用户是否登录
    if (Taro.getStorageSync('token') !== '') {
        const { error, success } = await getAddress()
        console.log(error,'error');
        console.log(success,'success');
        
        
        if (error === null) {
            harvestData.value = success.items
        }

    }

}
const instance = getCurrentInstance()
onMounted(() => {

    if (instance && instance.router && instance.router.onShow) {
        eventCenter.on(instance.router.onShow, onShowPage)
    } else {
        // 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
        console.error('无法获取router.onShow事件处理函数')
        Taro.showToast({
            title: '无法获取事件',
            icon: 'none',
            duration: 2000
        })
    }




})
onUnmounted(() => {
    if (instance && instance.router && instance.router.onShow) {
        eventCenter.off(instance.router.onShow, onShowPage)
    } else {
        // 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
        console.error('无法获取router.onShow事件处理函数')
        Taro.showToast({
            title: '无法获取事件',
            icon: 'none',
            duration: 2000
        })
    }

})

const harvestData: Ref<Array<IharvestData>> = ref([
])
const harvestDataOptions = reactive({
    id: 'id',
    fullAddress: 'address',
    addressName: 'name'
})
// const clickItem = (e:any) => {
//     editorAddress(Estate.编辑,e)
// }

const editClick = ($event: any, $item: any) => {

    editorAddressF(Estate.编辑, $item)
}

// 新建、编辑地址
const editorAddressF = (state: Estate, item: any) => {
    Taro.navigateTo({
        url: `/package/package-a/harvestAddress/setAddress/index?state=${state}`,
        // events: {
        //     someEvent: (data: any) => {
        //         if (data.defaultAddress === true) {
        //             //先全部取反
        //             harvestData.value.forEach(item => {
        //                 item.defaultAddress = false
        //             })
        //         }
        //         harvestData.value.push(data)
        //     },
        //     updateEvent: (data: any) => {
        //         console.log('updateEvent', data);

        //         if (data.defaultAddress === true) {
        //             harvestData.value.forEach(item => {
        //                 item.defaultAddress = false
        //             })

        //         }
        //         //找到要修改的那一条数据
        //         harvestData.value = harvestData.value.map((item) => {
        //             console.log(item, data);

        //             if (item.testid === data.testid) {
        //                 item = data
        //             }
        //             return item

        //         })

        //     }
        // }
    })
    //预加载数据
    Taro.preload({ item })
}
//要删除的地址的id
const deleteId = ref('')
const visibleDeleteAddress = ref(false)
//删除地址
const deleteAddressF = ($event: any, $item: any) => {
    visibleDeleteAddress.value = true
    deleteId.value = $item.id
}
const onDeleteOk = async () => {
    //发送请求
    const res = await deleteAddress({id:deleteId.value},{id:deleteId.value})
    console.log(res)
    if (res.error === null) {
        harvestData.value = harvestData.value.filter(item => {
            return item.id !== deleteId.value
        })
        deleteId.value = ''
        Taro.showToast({
            title: '删除成功',
            icon: 'none',
            duration: 2000
        })

    }

}



const test = ($event: any, $item: any) => {
    console.log($item, $event);
    const pages = getCurrentPages()
    const current = pages[pages.length - 1]
    const eventChannel = current.getOpenerEventChannel()
    $item.gotoback = true
    $item.testid = $item.id
    $item.testaddressName = $item.addressName
    eventChannel.emit('defaultEvent', $item);
    Taro.navigateBack({
        delta: 1
    })

}
</script>
<template>
    <basic-layout show-tab-bar>
        <!-- <custom-navbar title="收货地址"  left-show/> -->
        <nut-dialog title="删除地址" content="确认删除当前收货地址吗？" v-model:visible="visibleDeleteAddress" @ok="onDeleteOk" />
        <div class="harvest-box">
            <nut-address-list :data="harvestData" v-if="harvestData.length > 0" @del-icon="deleteAddressF"
                @edit-icon="editClick" :show-bottom-button="false" :data-options="harvestDataOptions"
               >
            </nut-address-list>
            <nut-empty v-else image="https://static-ftcms.jd.com/p/files/61a9e3313985005b3958672e.png"
                description="暂无收货地址"></nut-empty>


        </div>
        <div class="harvest-newButton text-center m-auto">
            <nut-button size="large" color="linear-gradient(to right, #ff6034, #ee0a24)"
                @click="editorAddressF(Estate.添加, '这里传递过来id')">{{ '新建地址' }}</nut-button>

        </div>
    </basic-layout>
</template>


<style lang="scss">
.harvest-newButton {
    width: 80%;
}
</style>