import { createApp } from 'vue';
import { setupStore } from './store';
import { setupAssets } from './plugins';
import Taro from '@tarojs/taro';


const App = createApp({
  onShow() {
    Taro.cloud.init({
      env:"success1015-1gn83n2ka7f9103e"
    })
    // 查询缓存cart是否存在，不存在则创建
    Taro.getStorage({
      key: 'cart',
      fail: function () {
        console.log('cart不存在')
        Taro.setStorage({
          key: 'cart',
          data: {}
        })
      }
    })
    const updateHandle=()=> {
			if (Taro.canIUse('getUpdateManager')) {
			  const updateManager = Taro.getUpdateManager();
			  console.log(updateManager, 'updateManager');
			  updateManager.onCheckForUpdate(function (res) {
				console.log('onCheckForUpdate====', res);
				// 请求完新版本信息的回调
				if (res.hasUpdate) {
				  updateManager.onUpdateReady(function () {
					Taro.showModal({
					  title: '更新提示',
					  content: '新版本已经准备好，是否重启应用？',
            
					  success: function (result) {
						console.log('success====', result);
						if (result.confirm) {
						  updateManager.applyUpdate();
						}
					  }
					});
				  });
				  updateManager.onUpdateFailed(function () {
					Taro.showModal({
					  title: '已经有新版本了哟~',
					  content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
					});
				  });
				}
			  });
			}
		  }
		  updateHandle()
    
    //
  }
  
  /** 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖 */
});
function setupApp() {
  /** 引入静态资源 */
  setupAssets();

  /** 挂载store */
  setupStore(App);
}

setupApp();

export default App;
