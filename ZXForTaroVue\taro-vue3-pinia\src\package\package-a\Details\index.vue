<script setup lang="ts">
import Taro, { useRouter } from '@tarojs/taro';
import { reactive, ref, onMounted, Ref, onBeforeMount, onUnmounted } from 'vue';
const router = useRouter();
import { Search2 } from '@nutui/icons-vue-taro'
import { useAuthStore } from '@/store/index'
import { getProductByNumber, getProduct, getFindCart, updateFindCart } from '@/service/index'
import _ from 'lodash'; // 引入lodash库

interface obj {
	[idx: string]: any
}
const auth = useAuthStore()
definePageConfig({
	navigationBarTitleText: '产品详情',
	enablePullDownRefresh: true,
	onReachBottomDistance: 250,
});
// 移除页面级滚动监听，改用scroll-view内部滚动
const PageScrollTop = ref(false)

// 滚动监听函数
const handleScroll = _.throttle((e: any) => {
	const { scrollTop } = e.detail
	// 当滚动超过一定距离时显示返回顶部按钮
	if (scrollTop >= 300) {
		PageScrollTop.value = true
	} else {
		PageScrollTop.value = false
	}
}, 300)

// 触底加载函数
const handleScrollToLower = _.throttle(() => {
	if (!isLoading.value) {
		onScrollBottomPage()
	}
}, 500)

// 加载状态
const isLoading = ref(false)
const onScrollBottomPage = () => {
	// console.log(CurrentPage.value, DefaultTotalPage.value);

	if (CurrentPage.value < DefaultTotalPage.value) {
		CurrentPage.value = Number(CurrentPage.value) + 1
		loadinggetProductF(CurrentPage.value)
	} else {
		Taro.showToast({
			title: '全部加载完毕~',
			icon: 'success',
			duration: 2000
		})
	}

}
//
const CurrentPage = ref(1)
const DefaultTotalPage = ref(1)
const loadinggetProductF = async (page: number = 1) => {
	isLoading.value = true
	try {
		if (preloadData!.value) {
			const { error, success } = await getProduct({ name: preloadData!.value, page: Number(page).toString() })
			if (error === null) {
				console.log("数据加载结果", success);
				const { items, psize, cur_page } = success as { items: Array<any>, psize: number, cur_page: number }
				DefaultTotalPage.value = psize
				CurrentPage.value = cur_page
				let temp_value: any[] | null = []
				temp_value = items.map((item) => {
					return Object.assign(item, {
						count: 1,
						maxBuy: 10000,
						checkbox: false,
						design: '',
						leaveMsg: ''
					})
				})

				cartCommodities.value.push(...temp_value)
				temp_value = null
			}
		} else {
			await getProductDetailF(preloadData!.model)
		}
	} catch (error) {
		console.error('加载产品数据失败:', error)
		Taro.showToast({
			title: '加载失败，请重试',
			icon: 'error',
			duration: 2000
		})
	} finally {
		isLoading.value = false
	}
}
let clearBottom = ref()
onMounted(async () => {

	// console.log('112',toRaw(clearBottom.value.style._value));

	// eventCenter.on(getCurrentInstance().router!.onShow, async () => {
	await loadinggetProductF()
	// console.log('preloadData', preloadData!.model);




	await getCart()


	// console.log('preloadData', preloadData);

	// })


})
onUnmounted(() => {
	cartCommodities.value = []
	// eventCenter.off(getCurrentInstance().router!.onShow)
})
//获取产品数据
const getProductDetailF = async (model: string) => {
	const { error, success } = await getProduct({ name: model, page: Number(1).toString() })
	if (error === null) {

		const { items, psize, cur_page } = success as { items: Array<any>, psize: number, size: number, cur_page: number }
		// DefaultTotalPage.value = psize
		// CurrentPage.value = size
		searchListIndex.value = 1
		DQsearchListIndex.value = psize
		DefaultTotalPage.value = psize
		CurrentPage.value = cur_page
		console.log("数据加载结果~~", psize, DQsearchListIndex.value);
		let temp_value: any[] | null = []
		temp_value = items.map((item) => {
			return Object.assign(item, {
				count: 1,
				maxBuy: 10000,
				checkbox: false,
				design: '',
				leaveMsg: '',
				cartCommodities: false
			})
		})
		// console.log('temp_valuetemp_valuetemp_valuetemp_value', temp_value);


		cartCommodities.value.push(...temp_value)
		temp_value = null

	}
}

const preloadData: Record<any, any> | undefined = reactive(Taro.getCurrentInstance().preloadData!)



//购买数量
const countNumber = ref(1)





//前往购物车
const gotoshopping = () => {
	Taro.switchTab({
		url: '/pages/shoppingCart/index'
	})
}


//购物车页copy
const allSelected = ref(false)
const isRmCommodityDialogShow = ref(false)
interface IcartCommodities {
	id: number,
	code: string,
	name: string,
	spec: string,
	model: string,
	price_unit: number,
	image: string,
	count: number,
	maxBuy: number,
	checkbox: boolean,
	design: string | undefined,
	leaveMsg: string | undefined,
	fanhao_id: string | undefined,


}
// image: 'https://img01.71360.com/file/read/www2/M00/96/19/rBwBEmTHh_-AClvEAACUH1BBgu0725.png?w=600',
// id: 2, 
// name: 'ZX0151亚银双头灯', 
// price: 66.60, 
// count: 1, 
// maxBuy: 10000, 
// checkbox: false, 
// spec: '352*138*4mm/DC24-36V/吊装/15cm/上出线/40pcs', 
// design: undefined,leaveMsg:'' 


const cartCommodities: Ref<Array<IcartCommodities>> = ref(
	[
	])



// const getCartF = async (userId: string) => {
// 	const { error, success } = await getCart({ userId })
// 	if (error === null) {
// 		console.log(success);

// 		cartCommodities.value = [...success as Array<IcartCommodities>]
// 	}

// }

onBeforeMount(() => {
	// getCartF(Taro.getStorageSync('userInfo').userId)
	// cartCommodities.value.forEach(item => {
	// 	item['checkbox'] = false
	// })
})
const totalPrice = ref(0)
const swipeRefs = ref([])
const goMakeOrder = () => {
	visibleSubmit.value = true
}

let tempId = -1
const showRmDialog = (id: number) => {
	tempId = id
	isRmCommodityDialogShow.value = true
}

const countTotalPrice = () => {
	totalPrice.value = cartCommodities.value
		.filter(x => x.checkbox)
		.reduce((acc, curr) => acc + curr.price_unit * curr.count, 0);
}
const removeCommodity = (id: number) => {
	cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.id === id), 1)
}
const reCCard = () => {
	swipeRefs.value[cartCommodities.value.findIndex(item => item.id === tempId)]?.close()
}

const cCheckboxChange = (state: any, label: any) => {
	const l = cartCommodities.value.map(i => i.checkbox).filter(x => x).length
	allSelected.value = l === cartCommodities.value.length;
	countTotalPrice()
}
const minus = (id: number) => {
	cartCommodities.value.forEach(
		item => {
			if (id === item.id) {
				if (item.count !== 1) {
					item.count -= 1
				} else {
					Taro.showToast({
						title: '最少购买一个商品~',
						icon: 'none',
						duration: 2000
					})
				}
			}
		}
	)
	countTotalPrice()
}
const plus = (id: number) => {
	cartCommodities.value.forEach(
		item => {
			if (id === item.id) {
				if (item.count !== item.maxBuy) {
					item.count = Number(item.count) + 1
				} else {
					Taro.showToast({
						title: '当前最多仅能购买' + item.maxBuy + '份该商品~',
						icon: 'none',
						duration: 2000
					})
				}
			}
		}
	)
	countTotalPrice()
}
//全选事件
const checkAll = () => {
	if (allSelected.value) {
		cartCommodities.value.forEach(item => {
			item.checkbox = true
		})
	} else {
		cartCommodities.value.forEach(item => {
			item.checkbox = false
		})
	}
}
//控制按箱下单还是散件提交
const submitType = ref(0)
//每箱件数
const boxNum = ref(1)
//提交订单提示框
const visibleSubmit = ref(false)
const onSubmit = () => {
	const checkCartCommodities = cartCommodities.value
		.filter(x => x.checkbox)
	console.log('订单信息', checkCartCommodities);
	Taro.navigateTo({
		url: '/package/package-a/confirmOrder/index',

	})
	Taro.preload({ message: checkCartCommodities })
}
//滚动相关状态
const scrollTop = ref(0)

// 滚动到顶部处理
const handleScrollToUpper = () => {
	console.log('滚动到顶部')
	// 可以在这里添加下拉刷新逻辑
}

// 返回顶部功能
const backToTop = () => {
	scrollTop.value = 0
	// 使用nextTick确保DOM更新
	setTimeout(() => {
		scrollTop.value = 0
	}, 50)
}

// 优化滚动性能
const scrollIntoView = ref('')

//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
	console.log('子组件返回值:id', msg.id);
	console.log('子组件返回值:index', msg.index);
	ClickshowDialogItem = msg.id
	click()

}

const show = ref(false)
const click = () => {
	show.value = true
}

//搜索框相关事件
const searchValue = ref('')
const showSelect = ref(false)
const searchList: Ref<any[]> = ref([])
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)
const getProductByNumberF = async (item: Ref<string>) => {
	const { error, success } = await getProductByNumber({ name: item.value, per_page: '20' })
	// console.log('success                  ~~~', success);
	const { items, psize } = success as { items: Array<any>, psize: number }
	if (success && items) {
		searchList.value = items
		DQsearchListIndex.value = psize
	}
}
const onScrollBottom = async () => {
	console.log('触底了');
	// console.log('searchListIndex', searchListIndex.value);
	// console.log('DQsearchListIndex', DQsearchListIndex.value);


	if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
		searchListIndex.value++
		const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
		if (error === null) {
			const { items, psize } = success as { items: Array<any>, psize: number }
			if (success && items) {
				searchList.value = searchList.value.concat(items)
				DQsearchListIndex.value = psize
			}
		}
	} else {
		Taro.showToast({
			title: '加载完毕了~',
			icon: 'error',
			duration: 2000
		})
	}

}

const GetInputFocus = () => {
	if (searchValue.value.length >= 0) {
		show.value = true
		if (searchValue.value.length > 0) {
			getProductByNumberF(searchValue)
		} else {
			searchList.value = []
			DQsearchListIndex.value = 1
		}

	} else {
		show.value = false
	}

}
const clickItem = (item: any) => {

	// console.log("item----",item._relatedInfo.anchorRelatedText);
	const { name, id } = item
	if (ClickshowDialogItem) {
		cartCommodities.value.find(item => {
			if (item.id === ClickshowDialogItem) {
				item.design = name
				item.fanhao_id = id
			}
		})
		ClickshowDialogItem = null
		searchValue.value = ''
		console.log('cartCommodities', cartCommodities.value);
		show.value = false
		searchList.value = []


	} else {
		Taro.showToast({
			title: '操作失败',
			icon: 'error',
			duration: 2000
		})
	}

}

//获取购物车数据
const getCart = async () => {
	const { error, success } = await getFindCart()
	if (error === null && success) {
		console.log(success);
		NumberCarts.value = success.items.length

	}
}
//购物车数量
const NumberCarts = ref(0)
const showAnimate = ref(false)

const AddPurchase = async () => {
	let checkCartCommodities = cartCommodities.value
		.filter(x => x.checkbox)
	console.log(checkCartCommodities, '~~~');
	checkCartCommodities = checkCartCommodities.map(i =>
		[
			i.id,
			i.count
		]
	)
	if (checkCartCommodities.length > 0) {
		let resultStr = '[' + checkCartCommodities.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
		console.log(resultStr);

		const { error, success } = await updateFindCart({
			product_ids: resultStr
		})
		if (error === null) {
			Taro.showToast({
				title: '操作成功',
				icon: 'success',
				duration: 2000,
			})
			await getCart()
		} else {
			Taro.showToast({
				title: '操作失败',
				icon: 'error',
				duration: 2000
			})
		}
		console.log('~~~', error, success);


		console.log(checkCartCommodities);

	} else {
		Taro.showToast({
			title: '还未选购商品',
			icon: 'error',
			duration: 2000
		})
	}


}
const val = ref('')
const stateShow = reactive({
	showPreview: false,
	imgData: []
})
const longpressImage = (image: any) => {
	stateShow.showPreview = true;
	stateShow.imgData = [{ src: image }]

}
const findNewProduct = ref('')
const bindSearchValue = ref('')


</script>
<template>
	<basic-layout style="background-color: #f7f7f7;">
		<!-- 顶部搜索栏 -->
		<nut-sticky top="0" class="search-sticky">
			<div class="search-header">
				<nut-searchbar
					v-model="preloadData!.value"
					confirm-type="search"
					placeholder="请输入产品型号/名称/规格/料号"
					shape="round"
					input-background="#f5f5f5"
					class="custom-searchbar"
				>
					<template #rightout>
						<div class="search-btn" @click="() => {
							CurrentPage = 1,
								cartCommodities.length = 0,
								getProductDetailF(preloadData!.value)
						}">
							<i class="i-bx-search mr-1"></i>
							查询
						</div>
					</template>
					<template #rightin>
						<Search2 />
					</template>
				</nut-searchbar>
			</div>
		</nut-sticky>
		<div class="from-box">
			<!-- 返回顶部按钮 -->
			<nut-fixed-nav
				type="left"
				:position="{ top: '140px' }"
				v-show="PageScrollTop"
				@click="backToTop"
				class="back-to-top"
			>
				<template #btn>
					<div class="back-btn">
						<i class="i-bx-up-arrow-alt"></i>
						<span class="text">顶部</span>
					</div>
				</template>
			</nut-fixed-nav>

			<nut-action-sheet v-model:visible="show" class="myAction">
				<div style="height: 340px;">
					<nut-searchbar v-model="searchValue" shape="square" label="请输入楼层以搜索" input-background="#F0F0F0"
						@change="GetInputFocus" id="pop-target">
						<template #leftin>
							<Search2 />
						</template>
					</nut-searchbar>

					<div :class="{ toShowDiv: !showSelect }"
						style="width: 80%;height: 300px;background-color: #FFFFFF;background-color: #FFFFFF;position: absolute;left: 50%;transform:translateX(-50%); z-index:999;">
						<div>
							<div v-if="searchList.length > 0">
								<nut-list :list-data="searchList" :container-height="300"
									@scroll-bottom="onScrollBottom">
									<template #default="{ item }">
										<div class="list-item" @click="clickItem(item)">
											{{ item.name }}
										</div>
										<nut-divider :dashed="true" />
									</template>
								</nut-list>
							</div>
							<div v-else style="margin-top: 5%;color: #333333;font-weight: 900;">
								{{ '请输入相应正楼层编号后进行查询' }}
							</div>
						</div>

					</div>
				</div>
			</nut-action-sheet>
			<!-- 产品列表区域 -->
			<div class="product-section" v-if="true">
				<nut-dialog
					title="提交订单"
					content="确认提交订货信息并生成订单吗？"
					v-model:visible="visibleSubmit"
					@ok="onSubmit"
					class="custom-dialog"
				/>

				<div class="product-container" v-if="cartCommodities.length > 0">
					<nut-image-preview
						:show="stateShow.showPreview"
						:images="stateShow.imgData"
						@close="stateShow.showPreview = false"
					/>

					<scroll-view
						:scroll-y="true"
						class="product-scroll"
						@scrolltoupper="handleScrollToUpper"
						@scrolltolower="handleScrollToLower"
						@scroll="handleScroll"
						:scroll-top="scrollTop"
						:enable-back-to-top="true"
						:scroll-with-animation="true"
						:enhanced="true"
						:show-scrollbar="false"
						:fast-deceleration="false"
						:bounces="true"
						:scroll-anchoring="true"
					>
						<div class="product-list">
							<div
								v-for="(item, index) in cartCommodities"
								:key="item.id"
								class="product-item"
							>
								<div class="product-card-wrapper">
									<div class="product-checkbox">
										<nut-checkbox
											@change="cCheckboxChange"
											v-model="item.checkbox"
											icon-size="20"
											style="margin-right: 0 !important;"
										/>
									</div>

									<div class="product-image-section">
										<div class="image-container">
											<image
												:src="item.image"
												class="product-image"
												@longpress="longpressImage(item.image)"
											/>
											<div class="image-overlay" v-if="!item.image">
												<i class="i-bx-image-alt"></i>
											</div>
										</div>
									</div>
									<div class="product-info-section">
										<div class="product-title-area">
											<!-- 产品型号标签 -->
											<div class="model-tag">
												<i class="i-bx-crown"></i>
												<span>{{ item.model }}</span>
											</div>

											<!-- 产品名称标签 -->
											<div class="name-tag">
												{{ item.name.split('/')[0] }}
											</div>

											<!-- 产品子名称 -->
											<nut-tag
												color="rgba(18, 47, 56, 0.1)"
												text-color="#122F38"
												v-show="item.name.split('/')[1]"
												class="sub-name-tag"
											>
												{{ item.name.split('/')[1] }}
											</nut-tag>
										</div>

										<!-- 产品规格 -->
										<div class="product-spec">
											<nut-tag
												color="#f8f9fa"
												text-color="#666"
												class="spec-tag"
											>
												{{ item.spec }}
											</nut-tag>
										</div>

										<!-- 价格和编号区域 -->
										<div class="price-code-section">
											<div class="price-area">
												<span class="currency">¥</span>
												<span class="price">{{ item.price_unit }}</span>
												<span class="unit">/PCS</span>
											</div>
											<div class="code-area">
												<nut-tag color="#FF6B35" text-color="#fff" class="code-tag">
													{{ item.code }}
												</nut-tag>
											</div>
										</div>

										<!-- 隐藏的设计单元格 -->
										<DesignCell
											:design="item.design"
											:id="item.id"
											:index="index"
											@update:design="showDialog"
											class="noShow"
										/>
									</div>

									<!-- 数量控制器 -->
									<div class="quantity-control">
										<nut-input-number
											v-model="item.count"
											:min="1"
											:max="200000"
											input-width="55"
											@blur="countTotalPrice()"
											@add="() => plus(item.id)"
											@reduce="() => minus(item.id)"
											class="custom-input-number"
										/>
									</div>
								</div>
							</div>
						</div>

						<!-- 加载更多指示器 -->
						<div v-if="isLoading && cartCommodities.length > 0" class="loading-more">
							<div class="loading-spinner"></div>
							<span class="loading-text">加载中...</span>
						</div>

						<!-- 加载完成提示 -->
						<div v-if="!isLoading && CurrentPage >= DefaultTotalPage && cartCommodities.length > 0" class="load-complete">
							<span>已加载全部数据</span>
						</div>
					</scroll-view>
				</div>

				<!-- 暂无数据状态 -->
				<div class="empty-state" v-else>
					<nut-empty description="暂无数据" class="custom-empty" />
				</div>
			</div>

			<!-- 暂无产品数据 -->
			<div class="no-data" v-else>
				<nut-empty description="暂无该产品数据" class="custom-empty" />
			</div>

		</div>
		
		<!-- 底部信息栏 -->
		<div class="user-button-text">
			<span>{{ '技术支持 © 广东左向科技有限公司' }}</span>
		</div>


		<!-- 底部操作栏 -->
		<div class="bottom-action-bar">
			<div class="action-bar-content">
				<!-- 购物车图标 -->
				<div class="cart-section" @click="gotoshopping">
					<nut-badge :value="NumberCarts" class="cart-badge">
						<div class="cart-icon-wrapper">
							<i class="i-bx-cart-alt"></i>
							<span class="cart-text">购物车</span>
						</div>
					</nut-badge>
				</div>

				<!-- 价格显示 -->
				<div class="price-section">
					<span class="total-label">合计:</span>
					<span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
				</div>

				<!-- 操作按钮组 -->
				<div class="button-group">
					<nut-button
						class="add-cart-btn"
						@click="AddPurchase"
						:disabled="cartCommodities.filter(x => x.checkbox).length === 0"
					>
						<i class="i-bx-cart-add"></i>
						<span>加入购物车</span>
					</nut-button>

					<nut-button
						class="buy-now-btn"
						type="primary"
						@click="goMakeOrder"
						:disabled="(cartCommodities.length === 0) || (cartCommodities.filter(x => x.checkbox).length === 0)"
					>
						立即购买
					</nut-button>
				</div>
			</div>
		</div>
	</basic-layout>
</template>

<style lang="scss">

// 搜索栏样式
.search-sticky {
	z-index: 100;
	box-shadow: 0 2px 8px rgba(18, 47, 56, 0.08);
}

.search-header {
	background: #fff;
	padding: 8px 16px;

	.custom-searchbar {
		--nutui-searchbar-input-background: #f5f5f5;
		--nutui-searchbar-input-border-radius: 20px;
	}

	.search-btn {
		display: flex;
		align-items: center;
		color: #122F38;
		font-weight: 600;
		font-size: 14px;
		padding: 8px 12px;
		border-radius: 6px;
		background: rgba(18, 47, 56, 0.05);
		transition: all 0.2s;

		&:active {
			background: rgba(18, 47, 56, 0.1);
		}
	}
}

// 返回顶部按钮
.back-to-top {
	.back-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #122F38 0%, #1a4249 100%);
		color: #fff;
		border-radius: 8px;
		padding: 8px;
		box-shadow: 0 4px 12px rgba(18, 47, 56, 0.3);

		i {
			font-size: 16px;
			margin-bottom: 2px;
		}

		.text {
			font-size: 10px;
			font-weight: 500;
		}
	}
}

// 产品区域样式
.product-section {
	padding: 0 12px;
	margin-bottom: 100px;
}

.product-container {
	background: #fff;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 12px rgba(18, 47, 56, 0.06);
}

.product-scroll {
	height: calc(100vh - 180px);
	margin-bottom: 80px;
	// 优化滚动性能
	-webkit-overflow-scrolling: touch;
	// 启用硬件加速
	transform: translateZ(0);
	will-change: scroll-position;
	// 防止滚动穿透
	overscroll-behavior: contain;
	// 优化滚动条
	&::-webkit-scrollbar {
		width: 2px;
	}
	&::-webkit-scrollbar-track {
		background: transparent;
	}
	&::-webkit-scrollbar-thumb {
		background: rgba(18, 47, 56, 0.2);
		border-radius: 2px;
	}
}

.product-list {
	padding: 4px;
	// 优化渲染性能
	contain: layout style paint;
}

.product-item {
	margin-bottom: 8px;
	position: relative;
	// 优化重绘性能
	contain: layout style paint;
	// 启用硬件加速
	transform: translateZ(0);

	&:last-child {
		margin-bottom: 8px;
	}
}

.product-card-wrapper {
	display: flex;
	background: #fff;
	border-radius: 8px;
	padding: 12px;
	box-shadow: 0 1px 8px rgba(18, 47, 56, 0.06);
	border: 1px solid rgba(18, 47, 56, 0.05);
	transition: all 0.3s ease;
	position: relative;
	min-height: 120px;
	// 优化渲染性能
	contain: layout style paint;
	// 启用硬件加速
	transform: translateZ(0);
	// 优化重绘
	backface-visibility: hidden;

	&:hover {
		box-shadow: 0 2px 16px rgba(18, 47, 56, 0.1);
		border-color: rgba(18, 47, 56, 0.08);
		// 使用transform代替box-shadow变化以获得更好的性能
		transform: translateZ(0) translateY(-1px);
	}
}

.product-checkbox {
	display: flex;
	align-items: flex-start;
	margin-right: 8px;
	padding-top: 2px;
	flex-shrink: 0;
}

.product-image-section {
	margin-right: 12px;
	flex-shrink: 0;

	.image-container {
		position: relative;
		width: 80px;
		height: 80px;
		border-radius: 6px;
		overflow: hidden;
		background: #f8f9fa;

		.product-image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.image-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #f0f0f0;
			color: #ccc;

			i {
				font-size: 24px;
			}
		}
	}
}

.product-info-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6px;
	min-width: 0; // 防止flex子元素溢出
	// padding-right: 60px; // 为数量控制器留出空间
}

.product-title-area {
	display: flex;
	flex-direction: column;
	gap: 4px;

	.model-tag {
		display: inline-flex;
		align-items: center;
		gap: 3px;
		padding: 3px 6px;
		background: #122F38;
		color: #fff;
		border-radius: 3px;
		font-size: 11px;
		font-weight: 600;
		width: fit-content;
		max-width: 100%;

		i {
			font-size: 10px;
			color: #FFD700;
			flex-shrink: 0;
		}

		span {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	.name-tag {
		display: inline-flex;
		padding: 3px 6px;
		background: rgba(18, 47, 56, 0.1);
		color: #122F38;
		border: 1px solid rgba(18, 47, 56, 0.3);
		border-radius: 3px;
		font-size: 11px;
		font-weight: 500;
		width: fit-content;
		max-width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.sub-name-tag {
		width: fit-content;
		max-width: 100%;
		font-size: 10px;

		.nut-tag {
			max-width: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

.product-spec {
	margin: 2px 0;

	.spec-tag {
		font-size: 10px;
		line-height: 1.3;
		max-width: 100%;
		word-break: break-all;
		padding: 2px 4px;
		display: block;

		.nut-tag {
			max-width: 100%;
			word-wrap: break-word;
			white-space: normal;
			line-height: 1.3;
		}
	}
}

.price-code-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	margin-top: auto;
	gap: 8px;

	.price-area {
		display: flex;
		align-items: baseline;
		gap: 1px;
		flex-shrink: 0;

		.currency {
			font-size: 12px;
			color: #122F38;
			font-weight: 500;
		}

		.price {
			font-size: 16px;
			color: #122F38;
			font-weight: 700;
		}

		.unit {
			font-size: 9px;
			color: #FF6B35;
			font-weight: 600;
			margin-left: 2px;
		}
	}

	.code-area {
		flex-shrink: 0;

		.code-tag {
			font-size: 9px;
			font-weight: 600;
			font-family: 'Courier New', monospace;
			padding: 2px 4px;

			.nut-tag {
				max-width: 80px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}

.quantity-control {
	position: absolute;
	bottom: 12px;
	right: 12px;

	.custom-input-number {
		--nutui-inputnumber-button-width: 24px;
		--nutui-inputnumber-button-height: 24px;
		--nutui-inputnumber-input-height: 24px;
		--nutui-inputnumber-input-font-size: 12px;
		--nutui-inputnumber-input-font-color: #122F38;
		--nutui-inputnumber-input-background-color: #f5f5f5;
		--nutui-inputnumber-button-background-color: #122F38;
		--nutui-inputnumber-button-icon-color: #fff;
		--nutui-inputnumber-input-width: 40px;
	}
}

// 底部操作栏样式
.bottom-action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	border-top: 1px solid rgba(18, 47, 56, 0.1);
	box-shadow: 0 -2px 16px rgba(18, 47, 56, 0.08);
	z-index: 100;
	padding-bottom: env(safe-area-inset-bottom);
}

.action-bar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 12px;
	gap: 8px;
	min-height: 60px;
}

.cart-section {
	display: flex;
	align-items: center;
	cursor: pointer;
	flex-shrink: 0;

	.cart-badge {
		--nutui-badge-color: #FF6B35;
		--nutui-badge-background-color: #FF6B35;
		--nutui-badge-border-radius: 8px;
		--nutui-badge-font-size: 10px;
	}

	.cart-icon-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1px;
		padding: 2px 6px;

		i {
			font-size: 18px;
			color: #122F38;
		}

		.cart-text {
			font-size: 9px;
			color: #122F38;
			font-weight: 500;
		}
	}
}

.price-section {
	display: flex;
	align-items: baseline;
	gap: 3px;
	flex: 1;
	justify-content: center;

	.total-label {
		font-size: 12px;
		color: #666;
		font-weight: 500;
	}

	.total-price {
		font-size: 16px;
		color: #122F38;
		font-weight: 700;
	}
}

.button-group {
	display: flex;
	gap: 6px;
	flex-shrink: 0;

	.add-cart-btn {
		--nutui-button-default-background-color: rgba(18, 47, 56, 0.1);
		--nutui-button-default-border-color: #122F38;
		--nutui-button-default-color: #122F38;
		--nutui-button-default-padding: 6px 8px;
		border-radius: 4px;
		font-size: 11px;
		font-weight: 600;
		display: flex;
		align-items: center;
		gap: 3px;
		min-width: 70px;

		i {
			font-size: 12px;
		}

		span {
			white-space: nowrap;
		}

		&:disabled {
			opacity: 0.5;
		}
	}

	.buy-now-btn {
		--nutui-button-primary-background-color: #122F38;
		--nutui-button-primary-border-color: #122F38;
		--nutui-button-primary-padding: 6px 12px;
		border-radius: 4px;
		font-size: 12px;
		font-weight: 600;
		min-width: 80px;
		white-space: nowrap;

		&:disabled {
			--nutui-button-primary-background-color: #ccc;
			--nutui-button-primary-border-color: #ccc;
		}
	}
}

// 加载状态样式
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16px;
	gap: 8px;

	.loading-spinner {
		width: 16px;
		height: 16px;
		border: 2px solid #f3f3f3;
		border-top: 2px solid #122F38;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	.loading-text {
		font-size: 12px;
		color: #666;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.load-complete {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 12px;
	font-size: 12px;
	color: #999;
	border-top: 1px solid #f0f0f0;
	margin-top: 8px;
}

// 空状态样式
.empty-state, .no-data {
	padding: 40px 20px;
	text-align: center;

	.custom-empty {
		--nutui-empty-description-color: #666;
		--nutui-empty-description-font-size: 14px;
	}
}

// 用户按钮文本
.user-button-text {
	font-size: 12px;
	color: #A6A6A6;
	text-align: center;
	margin: 2rem auto;
}

// 列表项样式
.list-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin-bottom: 10px;
	height: 24px;
	font-size: 16px;
	color: #122F38;
	font-family: sans-serif;
	font-weight: 500;

	&:hover {
		background: rgba(18, 47, 56, 0.05);
		border-radius: 4px;
	}
}

// 自定义对话框
.custom-dialog {
	--nutui-dialog-header-font-weight: 600;
	--nutui-dialog-header-color: #122F38;
	--nutui-dialog-content-color: #666;
	--nutui-dialog-ok-color: #122F38;
}

// 页面背景
page {
	background-color: #f7f7f7 !important;
}

// 固定导航按钮
.nut-fixed-nav__btn {
	width: 60px !important;
	background: linear-gradient(135deg, #122F38 0%, #1a4249 100%) !important;
}

// 复选框样式
.nut-checkbox__label {
	box-shadow: 0 0 0 1px rgba(18, 47, 56, 0.1);
	border-radius: 4px;
}

// 隐藏元素
.noShow {
	visibility: hidden !important;
}

// 响应式设计
@media (max-width: 750px) {
	.product-section {
		padding: 0 8px;
	}

	.product-card-wrapper {
		padding: 10px;
		min-height: 100px;
	}

	.product-image-section {
		margin-right: 10px;

		.image-container {
			width: 70px;
			height: 70px;
		}
	}

	.product-info-section {
		padding-right: 50px;
		gap: 4px;
	}

	.product-title-area {
		gap: 3px;

		.model-tag, .name-tag {
			font-size: 10px;
			padding: 2px 4px;
		}
	}

	.price-code-section {
		.price-area {
			.price {
				font-size: 14px;
			}

			.currency {
				font-size: 11px;
			}

			.unit {
				font-size: 8px;
			}
		}

		.code-area .code-tag {
			font-size: 8px;
		}
	}

	.quantity-control {
		bottom: 10px;
		right: 10px;

		.custom-input-number {
			--nutui-inputnumber-button-width: 20px;
			--nutui-inputnumber-button-height: 20px;
			--nutui-inputnumber-input-height: 20px;
			--nutui-inputnumber-input-font-size: 11px;
			--nutui-inputnumber-input-width: 35px;
		}
	}

	.action-bar-content {
		padding: 6px 10px;
		gap: 6px;
		min-height: 50px;
	}

	.button-group {
		gap: 4px;

		.add-cart-btn {
			font-size: 10px;
			padding: 4px 6px;
			min-width: 60px;

			i {
				font-size: 11px;
			}
		}

		.buy-now-btn {
			font-size: 11px;
			padding: 4px 8px;
			min-width: 70px;
		}
	}

	.price-section {
		.total-label {
			font-size: 11px;
		}

		.total-price {
			font-size: 14px;
		}
	}
}

@media (max-width: 480px) {
	.product-section {
		padding: 0 6px;
	}

	.search-header {
		padding: 6px 10px;
	}

	.product-card-wrapper {
		padding: 8px;
		min-height: 90px;
	}

	.product-checkbox {
		margin-right: 6px;
	}

	.product-image-section {
		margin-right: 8px;

		.image-container {
			width: 60px;
			height: 60px;
		}
	}

	.product-info-section {
		padding-right: 45px;
		gap: 3px;
	}

	.product-title-area {
		gap: 2px;

		.model-tag, .name-tag {
			font-size: 9px;
			padding: 1px 3px;
		}
	}

	.product-spec .spec-tag {
		font-size: 9px;
		padding: 1px 3px;
	}

	.price-code-section {
		gap: 4px;

		.price-area {
			.price {
				font-size: 13px;
			}

			.currency {
				font-size: 10px;
			}

			.unit {
				font-size: 7px;
			}
		}

		.code-area .code-tag {
			font-size: 7px;

			.nut-tag {
				max-width: 60px;
			}
		}
	}

	.quantity-control {
		bottom: 8px;
		right: 8px;

		.custom-input-number {
			--nutui-inputnumber-button-width: 18px;
			--nutui-inputnumber-button-height: 18px;
			--nutui-inputnumber-input-height: 18px;
			--nutui-inputnumber-input-font-size: 10px;
			--nutui-inputnumber-input-width: 30px;
		}
	}

	.action-bar-content {
		padding: 5px 8px;
		gap: 4px;
		min-height: 45px;
	}

	.cart-section .cart-icon-wrapper {
		padding: 1px 4px;

		i {
			font-size: 16px;
		}

		.cart-text {
			font-size: 8px;
		}
	}

	.button-group {
		gap: 3px;

		.add-cart-btn {
			font-size: 9px;
			padding: 3px 5px;
			min-width: 50px;

			i {
				font-size: 10px;
			}
		}

		.buy-now-btn {
			font-size: 10px;
			padding: 3px 6px;
			min-width: 60px;
		}
	}

	.price-section {
		gap: 2px;

		.total-label {
			font-size: 10px;
		}

		.total-price {
			font-size: 13px;
		}
	}
}
</style>
