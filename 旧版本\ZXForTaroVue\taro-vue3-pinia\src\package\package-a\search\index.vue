<script setup>
import { onBeforeMount, ref } from "vue";
import { updateOrder, getProduct, getProductByNumber } from '@/service/index'
import Taro, { showToast, getCurrentPages } from '@tarojs/taro';
import { Search2, IconFont } from '@nutui/icons-vue-taro'
const val = ref('')
/** 设置页面属性 */
definePageConfig({
  navigationBarTitleText: '新增查询',
});
const CurrentPage = ref(1)
const DefaultTotalPage = ref(1)
const onSearch = async (again = false) => {
  if (again) {
    SearchList.value = []
  }
  Taro.showLoading({
    title: '加载中...',
    mask: true
  })
  const { error, success } = await getProduct({ name: val.value, page: CurrentPage.value })
  if (error === null) {
    DefaultTotalPage.value = success.psize
    CurrentPage.value = success.cur_page
    SearchList.value.push(...success.items)
    SearchList.value = SearchList.value.map(item => {
      item.product_qty = 1
      item.zx_line_notes = ''
      item.design = ''
      item.fanhao_id = ''
      item.icon = 'new'

      return item
    })
    // val.value = ''
  }
  Taro.hideLoading()
  console.log('SearchList', SearchList.value);

}
//查询结果列表
const SearchList = ref([])
//滚动加载
const scrollTop = ref(0)
const cCheckboxChange = (state, label) => {
  const l = SearchList.value.map(i => i.checkbox).filter(x => x).length
  allSelected.value = l === SearchList.value.length;
  countTotalPrice()


}
//子组件返回值(动作面板)
let ClickshowDialogItem = null
const showDialog = (msg) => {
  console.log('子组件返回值:id', msg.id);
  console.log('子组件返回值:index', msg.index);
  ClickshowDialogItem = msg.id
  click()

}
const show = ref(false)
const click = () => {
  show.value = true
}
const countTotalPrice = () => {
  totalPrice.value = SearchList.value
    .filter(x => x.checkbox)
    .reduce((acc, curr) => acc + curr.price_unit * curr.product_qty, 0);
}
const minus = (id) => {
  console.log('minus', id);

  SearchList.value.forEach(
    item => {
      if (id === item.id) {
        if (item.product_qty !== 1) {
          item.product_qty -= 1
        } else {
          Taro.showToast({
            title: '最少购买一个商品~',
            icon: 'none',
            duration: 2000
          })
        }
      }
    }
  )
  countTotalPrice()
}
const plus = (id) => {
  console.log('plus', id);
  SearchList.value.forEach(
    item => {
      if (id === item.id) {
        if (item.product_qty !== 999) {
          item.product_qty = Number(item.product_qty) + 1
        } else {
          Taro.showToast({
            title: '当前最多仅能购买' + 999 + '份该商品~',
            icon: 'none',
            duration: 2000
          })
        }
      }
    }
  )
  countTotalPrice()
}

//搜索框相关事件
const searchValue = ref('')
const showSelect = ref(false)
const searchFHList = ref([])
const searchFHListIndex = ref(1)
const DQsearchListIndex = ref(1)

const getProductByNumberF = async (item) => {
  const { error, success } = await getProductByNumber({ name: item, per_page: '20' })
  console.log('success                  ~~~', success);
  const { items, psize } = success
  if (success && items) {
    searchFHList.value = items
    DQsearchListIndex.value = psize
  }
}
const onScrollBottom = async () => {
  console.log('触底了');
  console.log('searchListIndex', searchFHListIndex.value);
  console.log('DQsearchListIndex', DQsearchListIndex.value);


  if (Number(searchFHListIndex.value) < Number(DQsearchListIndex.value)) {
    searchFHListIndex.value++
    const { error, success } = await getProductByNumber({ name: searchFHList.value, page: searchFHListIndex.value.toString(), per_page: '20' })
    if (error === null) {

      if (success && items) {
        searchFHList.value = searchFHList.value.concat(items)
        DQsearchListIndex.value = psize
      }
    }
  } else {
    Taro.showToast({
      title: '加载完毕了~',
      icon: 'error',
      duration: 2000
    })
  }

}
const GetInputFocus = () => {
  if (searchFHList.value.length >= 0) {
    show.value = true
    if (searchValue.value.length > 0) {
      getProductByNumberF(searchValue.value)
    } else {
      searchFHList.value = []
      DQsearchListIndex.value = 1
    }

  } else {
    show.value = false
  }

}
const clickItem = (item) => {

  // console.log("item----",item._relatedInfo.anchorRelatedText);
  const { name, id } = item
  if (ClickshowDialogItem) {
    SearchList.value.find(item => {
      if (item.id === ClickshowDialogItem) {
        item.design = name
        item.fanhao_id = id
      }
    })
    ClickshowDialogItem = null
    searchValue.value = ''
    console.log('cartCommodities', SearchList.value);
    show.value = false
    searchFHList.value = []


  } else {
    Taro.showToast({
      title: '操作失败',
      icon: 'error',
      duration: 2000
    })
  }

}
const allSelected = ref(false)
const totalPrice = ref(0)

//对话框
const onCancel = () => {
  console.log('event cancel');
};


const onOk = async () => {
  console.log('event ok');
  let checkList = SearchList.value.filter(i => i.checkbox === true)
  console.log('checkList', checkList);

  let designConfirm = checkList.find(el =>
    el.name.includes('特殊图案') && el.design.length <= 0
  )


  if (designConfirm) {
    Taro.showModal({
      title: '提示',
      content: '特殊图案产品需选择楼层图案后才可下单',
      success: function (res) {
      }
    })

  } else {
    checkList=checkList.map(item =>{
    return {
      product_id:item.id,
      product_qty:item.product_qty,
      price_unit:item.price_unit,
      zx_line_notes:item.zx_line_notes,
      fanhao_id:item.fanhao_id,
      
    }}
   )
    const { error, success } = await updateOrder({

      add_lines: JSON.stringify(checkList),

    }, {
      id: Taro.getCurrentInstance().preloadData.OrderId

    })
    if (error === null && success) {
      console.log('success---', success);
      Taro.showToast({
        title: '提交成功',
        success: () => {
          setTimeout(() => {
            let pages = Taro.getCurrentPages()
            let prevPage = pages[pages.length - 2]
            prevPage.setData({
              checkList
            })
            Taro.navigateBack({
              delta: 1
            })

          }, 2000);

        },
        duration: 2000
      })

    } else {
      Taro.showToast({
        title: error.message,
        icon: 'error',
        duration: 2000
      })
    }
  }


  // Taro.showToast({
  //   title: '暂存成功',
  //   icon: 'success'
  // })

  // setTimeout(() => {
  //   let pages = Taro.getCurrentPages()
  //   let prevPage = pages[pages.length - 2]
  //   prevPage.setData({
  //     checkList
  //   })
  //   Taro.navigateBack({
  //     delta: 1
  //   })

  // }, 2000);

}


const visible1 = ref(false);
const visible3 = ref(false);
Taro.useReachBottom(async () => {
  // console.log('触底了');
  onScrollBottomPage()

})
const onScrollBottomPage = () => {
  console.log(CurrentPage.value, DefaultTotalPage.value);

  if (CurrentPage.value < DefaultTotalPage.value) {
    CurrentPage.value = Number(CurrentPage.value) + 1
    onSearch()
  } else {
    Taro.showToast({
      title: '全部加载完毕~',
      icon: 'success',
      duration: 2000
    })
  }

}
Taro.usePageScroll((res) => {


  if (res.scrollTop >= 528) {
    // console.log('true');
    PageScrollTop.value = true
  } else {
    // console.log('false');
    PageScrollTop.value = false

  }
});
const PageScrollTop = ref(false)
const checkItem = (id) => {

  // console.log('item', id);
  let preloadValue = Taro.getCurrentInstance().preloadData.existingProductList
  let Exist = preloadValue.find(item =>
    item.id === id
  ) || null
  console.log(Exist, 'exist');

  if (Exist != null) {
    visible3.value = true

  }

}

</script>

<template>
  <nut-fixed-nav type="left" :position="{ top: '140px' }" v-show="PageScrollTop" @click="Taro.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })">
    <template #btn>
      <span class="text">{{ "返回顶部" }}</span>
    </template>
  </nut-fixed-nav>
  <nut-sticky top="0">
    <nut-searchbar v-model="val" confirm-type="search" placeholder="请输入产品型号/名称/规格/料号">
      <template #rightout>
        <div @click="onSearch(true)">查询</div>
      </template>
      <template #rightin>
        <Search2 />
      </template>
    </nut-searchbar>
  </nut-sticky>
  <nut-action-sheet v-model:visible="show" class="myAction">
    <div style="height: 340px;">
      <nut-searchbar v-model="searchValue" shape="square" label="请输楼层以搜索" input-background="#F0F0F0"
        @change="GetInputFocus" id="pop-target">
        <template #leftin>
          <Search2 />
        </template>
      </nut-searchbar>

      <div :class="{ toShowDiv: !showSelect }"
        style="width: 80%;height: 300px;background-color: #FFFFFF;background-color: #FFFFFF;position: absolute;left: 50%;transform:translateX(-50%); z-index:999;">
        <div>
          <div v-if="searchFHList.length > 0">
            <nut-list :list-data="searchFHList" :container-height="300" @scroll-bottom="onScrollBottom">
              <template #default="{ item }">
                <div class="list-item" @click="clickItem(item)">
                  {{ item.name }}
                </div>
                <nut-divider :dashed="true" />
              </template>
            </nut-list>
          </div>
          <div v-else style="margin-top: 5%;color: #333333;font-weight: 900;">
            {{ '请输入相应正楼层编号后进行查询' }}
          </div>
        </div>

      </div>
    </div>
  </nut-action-sheet>
  <card class="card">
    <nut-swipe-group lock style="width: 100vw" v-if="SearchList.length > 0">
      <!-- <nut-backtop height="90vh" style="margin-bottom: 60rpx;" :distance="30" :bottom="250">
        <template #content> -->

      <nut-swipe v-for="(item, index) in SearchList" ref="swipeRefs" style="margin-bottom: 10px;"
        :name="item.id.toString()" :key="item.id">
        <view class="my-boxShad" style="border-top-left-radius: 10px;border-bottom-left-radius: 10px;">
          <view style="display: flex; gap: 5px; width: 100vw;text-align: center;">
            <nut-checkbox @change="cCheckboxChange" v-model="item.checkbox" icon-size="20" style="margin-left: .625rem;"
              @click.once="checkItem(item.id)">
              <image :src="item.image" style="width: 70px; height: 70px"></image>
              <!-- <nut-tag plain type="primary">{{ item.name.split('/')[0] }}</nut-tag> -->
            </nut-checkbox>
            <view style="display: flex; flex-direction: column; gap: 8px;padding: 5% 5% 5% 0">
              <view
											style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;text-align: left;">

											<!-- <nut-tag color="#F7AA71" class="ml-3% mr-3%">{{ item.model }}</nut-tag> -->
											<div
												style="color:#DD5F73;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #DD5F73;margin-bottom: 1%;margin-right: 3%;">
												<i class="i-bx-crown"
													style="vertical-align: text-bottom;color: #DD5F73;font-weight: bold;"></i>
												{{ item.model }}
											</div>
                      <div
												style="color:#4D74FA;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #4D74FA;margin-bottom: 1%;">
												
													{{ item.name.split('/')[0] }}
											</div>
											<nut-tag color="#F7AA71" v-show="item.name.split('/')[1]">{{
												item.name.split('/')[1]
											}}</nut-tag>

										</view>
              <nut-tag color="#F2F2F2" text-color="#909090" class="text-ellipsis w-11.25rem break-all">{{
                item.spec
              }}</nut-tag>
              <view class="flex justify-between">
                <span>

                  <nut-price :price="item.price_unit" style="color: #F36409;font-weight: 900;"></nut-price>
                  <span class="unit color-#ED7976 pl-5px  font-900">/</span>
                  <span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
                </span>
                <nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }} </nut-tag>
              </view>
              <DesignCell :design="item.design" :id="item.id" :index="index" @update:design="showDialog"
                :name="item.name.split('/')[0]" :class="{ noShow: !item.name.split('/')[0].includes('特殊图案') }">
              </DesignCell>

            </view>
          </view>
          <div class="flex justify-start h40px pl-3%! ">
            <div class="line-height-40px">{{ '备注：' }}</div>
            <div class="w60%! pl-2px!">
              <nut-input v-model="item.zx_line_notes" :max-length="50" placeholder="去留言(下单后无法追加)" class="w60%! pl-2px!">
              <template #right>
                <i class="i-bx-edit-alt"></i>
              </template>
            </nut-input>
            </div>
          </div>
        </view>

        <view class="quantity-button-group">
          <!-- <IconFont @click="() => minus(item.id)" name="minus" class="button-cell button-minus">
              </IconFont>
              <view class="button-cell button-cell-middle ">{{ item.product_qty }}</view>
              <IconFont @click="() => plus(item.id)" class="button-cell button-plus" name="plus">
              </IconFont> -->
          <nut-input-number v-model="item.product_qty" :min="1" :max="200000" input-width="55" @blur="countTotalPrice()"
            @add="() => plus(item.id)" @reduce="() => minus(item.id)" />
        </view>

      </nut-swipe>

      <!-- </template>
      </nut-backtop> -->
    </nut-swipe-group>

    <nut-empty v-else image="empty" description="暂无查询结果"></nut-empty>
  </card>
  <div class="w-100% h-30px mt-5px"></div>
  <view class="bottom-card">

    <view style="display: flex; gap: 5px">
      <view style="font-weight: bold;">合计:
        <nut-price :price="totalPrice"></nut-price>
      </view>
    </view>
    <view style="margin-right: 10px">
      <nut-button class="right-button" type="primary" @click="visible1 = true" :disabled="(SearchList.length === 0) || (SearchList
        .filter(x => x.checkbox).length === 0)">{{ '新增订单' }}</nut-button>
    </view>
  </view>
  <nut-dialog title="新增订单" content="请确定是否新增相关产品到订单" v-model:visible="visible1" @cancel="onCancel" @ok="onOk" />

  <nut-dialog no-cancel-btn title="温馨提示" v-model:visible="visible3" ok-text="我知道了" content="您所勾选的产品在当前订单中已已存在，请确认核实订单？">
  </nut-dialog>




</template>

<style lang="scss">
.list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 10px;
  height: 24px;
  //   background-color: #919191;
  //   border-radius: 10px;
  //   color:#F8F8F8;
  //   font-weight: 900;
  font-size: 16px;
  color: #616161;
  font-family: sans-serif;
}

.left-button {
  border: none;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  background-color: #FF8E4C !important;
  color: #FFF !important;
}

.right-button {
  border: none;
  background-color: #F56171 !important;
  color: #FFF !important;
}

.test {
  padding: 12px 0 12px 20px;
  border-top: 1px solid #eee;
}

.my-boxShad {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}

.button-minus {
  // border: 1px solid #aba8a8;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  background-color: #F9FAFC;
}

.button-plus {
  // border: 1px solid #aba8a8;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  background-color: #F9FAFC;
}

.button-cell-middle {
  // border-top: 1px solid #aba8a8;
  // border-bottom: 1px solid #aba8a8;
  background-color: #F9FAFC;
  text-align: center;
  line-height: 17px;
  margin: 0 5%;
}

.card {
  margin: 0 0;
}

.button-cell {
  min-width: 25px;
  height: 17px;
}

.quantity-button-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 8px;
  right: 30px;
}

.bottom-card {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  position: fixed;
  margin-bottom: 0;
  bottom: var(--nut-tabbar-height, 100rpx);
  width: 100vw;
  align-items: center;
  padding: .625rem;
  border-top: 1px solid #dcdcdc;
}

.image-box {
  .nut-cell {
    padding-top: 0;
    padding-bottom: 0;
    margin: 0;
    border-radius: none;

  }
}

.nut-address-custom-buttom {
  width: 100%;
  height: 54px;
  padding: 6px 0px 0;
  border-top: 1px solid #f2f2f2;
}

.from-box {
  .nut-sku-stepper-count {
    padding-right: 1.25rem;
  }
}

.btn {
  width: 90%;
  height: 42px;
  line-height: 42px;
  margin: auto;
  text-align: center;
  background: linear-gradient(135deg, #fa2c19 0%, #fa6419 100%);
  border-radius: 21px;
  font-size: 15px;
  color: white;
}

.sku-operate-box {
  width: 100%;
  display: flex;
  padding: 8px 10px;
  box-sizing: border-box;
}

.sku-operate-item {
  flex: 1;
}

.sku-operate-item:first-child {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.sku-operate-item:last-child {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.bottom-card {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  width: 100vw;
  align-items: center;
  padding: .625rem;
  border-top: 1px solid #dcdcdc;
}

.content-desc {
  font-size: 1rem;
  color: #000;
}

page {
  background-color: #F7F7F7 !important;
}
.noShow {
	visibility: hidden !important;
}
</style>
