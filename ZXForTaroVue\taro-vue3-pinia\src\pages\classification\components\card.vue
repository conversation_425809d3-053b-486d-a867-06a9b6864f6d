<template>
    <div class="product-card">
      <!-- 产品图片 -->
      <div class="product-image-section">
        <div class="image-container">
          <image
            :src="state.imgUrl || '/static/images/default-product.png'"
            class="product-image"
            mode="aspectFit"
          />
        </div>
      </div>

      <!-- 产品信息 -->
      <div class="product-info-section">
        <div class="product-header">
          <div class="model-tag">
            <i class="i-bx-cube"></i>
            <span>{{ state.title }}</span>
          </div>
        </div>

        <div class="product-name">
          <span class="name-text">{{ state.shopDesc }}</span>
        </div>

        <div class="product-footer">
          <div class="product-tags">
            <div class="tag self-operated">
              <i class="i-bx-check-circle"></i>
              <span>自营</span>
            </div>
          </div>
          <div class="view-details">
            <i class="i-bx-right-arrow-alt"></i>
          </div>
        </div>
      </div>
    </div>
  </template>
  <script setup lang="ts">
  import {defineProps, onMounted, watchEffect} from 'vue';
  const props=defineProps({
    item:{
        type:Object,
        required:true
    }
  })
//   const emit=defineEmits(['click-card'])
//   const clickCard=()=>{
//     emit('click-card',)
//   }
  onMounted(()=>{

  })
 
  import { ref } from 'vue'
  const state = ref({
    imgUrl:'',
    title: '',
    shopDesc: '自营',
  })
  watchEffect(()=>{
    state.value.title=props.item.model,
    state.value.imgUrl=props.item.image
    state.value.shopDesc=props.item.name.split('/')[0]
  })
  </script>
  <style lang="scss">
  // 主色调变量
  $primary-color: #122F38;
  $primary-light: rgba(18, 47, 56, 0.1);
  $primary-dark: #0d252c;
  $accent-color: #FF6B35;
  $success-color: #4CAF50;
  $warning-color: #FF9800;
  $error-color: #F44336;
  $info-color: #2196F3;
  $text-primary: #122F38;
  $text-secondary: #666;
  $text-light: #999;
  $background-light: #f8f9fa;
  $border-color: rgba(18, 47, 56, 0.1);

  // 产品卡片
  .product-card {
    display: flex;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    // 产品图片区域
    .product-image-section {
      flex-shrink: 0;
      width: 100px;
      height: 100px;

      .image-container {
        width: 100%;
        height: 100%;
        background: $background-light;
        display: flex;
        align-items: center;
        justify-content: center;

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 8px;
        }
      }
    }

    // 产品信息区域
    .product-info-section {
      flex: 1;
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0;

      .product-header {
        margin-bottom: 8px;

        .model-tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          background: $primary-light;
          color: $primary-color;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;

          i {
            font-size: 14px;
          }
        }
      }

      .product-name {
        margin-bottom: 12px;

        .name-text {
          font-size: 14px;
          font-weight: 500;
          color: $text-primary;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .product-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .product-tags {
          display: flex;
          gap: 6px;

          .tag {
            display: inline-flex;
            align-items: center;
            gap: 3px;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;

            &.self-operated {
              background: rgba(76, 175, 80, 0.1);
              color: $success-color;

              i {
                font-size: 12px;
              }
            }
          }
        }

        .view-details {
          color: $text-light;

          i {
            font-size: 16px;
            transition: all 0.3s ease;
          }
        }
      }
    }

    // 悬停效果
    &:hover {
      .product-info-section {
        .product-footer {
          .view-details {
            color: $primary-color;

            i {
              transform: translateX(2px);
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 375px) {
    .product-card {
      border-radius: 8px;

      .product-image-section {
        width: 80px;
        height: 80px;

        .image-container {
          .product-image {
            width: 60px;
            height: 60px;
          }
        }
      }

      .product-info-section {
        padding: 10px 12px;

        .product-header {
          margin-bottom: 6px;

          .model-tag {
            font-size: 11px;
            padding: 3px 6px;

            i {
              font-size: 12px;
            }
          }
        }

        .product-name {
          margin-bottom: 8px;

          .name-text {
            font-size: 13px;
          }
        }

        .product-footer {
          .product-tags {
            .tag {
              font-size: 9px;
              padding: 2px 4px;

              &.self-operated {
                i {
                  font-size: 10px;
                }
              }
            }
          }

          .view-details {
            i {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  </style>
  