<script setup>
import { onBeforeMount, ref } from "vue";
// props
const props = defineProps({
    values: {
        type: Array,
        required: true
    },
    type: String
});

// 取消订单事件
const cancelOrder = (order) => {
    console.log(order);
    visibleCancelOrder.value = true;
    cancelOrderId.value = order


};
const oncancelOrderOk = () => {
    console.log('取消订单,id为:', cancelOrderId.value);
    //取消订单接口
}
const visibleCancelOrder = ref(false);
const cancelOrderId = ref(0)
// mounted
onBeforeMount(() => {
    console.log(props.values);
});

</script>

<template>
    <!-- <nut-dialog title="取消订单" content="确认取消订单吗？" v-model:visible="visibleCancelOrder" @cancel="onCancel"
        @ok="oncancelOrderOk" /> -->
        {{ values }}
    <view v-if="values.length === 0">
        <nut-empty image="empty" description="暂时没有相关的订单，快去下单吧！"></nut-empty>
    </view>
    <scroll-view v-else :scroll-y="true" :scroll-x="false" class="w-full scroll-h">
        <view v-for="order in values" class="bg-white mb-2  p-2 rounded shadow">

            <view className="flex flex-col gap-2 ">
                <view v-for="commodity in order.commodities">
                    <view className="flex flex-row justify-between align-top">
                        <view className="flex min-w-0 gap-1">
                            <image :src="commodity.image" className="w-96 h-96 rounded"></image>
                            <view className="  font-light " style="width: 60vw;">
                                <!-- <view className="line-clamp-2 overflow-ellipsis ">{{ commodity.unit }} | {{
                                    commodity.title }}</view> -->
                                <view class="flex flex-wrap p-1">
                                    <view class="comodity-title"> {{ commodity.title }}</view>
                                    <view class="comodity-unit">{{ commodity.unit }}</view>

                                </view>
                            </view>
                        </view>
                        <view>
                            <view className="font-bold text-sm" style="color: #F36A12;font-weight: bold;"><span style="font-size: 12px;">￥</span>{{ commodity.price }}</view>
                            <view className="text-sm text-gray-400 text-right">x{{ commodity.count }}</view>
                        </view>
                    </view>
                </view>
                <view
                    className="border-y border-x-0 border-neutral-300 border-dashed text-neutral-500 text-sm flex flex-justify-between">
                    <span class="text-left">总计{{ order.commodities.length }}件商品 </span>
                    <span className="text-black inline font-bold text-right">付款:<span style="font-size:12px;color:#F36A12">￥</span><span style="color:#F36A12">{{ order.actualPay
                            }}</span></span>
                </view>
                <view class="flex flex-row justify-end gap-2 mr-1">
                    <nut-button v-if="order.status === 1" class="rounded" plain type="info" shape="square"
                        size="mini" disabled>待发货</nut-button>
                    <!-- <nut-button v-if="order.status === 1" class="rounded" plain type="warning" shape="square"
                        size="mini" @click="cancelOrder(order.id)">取消订单</nut-button> -->
                    <nut-button v-if="order.status === 3" class="rounded" plain type="primary" shape="square"
                        size="mini" disabled>已完成</nut-button>
                </view>
            </view>
        </view>
    </scroll-view>
</template>

<style>
.scroll-h {
    height: 89vh;
}
.comodity-title {
    font-size: 1rem
}
.comodity-unit{
    font-size:0.75rem;
    color:#979797;
}
</style>
