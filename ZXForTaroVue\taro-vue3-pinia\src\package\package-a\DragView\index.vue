<template class="flex flex-col justify-between">
  <div class="p-5px">
    <div class="mt-5px mb-5px ml-7px font-size-16px font-600">{{ '包含关键词' }}</div>
    <view class="drag-grid m-5px">

      <transition-group name="drag">

        <view v-for="(item, index) in list1" :key="index" class="drag-item flex flex-col justify-center items-center "
          @touchstart="handleTouchStart(index, $event)" @touchmove="handleTouchMove(index, $event)"
          @touchend="handleTouchEnd(index, $event)" :data-index="index">

          <!-- {{ item.text }} -->
          <view class="w-75px h-75px m-3px" @longpress="longpressImage(index, item.id)"
            style="display: flex;flex-direction: column;justify-content: center;align-items: center;box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;">
            <image :src="item.href" class="w-40px h-40px" />
            <div style="color: #757575;font-family: serif;text-align: center;">{{ item.text }}</div>
          </view>



          <div v-if="!hidden && currentIndex == index" class="flex flex-col justify-center items-center"
            style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; opacity: 0.8" :style="{
              transform: `translate(${positionsX[index] || 0}px, ${positionsY[index] || 0}px)`,
              zIndex: currentIndex === index ? 1000 : 1,
            }">

            <!-- {{ item.text }}-->
            <!-- <span style="color: #757575;font-family: serif;">{{ item.text }}</span> -->
            <image :src="item.href" class="w-40px h-40px" />



          </div>
        </view>
      </transition-group>

      <view class="w-69px h-69px m-3px clickPng" v-show="list1.length < 8" @click="noTitleClick"
        style="display: flex;flex-direction: column;justify-content: center;align-items: center;filter: brightness(96%);border:3px dashed #DDD;border-spacing: 2px;">
        <i class="i-bx-plus-circle font-size-30px color-#DDDDDD"></i>

      </view>
    </view>
  </div>
  <view class="text-end">
    <nut-button color="#FF6765" class="mt-5%! mr-5%! mb-5%! font-600" style="border-radius: 5px !important;"
      shape="square" @click="putHomeCategoryF()">{{ '保存更改' }}</nut-button>
  </view>
  <nut-dialog v-model:visible="visible2" @cancel="onCancel" @ok="onOk">
    <template #default>
      <nut-input v-model="inputValue" placeholder="请输入自定义关键词" :max-length="10" show-word-limit />
    </template>

  </nut-dialog>
  <nut-dialog title="温馨提示" content="是否确认删除" v-model:visible="visible3" @ok="ontipsOk" />

</template>

<script>
import Taro, { eventCenter, getCurrentInstance, getCurrentPages } from '@tarojs/taro';
import { putHomeCategory, createHomeCategory, deleteHomeCategory, getUserDefinedClass } from '@/service/api'
import { toRaw } from 'vue';
import { type } from 'os';
/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '编辑常用关键词',
	enableShareAppMessage: true
});
const instance = getCurrentInstance()
export default {
  name: 'DraggableGrid',

  data() {
    return {


      imageWitdh: 0,

      x: 0, // movable-view的坐标

      y: 0,

      areaHeight: 0, // movable-area的高度

      hidden: true, // movable-view是否隐藏

      currentImg: '', // movable-view的图片地址

      currentIndex: 0, // 要改变顺序的图片的下标

      pointsArr: [], // 每张图片的坐标

      flag: true, // 是否是长按

      scrollTop: 0, // 滚动条距离顶部的距离

      startX: 0,

      moveX: 0,

      startY: 0,

      moveY: 0,

      dragging: null,

      positionsX: new Array(9).fill(0),

      positionsY: new Array(9).fill(0),

      visible2: false,

      visible3: false,

      inputValue: '',

      nowId: '',

      clickNowValue: '',

      list1: []
    }

  },
  props: {


  },

  mounted() {
    if (instance && instance.router && instance.router.onShow) {
      eventCenter.on(instance.router.onShow, this.onShowPage)
    } else {
      // 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
      console.error('无法获取router.onShow事件处理函数')
      Taro.showToast({
        title: '无法获取事件',
        icon: 'none',
        duration: 2000
      })
    }
  },
  unmounted() {
    if (instance && instance.router && instance.router.onShow) {
      eventCenter.off(instance.router.onShow, this.onShowPage)
    } else {
      // 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
      console.error('无法获取router.onShow事件处理函数')
      Taro.showToast({
        title: '无法获取事件',
        icon: 'none',
        duration: 2000
      })
    }
  },

  methods: {
    handleTouchStart(index, e) {
      this._handleComputedPoints()

      this.currentImg = e.currentTarget.dataset.url

      this.currentIndex = e.currentTarget.dataset.index

      this.hidden = false

      this.flag = true

      this.x = e.currentTarget.offsetLeft

      this.y = e.currentTarget.offsetTop

      this.startX = e.touches[0].clientX

      this.startY = e.touches[0].clientY

    },

    _handleComputedPoints(e) {
      let that = this

      var query = Taro.createSelectorQuery()

      var nodesRef = query.selectAll('.drag-item')

      nodesRef

        .fields(

          {
            dataset: true,

            rect: true,

          },

          (result) => {
            that.pointsArr = result

            that.pointsArr.forEach((item, index) => {
              item.dataset.index = index

            })

          }

        )

        .exec()

    },

    handleTouchEnd(index, e) {
      let x = e.changedTouches[0].pageX

      let y = e.changedTouches[0].pageY - this.scrollTop

      const pointsArr = this.pointsArr

      let data = this.list1

      for (var j = 0; j < pointsArr.length; j++) {
        const item = pointsArr[j]

        if (x > item.left && x < item.right && y > item.top && y < item.bottom) {
          const endIndex = item.dataset.index

          const beginIndex = this.currentIndex

          //临时保存移动的目标数据

          let temp = data[beginIndex]

          //将移动目标的下标值替换为被移动目标的下标值

          data[beginIndex] = data[endIndex]

          //将被移动目标的下标值替换为beginIndex

          data[endIndex] = temp

        }

      }

      this.images = data

      this.hidden = true

      this.flag = false

      this.currentImg = ''

      this.positionsX[index] = 0

      this.positionsY[index] = 0

    },

    handleTouchMove(index, e) {
      let x = e.touches[0].pageX

      let y = e.touches[0].pageY

      // 首先先获得当前image-choose-container距离顶部的距离

      let that = this

      Taro.createSelectorQuery()

        .selectAll('.drag-grid')

        .boundingClientRect(function (rect) {
          let top = rect[0].top

          y = y - that.scrollTop - top

          that.x = x

          that.y = y

        })

        .exec()

      const touchX = e.touches[0].clientX

      this.moveX = touchX - this.startX

      this.positionsX[index] = this.moveX

      const touchY = e.touches[0].clientY

      this.moveY = touchY - this.startY

      this.positionsY[index] = this.moveY

    },
    async putHomeCategoryF() {


      toRaw(this.list1).map((item, index) => { item.sort = parseInt(index + 1) })

      console.log(toRaw(this.list1));

      const { error, success } = await putHomeCategory({ home_category: JSON.stringify(toRaw(this.list1)) })
      if (error == null) {
        await Taro.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success:function(){
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              })
            }, 2000)
          }
        })
        

      } else {
        Taro.showToast({
          title: '保存失败',
          icon: 'none',
          duration: 2000
        })
      }

    },
    onCancel() {

    },
    async onOk() {
      this.list1.push({ id: toRaw(this.list1)[toRaw(this.list1).length - 1].id + 1, text: this.inputValue, sort: toRaw(this.list1).length + 1 })
      console.log(toRaw(this.list1));
      const { error, success } = await createHomeCategory({ text: this.inputValue, sort: toRaw(this.list1).length + 1 })
      if (error == null) {
        await Taro.showToast({
          title: '添加成功',
          icon: 'success',
          duration: 2000
        })
        this.inputValue = ''
      } else {
        await Taro.showToast({
          title: '添加失败',
          icon: 'none',
          duration: 2000
        })
      }

    },
    noTitleClick() {
      this.visible2 = true
    },
    async longpressImage(id, value) {
      this.visible3 = true
      this.nowId = id
      this.clickNowValue = value

    },
    async ontipsOk() {

      console.log(this.nowId);

      // console.log(this.list1.filter(i=>{return i.id!==this.nowId}));
      // toRaw(this.list1) = this.list1.filter(i=>{return i.id!==this.nowId})
      this.list1.splice(this.nowId, 1)

      toRaw(this.list1).map((item, index) => { item.sort = parseInt(index + 1) })
      const { error, success } = await deleteHomeCategory({ id: this.clickNowValue })
      if (error == null) {
        await Taro.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 2000
        })
      } else {
        await Taro.showToast({
          title: '删除失败',
          icon: 'none',
          duration: 2000
        })
      }
      // console.log(toRaw(this.list1));



    },
    async onShowPage() {
      //用户是否登录
      if (Taro.getStorageSync('token') !== '') {
        const { error, success } = await getUserDefinedClass()
        console.log(error, 'error');
        console.log(success, 'success');


        if (error === null) {
            this.list1=success.items.sort((a,b)=>a.sort-b.sort)
            Taro.showToast({
              title: '获取成功',
              icon: 'success',
              duration: 2000
            })
        }else{
          Taro.showToast({
            title: '获取失败',
            icon: 'none',
            duration: 2000
          })
        }

      }
    }


  },

}

</script>

<style module>
.drag-grid {
  display: grid;

  grid-template-columns: repeat(4, 1fr);


}

.drag-move {
  transition: transform 0.3s;

}

.drag-item {
  cursor: move;

  touch-action: none;

  user-select: none;
  background-color: #FFF;

  /* padding: 10px; */


  transition: transform 0.2s ease;

  position: relative;

}

.clickPng:hover,
.clickPng:active {
  filter: brightness(86%);
}
</style>