<script setup>
import { onBeforeMount, ref,onMounted,onUnmounted } from "vue";
import { getOrder } from '@/service/index'
import Taro, { showToast, getCurrentPages,eventCenter, getCurrentInstance } from '@tarojs/taro';
import { Search2, IconFont } from '@nutui/icons-vue-taro'
import Orders from "@/pages/orders/orders.vue";
const val = ref('')
/** 设置页面属性 */
definePageConfig({
  navigationBarTitleText: '查询历史订单',
});
onMounted(() => {
    
    eventCenter.on(getCurrentInstance().router.onShow, () => {
      let preloadMessage = Taro.getCurrentInstance().preloadData.value
      console.log(preloadMessage) 
      if (preloadMessage) {
        getOrderF(10,preloadMessage)
        
      }

    })
})
onUnmounted(() => {
    eventCenter.off(getCurrentInstance().router.onShow)
})
//当前页码
const orderPage = ref(1)
//总分页数
const Psize=ref(1)
//每次拿到的订单数据
const ordersList = ref([

])
const orders=ref([

])
const getOrderF = async (per_page=20,name) => {
    Taro.showLoading({
        title: '加载中'
    })
    if (orderPage.value<=Psize.value && orders.value.length % per_page==0) {
        console.log(orders.value.length,per_page);
        
        const {error,success}=await getOrder({name})
        console.log("----------",error);
        // console.log("==========",success.items);
       if(error==null){
        orders.value.push(...success.items)
        Psize.value=success.psize
        orderPage.value<success.psize?orderPage.value=success.cur_page+1:orderPage.value=success.cur_page
        Taro.hideLoading()

        console.log(orders.value,Psize.value);
        
       }else{
        Taro.showToast({
            title: error,
            icon: 'error',
            duration: 2000
        })
       }        
    } else {
        Taro.hideLoading()
        Taro.showToast({
            title: '没有更多订单了',
            icon: 'none',
            duration: 2000
        })
    }
}
const onSearch = async () => {
  orders.value=[]
  Psize.value=1
  orderPage.value=1
  getOrderF(20,val.value)
}
//查询结果列表
const SearchList = ref('')
//滚动加载
const scrollTop = ref(0)
//子组件返回值(动作面板)
let ClickshowDialogItem = null
const showDialog = (msg) => {
  console.log('子组件返回值:id', msg.id);
  console.log('子组件返回值:index', msg.index);
  ClickshowDialogItem = msg.id
  click()

}
const show = ref(false)
const click = () => {
  show.value = true
}
//搜索框相关事件
const searchValue = ref('')
const showSelect = ref(false)
const searchFHList = ref([])
const searchFHListIndex = ref(1)
const DQsearchListIndex = ref(1)

const onScrollBottom = async () => {
  console.log('触底了');
  // await getOrderF(10,Taro.getCurrentInstance().preloadData.value)
  

}
Taro.useReachBottom(async() => {
  onScrollBottom()
})

const visible1 = ref(false);


</script>

<template>
  <nut-searchbar v-model="val" confirm-type="search" placeholder="请输入订单号" :bottom="40">
    <template #rightout>
      <div @click="onSearch">查询</div>
    </template>
    <template #rightin>
      <Search2 />
    </template>
  </nut-searchbar>
  <card class="card p-0!">
    <div lock style="width: 100vw" v-if="orders.length > 0">
      <nut-backtop height="100vh"  :distance="30" >
        <template #content>
         
          
             
             <orders :values="orders" ></orders>
       

        </template>
      </nut-backtop>
    </div>

    <nut-empty v-else image="empty" description="暂无查询结果"></nut-empty>
  </card>






</template>

<style lang="scss">
.list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 10px;
  height: 24px;
  //   background-color: #919191;
  //   border-radius: 10px;
  //   color:#F8F8F8;
  //   font-weight: 900;
  font-size: 16px;
  color: #616161;
  font-family: sans-serif;
}

.left-button {
  border: none;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  background-color: #FF8E4C !important;
  color: #FFF !important;
}

.right-button {
  border: none;
  background-color: #F56171 !important;
  color: #FFF !important;
}

.test {
  padding: 12px 0 12px 20px;
  border-top: 1px solid #eee;
}

.my-boxShad {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}

.button-minus {
  // border: 1px solid #aba8a8;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  background-color: #F9FAFC;
}

.button-plus {
  // border: 1px solid #aba8a8;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  background-color: #F9FAFC;
}

.button-cell-middle {
  // border-top: 1px solid #aba8a8;
  // border-bottom: 1px solid #aba8a8;
  background-color: #F9FAFC;
  text-align: center;
  line-height: 17px;
  margin: 0 5%;
}

.card {
  margin: 0 0;
}

.button-cell {
  min-width: 25px;
  height: 17px;
}

.quantity-button-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 8px;
  right: 30px;
}

.bottom-card {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  position: fixed;
  margin-bottom: 0;
  bottom: var(--nut-tabbar-height, 100rpx);
  width: 100vw;
  align-items: center;
  padding: .625rem;
  border-top: 1px solid #dcdcdc;
}

.image-box {
  .nut-cell {
    padding-top: 0;
    padding-bottom: 0;
    margin: 0;
    border-radius: none;

  }
}

.nut-address-custom-buttom {
  width: 100%;
  height: 54px;
  padding: 6px 0px 0;
  border-top: 1px solid #f2f2f2;
}

.from-box {
  .nut-sku-stepper-count {
    padding-right: 1.25rem;
  }
}

.btn {
  width: 90%;
  height: 42px;
  line-height: 42px;
  margin: auto;
  text-align: center;
  background: linear-gradient(135deg, #fa2c19 0%, #fa6419 100%);
  border-radius: 21px;
  font-size: 15px;
  color: white;
}

.sku-operate-box {
  width: 100%;
  display: flex;
  padding: 8px 10px;
  box-sizing: border-box;
}

.sku-operate-item {
  flex: 1;
}

.sku-operate-item:first-child {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.sku-operate-item:last-child {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.bottom-card {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  width: 100vw;
  align-items: center;
  padding: .625rem;
  border-top: 1px solid #dcdcdc;
}

.content-desc {
  font-size: 1rem;
  color: #000;
}

page {
  background-color: #F7F7F7 !important;
}
</style>
