{"compilerOptions": {"allowJs": true, "baseUrl": ".", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "paths": {"~/*": ["./*"], "@/*": ["./src/*"]}, "types": ["node"]}, "exclude": ["node_modules", "dist"]}