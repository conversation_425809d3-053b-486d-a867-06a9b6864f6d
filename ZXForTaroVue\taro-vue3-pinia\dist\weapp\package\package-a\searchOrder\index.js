"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[466],{370:function(e,t,n){var r=n(1065),o=n(3221),a=n(7011),u={__name:"index",props:["style"],setup:function(e){var t=e;return function(e,n){return(0,o.wg)(),(0,o.iD)("view",{class:"sdf-card",style:(0,a.j5)(t.style)},[(0,o.WI)(e.$slots,"default")],4)}}};const i=u;var c=i,l=n(1845),s=(n(3505),n(3191)),p=n(3091),v=n(6821),f=n(2e3),h=n(1939),d=n(5969),w=n(6249),g=Object.defineProperty,m=Object.defineProperties,y=Object.getOwnPropertyDescriptors,b=Object.getOwnPropertySymbols,k=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,Z=function(e,t,n){return t in e?g(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},_=function(e,t){for(var n in t||(t={}))k.call(t,n)&&Z(e,n,t[n]);if(b){var r,o=(0,p.Z)(b(t));try{for(o.s();!(r=o.n()).done;){n=r.value;x.call(t,n)&&Z(e,n,t[n])}}catch(e){o.e(e)}finally{o.f()}}return e},H=function(e,t){return m(e,y(t))},O=(0,o.aZ)(H(_({},{name:"NutBacktop"}),{__name:"backtop.taro",props:{height:{default:"100vh"},bottom:{default:20},right:{default:10},distance:{default:200},zIndex:{default:10}},emits:["click"],setup:function(e,t){var n=t.emit,r=e,u=n,i=(0,v.iH)(!1),c=(0,v.iH)(1),l=function(e){c.value=2,i.value=e.detail.scrollTop>=r.distance},p=(0,o.Fl)((function(){var e="nut-backtop";return(0,s.Z)((0,s.Z)({},e,!0),"show",i.value)})),w=(0,o.Fl)((function(){return{right:"".concat(r.right,"px"),bottom:"".concat(r.bottom,"px"),zIndex:r.zIndex}})),g=function(e){c.value=1,u("click",e)};return function(e,t){return(0,o.wg)(),(0,o.iD)("view",null,[(0,o.Wm)(h._,{"scroll-y":!0,style:(0,a.j5)({height:e.height}),"scroll-top":c.value,"scroll-with-animation":"true",onScroll:l},{default:(0,o.w5)((function(){return[(0,o.WI)(e.$slots,"content")]})),_:3},8,["style","scroll-top"]),(0,o.Uk)(),(0,o._)("view",{class:(0,a.C_)(p.value),style:(0,a.j5)(w.value),onClick:(0,f.iM)(g,["stop"])},[(0,o.WI)(e.$slots,"icon",{},(function(){return[(0,o.Wm)((0,v.SU)(d.e4),{width:"19px",height:"19px",class:"nut-backtop-main"})]}))],6)])}}}));(0,w.w)(O);n(3939);var j=n(3193),I=(n(9157),n(2419)),W=n(8140),C=n(2810),P=n(4733),D=n(1959),S=n.n(D),z=n(2740),T={key:0,lock:"",style:{width:"100vw"}},L={__name:"index",setup:function(e){var t=(0,v.iH)("");(0,o.bv)((function(){D.eventCenter.on((0,D.getCurrentInstance)().router.onShow,(function(){var e=S().getCurrentInstance().preloadData.value;console.log(e),e&&u(10,e)}))})),(0,o.Ah)((function(){D.eventCenter.off((0,D.getCurrentInstance)().router.onShow)}));var n=(0,v.iH)(1),r=(0,v.iH)(1),a=((0,v.iH)([]),(0,v.iH)([])),u=function(){var e=(0,C.Z)((0,I.Z)().mark((function e(){var t,o,u,i,c,l,s=arguments;return(0,I.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:20,o=s.length>1?s[1]:void 0,S().showLoading({title:"\u52a0\u8f7d\u4e2d"}),!(n.value<=r.value&&a.value.length%t==0)){e.next=14;break}return console.log(a.value.length,t),e.next=7,(0,P.co)({name:o});case 7:u=e.sent,i=u.error,c=u.success,console.log("----------",i),null==i?((l=a.value).push.apply(l,(0,W.Z)(c.items)),r.value=c.psize,n.value<c.psize?n.value=c.cur_page+1:n.value=c.cur_page,S().hideLoading(),console.log(a.value,r.value)):S().showToast({title:i,icon:"error",duration:2e3}),e.next=16;break;case 14:S().hideLoading(),S().showToast({title:"\u6ca1\u6709\u66f4\u591a\u8ba2\u5355\u4e86",icon:"none",duration:2e3});case 16:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),i=function(){var e=(0,C.Z)((0,I.Z)().mark((function e(){return(0,I.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.value=[],r.value=1,n.value=1,u(20,t.value);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),s=((0,v.iH)(""),(0,v.iH)(0),(0,v.iH)(!1),(0,v.iH)(""),(0,v.iH)(!1),(0,v.iH)([]),(0,v.iH)(1),(0,v.iH)(1),function(){var e=(0,C.Z)((0,I.Z)().mark((function e(){return(0,I.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("\u89e6\u5e95\u4e86");case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}());S().useReachBottom((0,C.Z)((0,I.Z)().mark((function e(){return(0,I.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:s();case 1:case"end":return e.stop()}}),e)}))));(0,v.iH)(!1);return function(e,n){var r=j.Z,u=O,s=l.Z,p=c;return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(r,{modelValue:t.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return t.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u8ba2\u5355\u53f7",bottom:40},{rightout:(0,o.w5)((function(){return[(0,o._)("div",{onClick:i},"\u67e5\u8be2")]})),rightin:(0,o.w5)((function(){return[(0,o.Wm)((0,v.SU)(d.LP))]})),_:1},8,["modelValue"]),(0,o.Wm)(p,{class:"card p-0_el_"},{default:(0,o.w5)((function(){return[a.value.length>0?((0,o.wg)(),(0,o.iD)("div",T,[(0,o.Wm)(u,{height:"100vh",distance:30},{content:(0,o.w5)((function(){return[(0,o.Wm)(z.Z,{values:a.value},null,8,["values"])]})),_:1})])):((0,o.wg)(),(0,o.j4)(s,{key:1,image:"empty",description:"\u6682\u65e0\u67e5\u8be2\u7ed3\u679c"}))]})),_:1})],64)}}};const U=L;var B=U,V={navigationBarTitleText:"\u67e5\u8be2\u5386\u53f2\u8ba2\u5355"};Page((0,r.createPageConfig)(B,"package/package-a/searchOrder/index",{root:{cn:[]}},V||{}))}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[91,107,216,592],(function(){return t(370)}));e.O()}]);