<script setup lang="ts">
import { navigateTo } from '@tarojs/taro';
import { useAppStore, useThemeStore } from '@/store';
import { reactive, toRefs, onMounted, Ref, ref, onUnmounted } from 'vue'
import Taro, { eventCenter, getCurrentInstance, getCurrentPages } from '@tarojs/taro';
import { login, getOrder, getUserDefinedClass, hotProduct } from '@/service/api'
import { Search2 } from "@nutui/icons-vue-taro";
import ShoppingCard from './components/shoppingCard.vue'
import { switchTab } from '@tarojs/taro';
import DragView from '@/package/package-a/DragView/index.vue'




const data = reactive({
	categoryInfo: {
		"category": [{
			"catName": "自电集控",
			"catType": 1,
			"children": [
				{
					"catName": "双头灯",
					"catType": 1,
					"childCateList": [
						{
							"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
							"catId": "Z2111A",
							"catName": "Z2111A",
						},

					],
				},
				{
					"catName": "吸顶灯",
					"catType": 1,
					"childCateList": [
						{
							"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
							"catId": "Z2221",
							"catName": "Z2221",
						},

					],
				},
			]
		}]
	},
	category: [{
		"catName": "自电集控",
		"catType": 1,
		"children": [
			{
				"catName": "双头灯",
				"catType": 1,
				"childCateList": [
					{
						"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
						"catId": "Z2111A",
						"catName": "Z2111A",
					},

				],
			},
			{
				"catName": "吸顶灯",
				"catType": 1,
				"childCateList": [
					{
						"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
						"catId": "Z2221",
						"catName": "Z2221",
					},

				],
			},
		]
	}],

	categoryChild: [
		{
			"catName": "双头灯",
			"catType": 1,
			"childCateList": [
				{
					"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
					"catId": "Z2111A",
					"catName": "Z2111A",
				},

			],
		},
		{
			"catName": "吸顶灯",
			"catType": 1,
			"childCateList": [
				{
					"backImg": "http://qiniu.cst119.com/zx/%E8%BE%93%E5%85%A5%E6%A8%A1%E5%9D%97_800_800.jpg",
					"catId": "Z2221",
					"catName": "Z2221",
				},

			],
		},
	],
})

const instance = getCurrentInstance()
onMounted(async () => {
	console.log('首页', Taro.getStorageSync('token'));
	appStore.setActiveTab('/pages/products/index');

	if (instance && instance.router && instance.router.onShow) {
		eventCenter.on(instance.router.onShow, getUserDefinedClassF)
	} else {
		// 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
		console.error('无法获取router.onShow事件处理函数')
		Taro.showToast({
			title: '无法获取事件',
			icon: 'none',
			duration: 2000
		})
	}

	Taro.showLoading({
		title: '加载中...',
		mask: true
	})
	await getData()
	// await getUserDefinedClassF()

	Taro.hideLoading()
})
onUnmounted(() => {
	if (instance && instance.router && instance.router.onShow) {
		eventCenter.off(instance.router.onShow, getUserDefinedClassF)
	} else {
		// 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
		console.error('无法获取router.onShow事件处理函数')
		Taro.showToast({
			title: '无法获取事件',
			icon: 'none',
			duration: 2000
		})
	}

})
// data.categoryInfo = categoryInfo
// data.category = categoryInfo.category
// data.categoryChild = categoryChild
const getUserDefinedClassF = async () => {

	const { error, success }: { error: any, success: any } = await getUserDefinedClass()
	if (error === null) {
		console.log(success?.items, 'success');
		list1.value = success?.items.map((i: { click: (item: Record<string, any>) => void; }) => {
			i.click = gotoClassification
			console.log(i, 'iiiiiii');

			return i

		}).sort((a: any, b: any) => a.sort - b.sort)



	}

}
const change = (index: string | number) => {
	console.log('change');
	data.categoryChild = [].concat(data.categoryInfo.category[index].children)
}
const onChange = (value: { backImg: string, catId: string, catName: string, showPic: boolean, showVideo: boolean }) => {
	//跳转产品详情页
	Taro.navigateTo({
		url: '/package/package-a/Details/index?catId=' + value.catId,
		fail: (res: any) => {
			console.log(res)
		},

	})
	Taro.preload({ value })
	console.log('当前分类数据', value)
}



const list: Ref<Array<string>> = ref([
	'https://mmbiz.qpic.cn/mmbiz_jpg/fjeyLGmLcP9VfqyIYXAnDiaW3GRk68rZY5RdHYH5Du66twVyyOY4GrSJpRzpB4LCpcUeRDJc9NjujKDZGvOlmGQ/640?wx_fmt=jpeg&from=appmsg&wxfrom=13&tp=wxpic',
	'https://mmbiz.qpic.cn/mmbiz_jpg/fjeyLGmLcPib7AeMkGibTfsWSaE6iabImfBY0HDbP5BGgDvueibehBhiaKGUxrB3lRh8Pibic45ajZllSplnibRyVovkUg/640?wx_fmt=jpeg&from=appmsg&tp=wxpic&wxfrom=10005&wx_lazy=1&wx_co=1'
])
const appStore = useAppStore();
const lookInventory = () => {

	// appStore.setActiveTab('/pages/inventory/index');
	// Taro.switchTab({
	// 	url: '/pages/inventory/index'
	// })
	Taro.navigateTo({
		url: '/package/package-a/DragView/index',
		fail: (res: any) => {
			console.log(res)
		},
	})
}

/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '首页',
	enableShareAppMessage: true
});

const getData = async () => {
	// let data = await Taro.request({
	//     url: "http://localhost:9999/api/user/list",
	//     method: "GET",
	//     header: {
	//         "content-type": "application/json",
	//     },
	// });
	// console.log(data);
	// let res =await list1({name:'123'})
	// console.log(res);

};
//公告栏列表
const noticeList = ref(['左向订单系统已上线，诚邀您进行体验！', '以前行力量，指引前进方向！'])


//跳转登录页函数
const toLogin = () => {
	Taro.reLaunch({
		url: '/pages/login/index'
	})
}
const searchValue = ref('')
const searchClick = async () => {
	
	await Taro.hideKeyboard({
		complete: async (res) => {
			
			Taro.showLoading(
				{
					title: '加载中...',
					mask: true
				}
			)
			setTimeout(() => {
				Taro.preload({ value: searchValue.value })
				searchValue.value = ''
				if (SwitchMap.value.get(SwitchValue.value)?.text === '产品') {
					
					//跳转产品详情页
					Taro.navigateTo({
						url: '/package/package-a/Details/index',
						fail: (res: any) => {
							console.log(res)
						},

					})
				}
				else if (SwitchMap.value.get(SwitchValue.value)?.text === '订单') {
					
					Taro.navigateTo({
						url: '/package/package-a/searchOrder/index',
						fail: (res: any) => {
							console.log(res)
						},
					})

				}
				Taro.hideLoading()
			}, 1000)
		}
	})



}
Taro.showShareMenu({
	withShareTicket: true,
	showShareItems: ['shareAppMessage', 'shareTimeline']
})
//跳转到分类页面
const gotoClassification = (item: Record<string, any>) => {
	// Taro.setStorageSync('tab',item)
	// console.log('跳转分类页面', item);

	// Taro.switchTab({
	// 	url: '/pages/classification/index'	
	// })
	// tabSwitch(null,'/pages/classification/index')
	Taro.navigateTo({
		url: '/package/package-a/Details/index?catId=' + item,
		fail: (res: any) => {
			console.log(res)
		},

	})
	Taro.preload({ value: item })

}
function tabSwitch(item: any, url: string) {
	appStore.setActiveTab(url);
	switchTab({ url });
}
const list1: Ref<Array<{ text: string, href: string, click: Function }>> = ref([
	// {
	// 	text: '集电集控',
	// 	href: "https://img01.71360.com/file/read/www/M00/41/57/rBwBHmTWAw-AFdtTAAEdAOpPUAo680.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '自电集控',
	// 	href: "https://img01.71360.com/file/read/www2/M00/9F/BD/rBwBEmTXKYqAE53DAADLk_h5mqY341.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '常规',
	// 	href: "https://img01.71360.com/file/read/www/M00/41/AF/rBwBHmTXOxuAEDEkAABpUuoO18I540.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '自定义',
	// 	href: "http://qiniu.zgzxkjy.com/product/自定义.png",
	// 	click: gotoClassification
	// }
])


//产品跳转查询页

//实现查询产品、订单切换
const SwitchMap = ref(new Map([
	[true, { icon: 'i-bx-copy-alt', text: '订单', text2: '请输入订单号' }],
	[false, { icon: 'i-bx-package', text: '产品', text2: '请输入产品型号/名称/规格/料号' }]
]))
const SwitchValue = ref(false)

const show1 = ref(false)
const listPopover = ref([
	{
		name: 'option1'
	},
	{
		name: 'option2'
	},
	{
		name: 'option3'
	}
])
const choose = (item: unknown, index: number) => {
	console.log(item, index)
}
</script>

<template>
	<basic-layout show-tab-bar style="background-color: #f7f7f7;">
		<!-- <custom-navbar title="首页" /> -->
		<!-- 搜索框 -->

		<div class="search-box">

			<nut-searchbar v-model="searchValue" shape="square" :placeholder="SwitchMap.get(SwitchValue)?.text2"
				input-background="#F0F0F0">
				<template #leftin>
					<Search2 />
				</template>
				<template #leftout>
					<div @click="SwitchValue = !SwitchValue" class="font-bold color-#181818">
						<i :class="SwitchMap.get(SwitchValue)?.icon"></i>{{ SwitchMap.get(SwitchValue)?.text }}
					</div>

				</template>
				<template #rightout>
					<view class="font-900" @click="searchClick()">
						搜索
					</view>
				</template>
			</nut-searchbar>


		</div>
		<!-- 轮播 -->

		<div class="swiper-box">
			<nut-swiper :auto-play="3000" loop style="height: 150px" pagination-visible>
				<nut-swiper-item v-for="(item, index) in list" :key="index" style="height: 150px">
					<img :src="item" alt="" style="height: 100%; width: 100%;" draggable="false" />
				</nut-swiper-item>
			</nut-swiper>
		</div>
		<!-- 功能区 -->
		<div>


			<div
				style="display: flex;flex-direction: row;width: 100%;height: 30px;padding: 3% 1%;justify-content: space-between;">
				<view style="display: -webkit-inline-box">
					<div style="height: 100%;width: 6px;margin-left: 1%;background-color: #1B9FFF;"></div>
					<div style="margin-left: 5%;font-weight: bold;font-size: 36rpx;color: #122F38;line-height: 30px;">{{
						'常用类别'
					}}</div>
				</view>
				<!-- <view class="cpxg-link" @click="lookInventory"
					style="margin-left: auto;margin-right: 5%;font-weight: bold;font-size: 24rpx;line-height: 30px;color: #BABABA;text-decoration: underline">
					<i class="i-bx-cog font-size-20px  color-#2a2a2a"></i>
				</view> -->

				<view class="cpxg-link mr-7.5px!" @click="lookInventory"
					style="margin-left: auto;font-weight: bold;font-size: 24rpx;line-height: 30px;color: #BABABA;text-decoration: underline">
					<i class="i-bx-cog font-size-20px  color-#2a2a2a"></i>
				</view>



			</div>
			<nut-grid :column-num="4" class=" ml-auto mr-auto border-rounded-5px relative" :gutter="5" :border="true">
				<template v-for="(item, index) in list1" :key="item.text">
					<nut-grid-item @click="item.click(item.text)" class="inline-block">
						<image :src="item.href" class="w-55px h-55px" />
						<span style="color: #757575;font-family: serif;font-size: 16px;font-weight: 900;overflow: hidden;text-overflow: ellipsis;
white-space: nowrap;">{{ item.text }}</span>
					</nut-grid-item>
				</template>
			</nut-grid>



			<!-- <DragView v-model="listA" :list1="list1">

			</DragView> -->

		</div>


		<div style="display: flex;flex-direction: row;width: 100%;height: 30px;padding: 3% 1%;">
			<div style="height: 100%;width: 6px;margin-left: 1%;background-color: #1B9FFF;"></div>
			<div style="margin-left: 5%;font-weight: bold;font-size: 36rpx;color: #122F38;line-height: 30px;">{{ '热销产品'
			}}</div>
			<!-- <view class="cpxg-link" @click="lookInventory"
				style="margin-left: auto;margin-right: 5%;font-weight: bold;font-size: 24rpx;line-height: 30px;color: #BABABA;text-decoration: underline">
				{{ '点击查看产品 >' }}</view> -->
		</div>
		<!-- <nut-cell title="产品选购" class="cpxg">
			<template #icon>
				<IconFont class="i-bx-dots-vertical"></IconFont>
			</template>
			<template #link>
				<view class="cpxg-link" @click="lookInventory">{{ '点击查看库存 >' }}</view>
			</template>
		</nut-cell> -->
		<!-- 促销区 -->
		<div>
			<nut-grid :column-num="3" class=" ml-auto mr-auto border-rounded-5px" :gutter="10">
				<ShoppingCard />
				<!-- <ShoppingCard :state="'双88F→/自电集控6mm不锈钢标志灯'" />
				<ShoppingCard :state="'单地下室/集电集控中型平板隧道标志灯'" /> -->
			</nut-grid>
		</div>
		<!-- <div class="m-2">
			<nut-cell style="box-shadow: 0 0 black;">
				<nut-category :category="data.category" @change="change">

					<nut-backtop height="45vh" :distance="30" :bottom="150">
						<template #content>
							<nut-category-pane :categoryChild="data.categoryChild" @onChange="onChange">
							</nut-category-pane>
						</template>
					</nut-backtop>
				</nut-category>
			</nut-cell>
		</div> -->
		<!-- 悬浮导航->快速前往登录 -->
		<div class="toLogin" v-show="!Taro.getStorageSync('token')">
			<span style="font-size: var(--nut-tabbar-height,100%);">{{ '您还没有登录，登录打开精彩世界' }}</span><nut-button
				@click="toLogin" type="primary" class="toLogin-button" size="small">{{
					'立即登录' }}</nut-button>
		</div>

		<!-- 底部信息栏 -->
		<div class="user-button-text">
			<span>{{ '技术支持 © 广东左向科技有限公司' }}</span>
		</div>
	</basic-layout>
</template>
<style lang="scss">
.nut-category-pane__childImg {}

.user-button-text {
	font-size: 12px;
	color: #A6A6A6;
	text-align: center;
	margin: 2rem auto;
}

.toLogin {
	position: fixed;
	bottom: 13%;
	background-color: rgba($color: #3a3a3a, $alpha: .7);
	border-radius: 5px;
	left: 50%;
	transform: translate(-50%, 0);
	padding: 2% 4%;
	line-height: 100%;
	color: #D0D0D0;
	font-size: 0.75rem;
	line-height: 1.875rem;
	width: 85%;
	display: flex;
	justify-content: space-between;

	.toLogin-button {
		color: #EAEEF9;
	}
}

.scroll-view-item_H {
	display: inline-block;
	width: 100%;
	height: 75px;
}

.cpxg {
	box-shadow: none;
	background: transparent;
	padding-left: 0;
	font-weight: 600;
	font-size: 1.125rem;

	.cpxg-link {
		color: #070707;
		font-size: 12px;
		font-weight: 100;
	}
}

::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}

.item {
	width: 200px;
	height: 100px;
	margin: 0 10px;
	background: #f0f0f0;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
}
</style>
