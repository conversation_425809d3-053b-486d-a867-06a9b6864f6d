<script setup lang="ts">
import { navigateTo, useDidHide, useDidShow } from '@tarojs/taro';
import { useAppStore, useThemeStore } from '@/store';
import { reactive, toRefs, onMounted, Ref, ref, onUnmounted } from 'vue'
import Taro, { eventCenter, getCurrentInstance, getCurrentPages } from '@tarojs/taro';
import { login, getOrder, getUserDefinedClass, hotProduct } from '@/service/api'
import { Search2 } from "@nutui/icons-vue-taro";
import ShoppingCard from './components/shoppingCard.vue'
import { switchTab } from '@tarojs/taro';
import DragView from '@/package/package-a/DragView/index.vue'






const instance = getCurrentInstance()

onMounted(() => {
	// console.log('首页', Taro.getStorageSync('token'));
	appStore.setActiveTab('/pages/products/index');
	console.log('onMounted');
	// eventCenter.off(instance.router.onShow, getUserDefinedClassF)

	if (instance && instance.router && instance.router.onShow) {

		// eventCenter.on(getCurrentInstance().router!.onShow, getUserDefinedClassF)
		eventCenter.on(getCurrentInstance().router!.onShow, () => {

			// getUserDefinedClassF()3
			// getData()
			console.log('onShow')
		})
	} else {
		// 处理null或undefined的情况，可能是打印错误日志或进行其他错误处理
		console.error('无法获取router.onShow事件处理函数')
		Taro.showToast({
			title: '无法获取事件',
			icon: 'none',
			duration: 2000
		})
	}

	Taro.showLoading({
		title: '加载中...',
		mask: true
	})
	//  getData()
	// await getUserDefinedClassF()

	Taro.hideLoading()
})
onUnmounted(() => {
	console.log('onUnmounted')

	eventCenter.off()
	// getUserDefinedClassF=null
	// eventCenter.off(instance.router.onShow, ())

})
useDidShow(() => {
	console.log('每次页面显示时执行')
	getUserDefinedClassF()
})

useDidHide(() => {
	console.log('页面隐藏时清理操作')
})
// data.categoryInfo = categoryInfo
// data.category = categoryInfo.category
// data.categoryChild = categoryChild
let getUserDefinedClassF = async () => {


	const { error, success }: { error: any, success: any } = await getUserDefinedClass()
	if (error === null) {
		// console.log(success?.items, 'success');
		list1.value = success?.items.map((i: { click: (item: Record<string, any>) => void; }) => {
			i.click = gotoClassification
			// console.log(i, 'iiiiiii');

			return i

		}).sort((a: any, b: any) => a.sort - b.sort)
	}

}
const change = (index: string | number) => {
	// console.log('change');
	data.categoryChild = [].concat(data.categoryInfo.category[index].children)
}
const onChange = (value: { backImg: string, catId: string, catName: string, showPic: boolean, showVideo: boolean }) => {
	//跳转产品详情页
	Taro.navigateTo({
		url: '/package/package-a/Details/index?catId=' + value.catId,
		fail: (res: any) => {
			console.log(res)
		},

	})
	Taro.preload({ value })
	// console.log('当前分类数据', value)
}



const list: Ref<Array<string>> = ref([
	'https://mmbiz.qpic.cn/mmbiz_jpg/fjeyLGmLcP9VfqyIYXAnDiaW3GRk68rZY5RdHYH5Du66twVyyOY4GrSJpRzpB4LCpcUeRDJc9NjujKDZGvOlmGQ/640?wx_fmt=jpeg&from=appmsg&wxfrom=13&tp=wxpic',
	'https://mmbiz.qpic.cn/mmbiz_jpg/fjeyLGmLcPib7AeMkGibTfsWSaE6iabImfBY0HDbP5BGgDvueibehBhiaKGUxrB3lRh8Pibic45ajZllSplnibRyVovkUg/640?wx_fmt=jpeg&from=appmsg&tp=wxpic&wxfrom=10005&wx_lazy=1&wx_co=1'
])
const appStore = useAppStore();
const lookInventory = () => {

	// appStore.setActiveTab('/pages/inventory/index');
	// Taro.switchTab({
	// 	url: '/pages/inventory/index'
	// })
	Taro.navigateTo({
		url: '/package/package-a/DragView/index',
		fail: (res: any) => {
			console.log(res)
		},
	})
}

/** 设置页面属性 */
definePageConfig({
	navigationBarTitleText: '首页',
	enableShareAppMessage: true
});

const getData = async () => {
	// let data = await Taro.request({
	//     url: "http://localhost:9999/api/user/list",
	//     method: "GET",
	//     header: {
	//         "content-type": "application/json",
	//     },
	// });
	// console.log(data);
	// let res =await list1({name:'123'})
	console.log('res');

};
//公告栏列表
const noticeList = ref('左向订单系统已上线，诚邀您进行体验！', '以前行力量，指引前进方向！')


//跳转登录页函数
const toLogin = () => {
	Taro.reLaunch({
		url: '/pages/login/index'
	})
}
const searchValue = ref('')
const searchClick = async () => {

	await Taro.hideKeyboard({
		complete: async (res) => {

			Taro.showLoading(
				{
					title: '加载中...',
					mask: true
				}
			)
			setTimeout(() => {
				Taro.preload({ value: searchValue.value })
				searchValue.value = ''
				if (SwitchMap.value.get(SwitchValue.value)?.text === '产品') {

					//跳转产品详情页
					Taro.navigateTo({
						url: '/package/package-a/Details/index',
						fail: (res: any) => {
							console.log(res)
						},

					})
				}
				else if (SwitchMap.value.get(SwitchValue.value)?.text === '订单') {

					Taro.navigateTo({
						url: '/package/package-a/searchOrder/index',
						fail: (res: any) => {
							console.log(res)
						},
					})

				}
				Taro.hideLoading()
			}, 1000)
		}
	})



}
Taro.showShareMenu({
	withShareTicket: true,
	showShareItems: ['shareAppMessage', 'shareTimeline']
})
//跳转到分类页面
const gotoClassification = (item: Record<string, any>) => {
	Taro.navigateTo({
		url: '/package/package-a/Details/index?catId=' + item,
		fail: (res: any) => {
			console.log(res)
		},

	})
	Taro.preload({ value: item })

}
function tabSwitch(item: any, url: string) {
	appStore.setActiveTab(url);
	switchTab({ url });
}
const list1: Ref<Array<{ text: string, href: string, click: Function }>> = ref([
	// {
	// 	text: '集电集控',
	// 	href: "https://img01.71360.com/file/read/www/M00/41/57/rBwBHmTWAw-AFdtTAAEdAOpPUAo680.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '自电集控',
	// 	href: "https://img01.71360.com/file/read/www2/M00/9F/BD/rBwBEmTXKYqAE53DAADLk_h5mqY341.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '常规',
	// 	href: "https://img01.71360.com/file/read/www/M00/41/AF/rBwBHmTXOxuAEDEkAABpUuoO18I540.png?w=600",
	// 	click: gotoClassification
	// },
	// {
	// 	text: '自定义',
	// 	href: "http://qiniu.zgzxkjy.com/product/自定义.png",
	// 	click: gotoClassification
	// }
])


//产品跳转查询页

//实现查询产品、订单切换
const SwitchMap = ref(new Map([
	[true, { icon: 'i-bx-copy-alt', text: '订单', text2: '请输入订单号' }],
	[false, { icon: 'i-bx-package', text: '产品', text2: '请输入产品型号/名称/规格/料号' }]
]))
const SwitchValue = ref(false)

const show1 = ref(false)
const listPopover = ref([
	{
		name: 'option1'
	},
	{
		name: 'option2'
	},
	{
		name: 'option3'
	}
])
const choose = (item: unknown, index: number) => {
	console.log(item, index)
}
</script>

<template>
	<basic-layout show-tab-bar style="background-color: #f7f7f7;">
		<!-- 搜索框 - 优化样式 -->
		<div class="search-box p-3 bg-white shadow-sm">
			<nut-searchbar v-model="searchValue" shape="round" :placeholder="SwitchMap.get(SwitchValue)?.text2"
				input-background="#F5F5F5">
				<template #leftin>
					<Search2 />
				</template>
				<template #leftout>
					<div @click="SwitchValue = !SwitchValue" class="font-bold color-#122F38 flex items-center">
						<i :class="SwitchMap.get(SwitchValue)?.icon"></i>
						<span class="ml-1">{{ SwitchMap.get(SwitchValue)?.text }}</span>
					</div>
				</template>
				<template #rightout>
					<view class="font-900 color-#122F38" @click="searchClick()">
						搜索
					</view>
				</template>
			</nut-searchbar>
		</div>

		<!-- 轮播 - 添加圆角和阴影 -->
		<div class="swiper-container mx-3 mt-3 rounded-lg overflow-hidden shadow-sm">
			<nut-swiper :init-page="0" :pagination-visible="true" pagination-color="#426543" auto-play="3000"
				class="h-100%!">
				<nut-swiper-item v-for="(item, index) in list" :key="index" class="h-full!">
					<img :src="item" class="w-full h-full! object-cover" />
				</nut-swiper-item>
			</nut-swiper>
		</div>

		<!-- 公告栏 - 优化样式 -->
		<div class="mx-3 mt-3">
			<nut-noticebar :text="noticeList" class="rounded-md bg-white shadow-sm p-2"
				:background="'rgba(255,255,255,0.9)'" color="#333"></nut-noticebar>
		</div>

		<!-- 分类导航 - 优化样式 -->
		<div class="mx-3 mt-3 bg-white rounded-lg p-3 shadow-sm">
			<div class="flex justify-between items-center mb-2">
				<div class="flex items-center">
					<div class="w-1 h-5 bg-#122F38 mr-2"></div>
					<div class="font-bold text-#122F38">搜索关键词</div>
				</div>
				<view @click="lookInventory" class="text-#122F38 text-sm">
					<i class="i-bx-cog font-size-20px"></i>
				</view>
			</div>

			<nut-grid :column-num="4" class="border-rounded-5px" :border="false">
				<template v-for="(item, index) in list1" :key="item.text">
					<nut-grid-item @click="item.click(item.text)"
						class="p-1 box-border! hover:bg-#f5f5f5 rounded-md transition-all">
						<image :src="item.href" class="w-55px h-55px mb-1" />
						<span class="text-#333 text-sm font-medium block truncate">{{ item.text }}</span>
					</nut-grid-item>
				</template>
			</nut-grid>
		</div>

		<!-- 热销产品 - 优化样式 -->
		<div class="mx-3 mt-3 bg-white rounded-lg p-3 shadow-sm">
			<div class="flex items-center mb-3">
				<div class="w-1 h-5 bg-#122F38 mr-2"></div>
				<div class="font-bold text-#122F38">热销产品</div>
			</div>
			<!-- 热销产品内容 -->
			<div>
				<nut-grid :column-num="2" class=" ml-auto mr-auto border-rounded-5px justify-evenly" :gutter="10">
					<ShoppingCard />
				</nut-grid>
			</div>
		</div>

		<!-- 悬浮登录提示 - 优化样式 -->
		<div class="toLogin fixed bottom-20 left-0 right-0 mx-3 bg-white rounded-lg p-3 shadow-lg flex justify-between items-center"
			v-show="!Taro.getStorageSync('token')">
			<span class="text-#333">您还没有登录，登录打开精彩世界</span>
			<nut-button @click="toLogin" type="primary" size="small" class="ml-2">立即登录</nut-button>
		</div>

		<!-- 底部信息栏 - 优化样式 -->
		<div class="user-button-text text-center text-#999 text-xs py-4">
			<span>技术支持 © 广东左向科技有限公司</span>
		</div>
	</basic-layout>
</template>

<style lang="scss">
.swiper-container {
	height: 150px;
}

.nut-noticebar {
	--nutui-noticebar-background: rgba(255, 255, 255, 0.9);
	--nutui-noticebar-color: #333;
	--nutui-noticebar-font-size: 14px;
}

.nut-grid-item {
	transition: all 0.2s;

	&:active {
		transform: scale(0.95);
	}
}

.toLogin {
	animation: fadeIn 0.5s;
	z-index: 10;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>