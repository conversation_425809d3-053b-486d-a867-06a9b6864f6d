<script setup lang="ts">
import Taro, { eventCenter, getCurrentInstance } from '@tarojs/taro';
import { useRouter } from '@tarojs/taro';
import { onBeforeMount, onMounted, ref } from 'vue';
import { My, Clock, Location, Order } from '@nutui/icons-vue-taro'
//控制选项卡
const value = ref('1')
//将预加载的数据preloadData保存到数组对象中
const dataList = ref([])
//两个数组对象，一个保存供货订单，一个保存求购订单
const dataListSupply = ref([])
const dataListBuy = ref([])
definePageConfig({
	navigationBarTitleText: '订单一览'
});
const router = useRouter()
const data = ref(new Array(100).fill(0))
//拿到预加载的数据
const preloadData: Record<any, any> | undefined = Taro.getCurrentInstance().preloadData
const pushPreloadData = () => {
	console.log('preloadData', preloadData);
	if (preloadData) {
		if (stateMap.get(router.params.type as string) === '供货') {
			console.log('supply');

			dataListSupply.value.push(preloadData)
		} else {
			dataListBuy.value.push(preloadData)
		}
		dataList.value.push(preloadData)
	}

	console.log('dataList.length', dataList.value);

}
onBeforeMount(() => {
	pushPreloadData()
})
onMounted(() => {
	data.value = data.value.map((_: number, index: number) => index + 1)

})

const onScrollBottom = () => {
	let arr = new Array(100).fill(0)
	const len = data.value.length
	data.value = data.value.concat(arr.map((_: number, index: number) => len + index + 1))
}
const stateMap = new Map([
	['supply', '供货'],
	['buy', '求购']
])
</script>
<template>
	<basic-layout>
		<!-- <custom-navbar title="订单一览" left-show /> -->
		<div class="from-box">
			<!-- {{ router.params }}
			{{ preloadData }} -->
			<!-- {{ dataList }} -->


			<nut-tabs v-model="value">
				<nut-tab-pane title="全部" pane-key="1">
					<nut-list v-if="dataList.length > 0" v-model:list-data="dataList" :container-height="470"
						@scroll-bottom="onScrollBottom">
						<template #default="{ item, value }">
							<div class="order-card list-item">
								<nut-cell :title="'联系人:' + item['联系人']" :desc="stateMap.get(router.params.type as string)" is-link>
									<template #icon>
										<My />
									</template>
								</nut-cell>
								<nut-cell :title="'产品型号:' + item['产品型号']" :desc="'数量:' + (item['供货数量'] ?? item['求购数量'])">
									<template #icon>
										<Order />
									</template>
								</nut-cell>
								<nut-cell :title="'时间:' + (item['供货时间'] ?? item['求购时间'])">
									<template #icon>
										<Clock />
									</template>
								</nut-cell>
								<nut-cell :title="'地址:' + (item['供货地址'] ?? item['求购地址'])">
									<template #icon>
										<Location />
									</template>
								</nut-cell>
							</div>
						</template>
					</nut-list>
					<div class="no-data" v-else>
						<nut-empty description="暂无数据" />
					</div>


				</nut-tab-pane>
				<nut-tab-pane title="求购中" pane-key="2">
					<nut-list v-if="dataListBuy.length > 0" v-model:list-data="dataListBuy" :container-height="470"
						@scroll-bottom="onScrollBottom">
						<template #default="{ item, value }">
							<div class="order-card list-item">
								<nut-cell :title="'联系人:' + item['联系人']" :desc="stateMap.get(router.params.type as string)" is-link>
									<template #icon>
										<My />
									</template>
								</nut-cell>
								<nut-cell :title="'产品型号:' + item['产品型号']" :desc="'数量:' + (item['供货数量'] ?? item['求购数量'])">
									<template #icon>
										<Order />
									</template>
								</nut-cell>
								<nut-cell :title="'时间:' + (item['供货时间'] ?? item['求购时间'])">
									<template #icon>
										<Clock />
									</template>
								</nut-cell>
								<nut-cell :title="'地址:' + (item['供货地址'] ?? item['求购地址'])">
									<template #icon>
										<Location />
									</template>
								</nut-cell>
							</div>
						</template>
					</nut-list>

					<div class="no-data" v-else>
						<nut-empty description="暂无数据" />
					</div>
				</nut-tab-pane>
				<nut-tab-pane title="供给中" pane-key="3">
					<nut-list v-if="dataListSupply.length > 0" v-model:list-data="dataListSupply" :container-height="470"
						@scroll-bottom="onScrollBottom">
						<template #default="{ item, value }">
							<div class="order-card list-item">
								<nut-cell :title="'联系人:' + item['联系人']" :desc="stateMap.get(router.params.type as string)" is-link>
									<template #icon>
										<My />
									</template>
								</nut-cell>
								<nut-cell :title="'产品型号:' + item['产品型号']" :desc="'数量:' + (item['供货数量'] ?? item['求购数量'])">
									<template #icon>
										<Order />
									</template>
								</nut-cell>
								<nut-cell :title="'时间:' + (item['供货时间'] ?? item['求购时间'])">
									<template #icon>
										<Clock />
									</template>
								</nut-cell>
								<nut-cell :title="'地址:' + (item['供货地址'] ?? item['求购地址'])">
									<template #icon>
										<Location />
									</template>
								</nut-cell>
							</div>
						</template>
					</nut-list>

					<div class="no-data" v-else>
						<nut-empty description="暂无数据" />
					</div>
				</nut-tab-pane>
			</nut-tabs>
		</div>
	</basic-layout>
</template>

<style lang="scss">
.order-card {
	border-radius: 10px;
	//添加边框阴影
	box-shadow: 0 0 10px rgba(94, 92, 92, 0.1);
	border: 2px solid #A7B0C9;
	margin: 3% 0;

	.nut-cell {
		margin: 0 !important;
		border-radius: none !important;
	}
}
</style>
