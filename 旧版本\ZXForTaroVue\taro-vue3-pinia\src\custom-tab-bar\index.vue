<script setup lang="ts">
import { computed } from 'vue';
import { switchTab } from '@tarojs/taro';
import { useAppStore, useThemeStore } from '@/store';

import { useRouter } from '@tarojs/taro';
const tabBar = {
	custom: true,
	color: '#000000',
	selectedColor:"FF0000",
	list: [
		{
			pagePath: '/pages/products/index',
			text: '首页',
			icon: 'i-bx-bxl-product-hunt'
		},
		{
			pagePath: '/pages/classification/index',
			text: '分类',
			icon: 'i-bx-grid-alt'
		},
		{
			pagePath: '/pages/shoppingCart/index',
			text: '购物车',
			icon: 'i-bx-shopping-bag'
		},
		{
			pagePath:'/pages/orders/index',
			text:'订单',
			icon:'i-bx-calendar-alt'
		},
		{
			pagePath: '/pages/my/index',
			text: '个人中心',
			icon: 'i-bx-user'
		},
	]
};


const themeStore = useThemeStore();
const theme = computed(() => themeStore.theme);
const themeVars = computed(() => themeStore.themeVars);

const appStore = useAppStore();
// console.log(appStore.getActiveTab,'appStore.getActiveTab');

const activeTab = computed(() => appStore.getActiveTab);





function tabSwitch(item: any, url: string) {
	appStore.setActiveTab(url);
	switchTab({ url });
}
</script>
<script lang="ts">
export default {
	options: {
		addGlobalClass: true
	}
};
</script>
<template>
	
	<nut-config-provider :theme="theme" :theme-vars="themeVars">
		<nut-tabbar :model-value="activeTab" bottom safe-area-inset-bottom @tab-switch="tabSwitch" active-color="#FF0000">
			<nut-tabbar-item v-for="item in tabBar.list" :key="item.pagePath" :name="item.pagePath"
				:tab-title="item.text">
				<template #icon>
					<div class="text-25px" :class="item.icon" />
				</template>
			</nut-tabbar-item>
		</nut-tabbar>
	</nut-config-provider>
</template>

<style lang="scss"></style>
