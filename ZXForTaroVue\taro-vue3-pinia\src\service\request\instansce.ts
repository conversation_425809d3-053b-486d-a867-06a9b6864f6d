import { request } from "@tarojs/taro";
import {
	REQUEST_TIMEOUT,
	SUCCESS_CODE,
	REFRESH_TOKEN_CODE,
	ERROR401_CODE,
} from "@/constants";
import {
	getRequestUrl,
	getRequestHeaders,
	handleExpireToken,
	showErrorMsg,
} from "./helpers";
import Taro from "@tarojs/taro";
import { useAuthStore } from "@/store/modules/auth";
const auth = useAuthStore();
const interceptor = function (chain: any) {
	const requestParams = chain.requestParams;
	const { method, data, url, header } = requestParams;
	// console.log(method, data, url, header,"aaaa");
	
	// console.log(header, "req-header");

	// console.log(url, "当前请求url为");
	// Taro.showToast({
	// 	title: url,
	// 	icon: "loading",
	// 	duration: 2000,
	// });



	//没有token
	if (
		!header.token &&
		url !== "https://ddxt.zgzxkjy.com:8070/zx/api/v1/login"
	) {
		return Taro.showToast({
			title: "请先登录",
			icon: "error",
			duration: 2000,
			success: () => {
				setTimeout(() => {
					handleExpireToken();
				}, 2000);
			},
		});
	} else {
		return chain.proceed(requestParams).then((res: any) => {
			// console.log(`http <-- ${url} result:`, res);
			return res;
		});
	}
};
Taro.addInterceptor(interceptor);
async function axios<T>(
	config: Service.RequestParam,
): Promise<Service.RequestResult<T>> {
	const { method, url, data } = config;
	const axiosConfig = config.axiosConfig as Service.AxiosConfig;
	const header = getRequestHeaders(axiosConfig);
	return await new Promise((resolve, reject) => {
		request({
			/** 兼容Url不同的情况，可以通过填写完整路径 */
			url: getRequestUrl(url),
			method,
			/** 对所有请求添加时间戳以防止缓存 */
			data: { _t: Date.now(), ...data },
			header,
			timeout: REQUEST_TIMEOUT,
			success: (res) => {
				const { code, msg, data } =
					res.data;
				// 	console.log('这里是返回值',res);
				// 	console.log('adsdsfd',code, msg, data);
					
					
				// console.log(msg, "返回的msg");
				/* 成功请求 */
				if (code === SUCCESS_CODE) {
					return resolve({
						error: null,
						success: data,
						msg,
						code
					});
				}
				if(code === 7){
					showErrorMsg(msg);
				}
				if (REFRESH_TOKEN_CODE.includes(code)) {
					setTimeout(() => {
						Taro.showToast({
							title: "登录过期请重登",
							icon: "error",
							duration: 2000,
							success: () => {
								setTimeout(() => {
									handleExpireToken();
								}, 2000);
							},
						});
					}, 2000)
				}
				/** 仅有使用服务端错误信息的请求才 toast 提示错误 */
				// console.log("axiosConfig.useErrMsg", axiosConfig.useErrMsg);

				// console.log(code, "code");

				if (axiosConfig.useErrMsg) {
					showErrorMsg("服务器异常");
				}
				if (code === ERROR401_CODE) {
					showErrorMsg(msg);
					if (
						msg === "用户信息过期，请重新登录" ||
						msg === "用户信息错误或过期，请重新登录"
					) {
						auth.resetAuthStore();
						setTimeout(() => {
							Taro.reLaunch({
								url: "/pages/login/index",
							});
						}, 2000);
					}
				}
				return resolve({
					error: {
						msg,
						errorCode: code,
					},
					success: null,
					code,
					msg
				});
			},
			fail: (err) => {
				console.log("err", err);
				Taro.hideLoading()
				
				Taro.showToast({
					title: `网络故障`+err.errMsg,
					icon: "error",
					duration: 2000,
				});
				
				reject(err);
			},
			complete: () => {
				//
			},
		});
	});
}

export default axios;
