{"msg": "操作成功", "code": 200, "permissions": ["system:user:resetPwd", "course:coursetype:query", "system:post:list", "monitor:druid:list", "system:menu:query", "system:menu:list", "tool:gen:edit", "system:dict:edit", "monitor:logininfor:remove", "system:user:query", "system:plans:edit", "system:user:export", "system:role:remove", "monitor:job:edit", "system:dept:query", "course:coursetype:edit", "system:dict:query", "monitor:online:query", "system:notice:edit", "monitor:online:list", "tool:gen:import", "system:plans:query", "monitor:logininfor:list", "workattendance:studentattendance:edit", "workattendance:studentattendance:query", "system:config:edit", "workattendance:studentattendance:export", "course:coursetype:add", "system:config:list", "system:menu:add", "workattendance:studentattendance:add", "transcript:manage_score:remove", "system:dict:remove", "system:user:edit", "system:post:export", "system:role:edit", "system:config:query", "system:plans:export", "monitor:operlog:remove", "system:role:add", "system:menu:remove", "system:dict:add", "monitor:logininfor:query", "transcript:manage_score:edit", "tool:build:list", "tool:swagger:list", "system:plans:remove", "system:dept:edit", "system:post:add", "monitor:job:remove", "system:role:export", "system:config:add", "monitor:logininfor:unlock", "monitor:job:export", "transcript:manage_score:import", "monitor:operlog:export", "system:dept:remove", "monitor:job:list", "workattendance:studentattendance:remove", "system:plans:list", "system:user:add", "system:notice:remove", "transcript:manage_score:export", "tool:gen:query", "system:plans:import", "workattendance:studentattendance:import", "system:dict:list", "monitor:job:query", "monitor:online:forceLogout", "system:plans:add", "system:notice:list", "system:notice:query", "workattendance:studentattendance:list", "system:post:edit", "monitor:job:add", "tool:gen:list", "transcript:manage_score:query", "transcript:manage_score:add", "system:dict:export", "system:post:query", "system:post:remove", "system:user:remove", "system:role:list", "transcript:manage_score:list", "system:user:import", "system:config:export", "monitor:online:batchLogout", "system:dept:list", "monitor:operlog:list", "monitor:server:list", "course:coursetype:list", "monitor:logininfor:export", "monitor:job:changeStatus", "tool:gen:preview", "monitor:operlog:query", "system:user:list", "system:notice:add", "monitor:cache:list", "course:coursetype:export", "tool:gen:code", "tool:gen:remove", "course:coursetype:remove", "system:role:query", "system:menu:edit", "system:dept:add", "system:config:remove"], "roles": ["teacher"], "user": {"createBy": "admin", "createTime": "2025-03-25 01:50:17", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "userId": 738, "deptId": 237, "userName": "易蕾", "nickName": "易蕾", "email": null, "userType": "4", "phonenumber": null, "sex": "0", "avatar": null, "password": "$2a$10$5k5qFJo0y2p2DURYHr2tZee.w/hDn4pLi0sfCvbfw2Oo2pqazLjjq", "status": "0", "delFlag": "0", "loginIp": "*************", "loginDate": "2025-04-13T16:38:48.000+08:00", "dept": {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "deptId": 237, "parentId": 234, "ancestors": "0,100,234", "deptName": "教务", "orderNum": 0, "leader": null, "phone": null, "email": null, "status": "0", "delFlag": null, "parentName": null, "children": []}, "roles": [{"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "roleId": 100, "roleName": "老师", "roleKey": "teacher", "roleSort": 3, "dataScope": "1", "menuCheckStrictly": false, "deptCheckStrictly": false, "status": "0", "delFlag": null, "flag": false, "menuIds": null, "deptIds": null, "permissions": ["system:user:resetPwd", "course:coursetype:query", "system:post:list", "monitor:druid:list", "system:menu:query", "system:menu:list", "tool:gen:edit", "system:dict:edit", "monitor:logininfor:remove", "system:user:query", "system:plans:edit", "system:user:export", "system:role:remove", "monitor:job:edit", "system:dept:query", "course:coursetype:edit", "system:dict:query", "monitor:online:query", "system:notice:edit", "monitor:online:list", "tool:gen:import", "system:plans:query", "monitor:logininfor:list", "workattendance:studentattendance:edit", "workattendance:studentattendance:query", "system:config:edit", "workattendance:studentattendance:export", "course:coursetype:add", "system:config:list", "system:menu:add", "workattendance:studentattendance:add", "transcript:manage_score:remove", "system:dict:remove", "system:user:edit", "system:post:export", "system:role:edit", "system:config:query", "system:plans:export", "monitor:operlog:remove", "system:role:add", "system:menu:remove", "system:dict:add", "monitor:logininfor:query", "transcript:manage_score:edit", "tool:build:list", "tool:swagger:list", "system:plans:remove", "system:dept:edit", "system:post:add", "monitor:job:remove", "system:role:export", "system:config:add", "monitor:logininfor:unlock", "monitor:job:export", "transcript:manage_score:import", "monitor:operlog:export", "system:dept:remove", "monitor:job:list", "workattendance:studentattendance:remove", "system:plans:list", "system:user:add", "system:notice:remove", "transcript:manage_score:export", "tool:gen:query", "system:plans:import", "workattendance:studentattendance:import", "system:dict:list", "monitor:job:query", "monitor:online:forceLogout", "system:plans:add", "system:notice:list", "system:notice:query", "workattendance:studentattendance:list", "system:post:edit", "monitor:job:add", "tool:gen:list", "transcript:manage_score:query", "transcript:manage_score:add", "system:dict:export", "system:post:query", "system:post:remove", "system:user:remove", "system:role:list", "transcript:manage_score:list", "system:user:import", "system:config:export", "monitor:online:batchLogout", "system:dept:list", "monitor:operlog:list", "monitor:server:list", "course:coursetype:list", "monitor:logininfor:export", "monitor:job:changeStatus", "tool:gen:preview", "monitor:operlog:query", "system:user:list", "system:notice:add", "monitor:cache:list", "course:coursetype:export", "tool:gen:code", "tool:gen:remove", "course:coursetype:remove", "system:role:query", "system:menu:edit", "system:dept:add", "system:config:remove"], "admin": false}], "roleIds": null, "postIds": null, "roleId": null, "admin": false}}