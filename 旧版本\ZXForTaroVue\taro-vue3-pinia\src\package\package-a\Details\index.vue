<script setup lang="ts">
import Taro, { eventCenter, useRouter, getCurrentInstance, useReachBottom } from '@tarojs/taro';
import { reactive, ref, onMounted, Ref, onBeforeMount, onUnmounted, toRaw } from 'vue';
const router = useRouter();
const catId = router.params.catId
import { HeartFill, Heart1, IconFont, Search2 } from '@nutui/icons-vue-taro'
import { useAuthStore } from '@/store/index'
import { getAddress, getProductDetail, getProductByNumber, getProduct, getFindCart, updateFindCart } from '@/service/index'
import _ from 'lodash'; // 引入lodash库

interface obj {
	[idx: string]: any
}
const auth = useAuthStore()
definePageConfig({
	navigationBarTitleText: '产品详情',
	enablePullDownRefresh: true,
	onReachBottomDistance: 50,
});

Taro.usePageScroll(_.throttle(
	(res) => {


		if (res.scrollTop >= 528) {
			// console.log('true');
			PageScrollTop.value = true
		} else {
			// console.log('false');
			PageScrollTop.value = false

		}
	}

	, 300));
const PageScrollTop = ref(false)
Taro.useReachBottom(async () => {
	console.log('触底了');

	onScrollBottomPage()
})
const onScrollBottomPage = () => {
	// console.log(CurrentPage.value, DefaultTotalPage.value);

	if (CurrentPage.value < DefaultTotalPage.value) {
		CurrentPage.value = Number(CurrentPage.value) + 1
		loadinggetProductF(CurrentPage.value)
	} else {
		Taro.showToast({
			title: '全部加载完毕~',
			icon: 'success',
			duration: 2000
		})
	}

}
//
const CurrentPage = ref(1)
const DefaultTotalPage = ref(1)
const loadinggetProductF = async (page: number = 1) => {
	Taro.showLoading({
		title: '加载中...',
		mask: true
	})
	if (preloadData!.value) {
		const { error, success } = await getProduct({ name: preloadData!.value, page: Number(page).toString() })
		if (error === null) {
			console.log("数据加载结果", success);
			const { items, psize, cur_page } = success as { items: Array<any>, psize: number, cur_page: number }
			DefaultTotalPage.value = psize
			CurrentPage.value = cur_page
			let temp_value: any[] | null = []
			temp_value = items.map((item) => {
				return Object.assign(item, {
					count: 1,
					maxBuy: 10000,
					checkbox: false,
					design: '',
					leaveMsg: ''
				})
			})
			// console.log('temp_valuetemp_valuetemp_valuetemp_value', temp_value);


			cartCommodities.value.push(...temp_value)
			temp_value = null
		}


	} else {
		await getProductDetailF(preloadData!.model)
	}
	Taro.hideLoading()

}
let clearBottom = ref()
onMounted(async () => {

	// console.log('112',toRaw(clearBottom.value.style._value));

	// eventCenter.on(getCurrentInstance().router!.onShow, async () => {
	await loadinggetProductF()
	// console.log('preloadData', preloadData!.model);




	await getCart()


	// console.log('preloadData', preloadData);

	// })


})
onUnmounted(() => {
	cartCommodities.value = []
	// eventCenter.off(getCurrentInstance().router!.onShow)
})
//获取产品数据
const getProductDetailF = async (model: string) => {
	const { error, success } = await getProduct({ name: model, page: Number(1).toString() })
	if (error === null) {

		const { items, psize, cur_page } = success as { items: Array<any>, psize: number, size: number, cur_page: number }
		// DefaultTotalPage.value = psize
		// CurrentPage.value = size
		searchListIndex.value = 1
		DQsearchListIndex.value = psize
		DefaultTotalPage.value = psize
		CurrentPage.value = cur_page
		console.log("数据加载结果~~", psize, DQsearchListIndex.value);
		let temp_value: any[] | null = []
		temp_value = items.map((item) => {
			return Object.assign(item, {
				count: 1,
				maxBuy: 10000,
				checkbox: false,
				design: '',
				leaveMsg: '',
				cartCommodities: false
			})
		})
		// console.log('temp_valuetemp_valuetemp_valuetemp_value', temp_value);


		cartCommodities.value.push(...temp_value)
		temp_value = null

	}
}

const preloadData: Record<any, any> | undefined = reactive(Taro.getCurrentInstance().preloadData!)



//购买数量
const countNumber = ref(1)





//前往购物车
const gotoshopping = () => {
	Taro.switchTab({
		url: '/pages/shoppingCart/index'
	})
}


//购物车页copy
const allSelected = ref(false)
const isRmCommodityDialogShow = ref(false)
interface IcartCommodities {
	id: number,
	code: string,
	name: string,
	spec: string,
	model: string,
	price_unit: number,
	image: string,
	count: number,
	maxBuy: number,
	checkbox: boolean,
	design: string | undefined,
	leaveMsg: string | undefined,
	fanhao_id: string | undefined,


}
// image: 'https://img01.71360.com/file/read/www2/M00/96/19/rBwBEmTHh_-AClvEAACUH1BBgu0725.png?w=600',
// id: 2, 
// name: 'ZX0151亚银双头灯', 
// price: 66.60, 
// count: 1, 
// maxBuy: 10000, 
// checkbox: false, 
// spec: '352*138*4mm/DC24-36V/吊装/15cm/上出线/40pcs', 
// design: undefined,leaveMsg:'' 


const cartCommodities: Ref<Array<IcartCommodities>> = ref(
	[
	])



// const getCartF = async (userId: string) => {
// 	const { error, success } = await getCart({ userId })
// 	if (error === null) {
// 		console.log(success);

// 		cartCommodities.value = [...success as Array<IcartCommodities>]
// 	}

// }

onBeforeMount(() => {
	// getCartF(Taro.getStorageSync('userInfo').userId)
	// cartCommodities.value.forEach(item => {
	// 	item['checkbox'] = false
	// })
})
const totalPrice = ref(0)
const swipeRefs = ref([])
const goMakeOrder = () => {
	visibleSubmit.value = true
}

let tempId = -1
const showRmDialog = (id: number) => {
	tempId = id
	isRmCommodityDialogShow.value = true
}

const countTotalPrice = () => {
	totalPrice.value = cartCommodities.value
		.filter(x => x.checkbox)
		.reduce((acc, curr) => acc + curr.price_unit * curr.count, 0);
}
const removeCommodity = (id: number) => {
	cartCommodities.value.splice(cartCommodities.value.findIndex(item => item.id === id), 1)
}
const reCCard = () => {
	swipeRefs.value[cartCommodities.value.findIndex(item => item.id === tempId)]?.close()
}

const cCheckboxChange = (state: any, label: any) => {
	const l = cartCommodities.value.map(i => i.checkbox).filter(x => x).length
	allSelected.value = l === cartCommodities.value.length;
	countTotalPrice()
}
const minus = (id: number) => {
	cartCommodities.value.forEach(
		item => {
			if (id === item.id) {
				if (item.count !== 1) {
					item.count -= 1
				} else {
					Taro.showToast({
						title: '最少购买一个商品~',
						icon: 'none',
						duration: 2000
					})
				}
			}
		}
	)
	countTotalPrice()
}
const plus = (id: number) => {
	cartCommodities.value.forEach(
		item => {
			if (id === item.id) {
				if (item.count !== item.maxBuy) {
					item.count = Number(item.count) + 1
				} else {
					Taro.showToast({
						title: '当前最多仅能购买' + item.maxBuy + '份该商品~',
						icon: 'none',
						duration: 2000
					})
				}
			}
		}
	)
	countTotalPrice()
}
//全选事件
const checkAll = () => {
	if (allSelected.value) {
		cartCommodities.value.forEach(item => {
			item.checkbox = true
		})
	} else {
		cartCommodities.value.forEach(item => {
			item.checkbox = false
		})
	}
}
//控制按箱下单还是散件提交
const submitType = ref(0)
//每箱件数
const boxNum = ref(1)
//提交订单提示框
const visibleSubmit = ref(false)
const onSubmit = () => {
	const checkCartCommodities = cartCommodities.value
		.filter(x => x.checkbox)
	console.log('订单信息', checkCartCommodities);
	Taro.navigateTo({
		url: '/package/package-a/confirmOrder/index',

	})
	Taro.preload({ message: checkCartCommodities })
}
//滚动加载
const scrollTop = ref(0)
const toView = ref('demo2')
const upper = (e: any) => {
	console.log('upper:', e)
}

const lower = (e: any) => {
	console.log('lower:', e)
}

const scroll = (e: any) => {
	console.log('scroll:', e)
}
//返回顶部
const backTop = () => {
	console.log('click')
}

//子组件返回值(动作面板)
let ClickshowDialogItem: null | number = null
const showDialog = (msg: any) => {
	console.log('子组件返回值:id', msg.id);
	console.log('子组件返回值:index', msg.index);
	ClickshowDialogItem = msg.id
	click()

}

const show = ref(false)
const click = () => {
	show.value = true
}

//搜索框相关事件
const searchValue = ref('')
const showSelect = ref(false)
const searchList: Ref<any[]> = ref([])
const searchListIndex = ref(1)
const DQsearchListIndex = ref(1)
const getProductByNumberF = async (item: Ref<string>) => {
	const { error, success } = await getProductByNumber({ name: item.value, per_page: '20' })
	// console.log('success                  ~~~', success);
	const { items, psize } = success as { items: Array<any>, psize: number }
	if (success && items) {
		searchList.value = items
		DQsearchListIndex.value = psize
	}
}
const onScrollBottom = async () => {
	console.log('触底了');
	// console.log('searchListIndex', searchListIndex.value);
	// console.log('DQsearchListIndex', DQsearchListIndex.value);


	if (Number(searchListIndex.value) < Number(DQsearchListIndex.value)) {
		searchListIndex.value++
		const { error, success } = await getProductByNumber({ name: searchValue.value, page: searchListIndex.value.toString(), per_page: '20' })
		if (error === null) {
			const { items, psize } = success as { items: Array<any>, psize: number }
			if (success && items) {
				searchList.value = searchList.value.concat(items)
				DQsearchListIndex.value = psize
			}
		}
	} else {
		Taro.showToast({
			title: '加载完毕了~',
			icon: 'error',
			duration: 2000
		})
	}

}

const GetInputFocus = () => {
	if (searchValue.value.length >= 0) {
		show.value = true
		if (searchValue.value.length > 0) {
			getProductByNumberF(searchValue)
		} else {
			searchList.value = []
			DQsearchListIndex.value = 1
		}

	} else {
		show.value = false
	}

}
const clickItem = (item: any) => {

	// console.log("item----",item._relatedInfo.anchorRelatedText);
	const { name, id } = item
	if (ClickshowDialogItem) {
		cartCommodities.value.find(item => {
			if (item.id === ClickshowDialogItem) {
				item.design = name
				item.fanhao_id = id
			}
		})
		ClickshowDialogItem = null
		searchValue.value = ''
		console.log('cartCommodities', cartCommodities.value);
		show.value = false
		searchList.value = []


	} else {
		Taro.showToast({
			title: '操作失败',
			icon: 'error',
			duration: 2000
		})
	}

}

//获取购物车数据
const getCart = async () => {
	const { error, success } = await getFindCart()
	if (error === null && success) {
		console.log(success);
		NumberCarts.value = success.items.length

	}
}
//购物车数量
const NumberCarts = ref(0)
const showAnimate = ref(false)

const AddPurchase = async () => {
	let checkCartCommodities = cartCommodities.value
		.filter(x => x.checkbox)
	console.log(checkCartCommodities, '~~~');
	checkCartCommodities = checkCartCommodities.map(i =>
		[
			i.id,
			i.count
		]
	)
	if (checkCartCommodities.length > 0) {
		let resultStr = '[' + checkCartCommodities.map(subArr => '(' + subArr.join(',') + ')').join(',') + ']';
		console.log(resultStr);

		const { error, success } = await updateFindCart({
			product_ids: resultStr
		})
		if (error === null) {
			Taro.showToast({
				title: '操作成功',
				icon: 'success',
				duration: 2000,
			})
			await getCart()
		} else {
			Taro.showToast({
				title: '操作失败',
				icon: 'error',
				duration: 2000
			})
		}
		console.log('~~~', error, success);


		console.log(checkCartCommodities);

	} else {
		Taro.showToast({
			title: '还未选购商品',
			icon: 'error',
			duration: 2000
		})
	}


}
const val = ref('')
const stateShow = reactive({
	showPreview: false,
	imgData: []
})
const longpressImage = (image: any) => {
	stateShow.showPreview = true;
	stateShow.imgData = [{ src: image }]

}
const findNewProduct = ref('')
const bindSearchValue = ref('')


</script>
<template >
	<basic-layout
		style="height: 100vh;min-height: 100vh;padding-bottom: 0px !important;bottom: 0px !important;position: relative !important;">
		<nut-sticky top="0">
			<nut-searchbar v-model="preloadData!.value" confirm-type="search" placeholder="请输入产品型号/名称/规格/料号">
				<template #rightout>
					<div @click="() => {
						CurrentPage = 1,
							cartCommodities.length = 0,
							getProductDetailF(preloadData!.value)
					}">查询</div>
				</template>
				<template #rightin>
					<Search2 />
				</template>
			</nut-searchbar>
		</nut-sticky>
		<div class="from-box">
			<nut-fixed-nav type="left" :position="{ top: '140px' }" v-show="PageScrollTop" @click="Taro.pageScrollTo({
				scrollTop: 0,
				duration: 300
			})">
				<template #btn>
					<span class="text">{{ "返回顶部" }}</span>
				</template>
			</nut-fixed-nav>

			<nut-action-sheet v-model:visible="show" class="myAction">
				<div style="height: 340px;">
					<nut-searchbar v-model="searchValue" shape="square" label="请输入楼层以搜索" input-background="#F0F0F0"
						@change="GetInputFocus" id="pop-target">
						<template #leftin>
							<Search2 />
						</template>
					</nut-searchbar>

					<div :class="{ toShowDiv: !showSelect }"
						style="width: 80%;height: 300px;background-color: #FFFFFF;background-color: #FFFFFF;position: absolute;left: 50%;transform:translateX(-50%); z-index:999;">
						<div>
							<div v-if="searchList.length > 0">
								<nut-list :list-data="searchList" :container-height="300"
									@scroll-bottom="onScrollBottom">
									<template #default="{ item }">
										<div class="list-item" @click="clickItem(item)">
											{{ item.name }}
										</div>
										<nut-divider :dashed="true" />
									</template>
								</nut-list>
							</div>
							<div v-else style="margin-top: 5%;color: #333333;font-weight: 900;">
								{{ '请输入相应正楼层编号后进行查询' }}
							</div>
						</div>

					</div>
				</div>
			</nut-action-sheet>
			<div v-if="true">
				<nut-dialog title="提交订单" content="确认提交订货信息并生成订单吗？" v-model:visible="visibleSubmit" @ok="onSubmit" />
				<card class="card">
					<nut-swipe-group lock style="width: 100vw" v-if="cartCommodities.length > 0">
						<!-- <nut-backtop height="90vh" style="margin-bottom: 60rpx;" :distance="30" :bottom="250"> -->
						<!-- <template #content> -->
						<nut-image-preview :show="stateShow.showPreview" :images="stateShow.imgData"
							@close="stateShow.showPreview = false" />
						<scroll-view :scroll-y="true" style="height: calc(100%-58rpx);margin-bottom: 60rpx;"
							@scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll" :scroll-top="scrollTop">
							<nut-swipe v-for="(item, index) in cartCommodities" ref="swipeRefs"
								style="margin-bottom: 10px;" :name="item.id.toString()" :key="item.id">
								<view
									style="display: flex; gap: 5px; width: 100vw;border-top-left-radius: 10px;border-bottom-left-radius: 10px;text-align: center;"
									class="my-boxShad">
									<nut-checkbox @change="cCheckboxChange" v-model="item.checkbox" icon-size="20"
										style="margin-left: .625rem;">


										<image :src="item.image" style="width: 100px; height: 100px;"
											@longpress="longpressImage(item.image)"></image>
										<!-- <nut-tag plain type="primary">{{ item.name.split('/')[0] }}</nut-tag> -->
									</nut-checkbox>
									<view style="display: flex; flex-direction: column; gap: 8px;padding: 5% 5% 5% 0">
										<view
											style="font-size: 14px;font-weight: 900;color: #4F4F4F;font-family: math;text-align: left;">

											<!-- <nut-tag color="#F7AA71" class="ml-3% mr-3%">{{ item.model }}</nut-tag> -->
											<div
												style="color:#DD5F73;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #DD5F73;margin-bottom: 1%;margin-right: 3%;">
												<i class="i-bx-crown"
													style="vertical-align: text-bottom;color: #DD5F73;font-weight: bold;"></i>
												{{ item.model }}
											</div>
											<div
												style="color:#4D74FA;font-weight: bold;display: inline-block;border-radius: 3px;padding: 1%;border: 1px solid #4D74FA;margin-bottom: 1%;">

												{{ item.name.split('/')[0] }}
											</div>

											<nut-tag color="#F7AA71" v-show="item.name.split('/')[1]">{{
												item.name.split('/')[1]
											}}</nut-tag>

										</view>
										<nut-tag color="#F2F2F2" text-color="#909090"
											class="text-ellipsis w-11.25rem break-all">{{
												item.spec
											}}</nut-tag>
										<view class="flex justify-between">
											<span>
												<nut-price :price="item.price_unit"
													style="color: #F36409;font-weight: 900;"></nut-price>
												<span class="unit color-#ED7976 pl-5px  font-900">/</span>
												<span class="unit color-#ED7976 font-900 font-size-10px">PCS</span>
											</span>
											<nut-tag color="#F2F2F2" text-color="#969696"> {{ item.code }} </nut-tag>
										</view>

										<DesignCell :design="item.design" :id="item.id" :index="index"
											@update:design="showDialog" class="noShow">
										</DesignCell>

									</view>
								</view>
								<view class="quantity-button-group">
									<!-- <IconFont @click="() => minus(item.id)" name="minus"
												class="button-cell button-minus">
											</IconFont>
											<view class="button-cell button-cell-middle ">{{ item.count }}</view>
											<IconFont @click="() => plus(item.id)" class="button-cell button-plus"
												name="plus">
											</IconFont> -->
									<nut-input-number v-model="item.count" :min="1" :max="200000" input-width="55"
										@blur="countTotalPrice()" @add="() => plus(item.id)"
										@reduce="() => minus(item.id)" />
								</view>
							</nut-swipe>
						</scroll-view>
						<!-- </template> -->
						<!-- </nut-backtop> -->
					</nut-swipe-group>
					<nut-empty v-else description="暂无数据" />
				</card>
			</div>

			<!-- 暂无数据 -->
			<div class="no-data" v-else>
				<nut-empty description="暂无该产品数据" />
			</div>

		</div>


		<div class="relative">
			<view ref="clearBottom" class="" style="
		position: fixed !important;
	    bottom: 0 !important;
		right:0;
		left:0;
		margin: 0 auto;
	    background-color: #ffffff;
	    display: flex;
	    justify-content: space-between;
	    width: 100vw;
	    align-items: center;
	    padding: .625rem;
	    border-top: 1px solid #dcdcdc;
		transform: translateZ(0);">
			<view @click="gotoshopping">

				<nut-badge :value="NumberCarts">
					<div style="text-align: center;margin-left: 1.5rem;">
						<i class="i-bx-cart-alt" style="font-size: 20px;"></i>
						<div style="font-size: 10px;font-family: serif;">{{ "购物车" }}</div>
					</div>
				</nut-badge>

			</view>
			<view style="display: flex; gap: 5px">
				<view style="font-weight: bold;">合计:
					<nut-price :price="totalPrice"></nut-price>

				</view>
			</view>
			<view style="margin-right: 10px">
				<nut-button class="left-button" @click="AddPurchase">
					<template #icon>
						<i class="i-bx-cart-add" style="font-size: 18px;"></i>
					</template>
				</nut-button>
				<nut-button class="right-button" type="primary" @click="goMakeOrder" :disabled="(cartCommodities.length === 0) || (cartCommodities
					.filter(x => x.checkbox).length === 0)">{{ '直接购买' }}</nut-button>
			</view>
		</view>
		</div>
	</basic-layout>
</template>

<style lang="scss">
page {
	height: 100vh !important;
}

.list-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin-bottom: 10px;
	height: 24px;
	//   background-color: #919191;
	//   border-radius: 10px;
	//   color:#F8F8F8;
	//   font-weight: 900;
	font-size: 16px;
	color: #616161;
	font-family: sans-serif;
}

.left-button {
	border: none;
	border-bottom-right-radius: 0;
	border-top-right-radius: 0;
	background-color: #FF8E4C !important;
	color: #FFF !important;
}

.right-button {
	border: none;
	border-bottom-left-radius: 0;
	border-top-left-radius: 0;
	background-color: #F56171 !important;
	color: #FFF !important;
}

.test {
	padding: 12px 0 12px 20px;
	border-top: 1px solid #eee;
}

.my-boxShad {
	box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}

.button-minus {
	// border: 1px solid #aba8a8;
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
	background-color: #F9FAFC;
}

.button-plus {
	// border: 1px solid #aba8a8;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
	background-color: #F9FAFC;
}

.button-cell-middle {
	// border-top: 1px solid #aba8a8;
	// border-bottom: 1px solid #aba8a8;
	background-color: #F9FAFC;
	text-align: center;
	line-height: 17px;
	margin: 0 5%;
}

.card {
	margin: 0 0;
}

.button-cell {
	min-width: 25px;
	height: 17px;
}

.quantity-button-group {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	position: absolute;
	bottom: 5px;
	right: 30px;
}

.bottom-card {
	position: fixed !important;
	bottom: 0px !important;
	background-color: #ffffff;
	display: flex;
	justify-content: space-between;
	margin-bottom: 0px !important;

	width: 100vw;
	align-items: center;
	padding: .625rem;
	border-top: 1px solid #dcdcdc;

}

.image-box {
	.nut-cell {
		padding-top: 0;
		padding-bottom: 0;
		margin: 0;
		border-radius: none;

	}
}

.nut-address-custom-buttom {
	width: 100%;
	height: 54px;
	padding: 6px 0px 0;
	border-top: 1px solid #f2f2f2;
}

.from-box {
	.nut-sku-stepper-count {
		padding-right: 1.25rem;
	}
}

.btn {
	width: 90%;
	height: 42px;
	line-height: 42px;
	margin: auto;
	text-align: center;
	background: linear-gradient(135deg, #fa2c19 0%, #fa6419 100%);
	border-radius: 21px;
	font-size: 15px;
	color: white;
}

.sku-operate-box {
	width: 100%;
	display: flex;
	padding: 8px 10px;
	box-sizing: border-box;
}

.sku-operate-item {
	flex: 1;
}

.sku-operate-item:first-child {
	border-top-left-radius: 20px;
	border-bottom-left-radius: 20px;
}

.sku-operate-item:last-child {
	border-top-right-radius: 20px;
	border-bottom-right-radius: 20px;
}

// .bottom-card {
// 	background-color: #ffffff;
// 	display: flex;
// 	justify-content: space-between;
// 	position: fixed;
// 	bottom: 0;
// 	width: 100vw;
// 	align-items: center;
// 	padding: .625rem;
// 	border-top: 1px solid #dcdcdc;
// }

.content-desc {
	font-size: 1rem;
	color: #000;
}

page {
	background-color: #F7F7F7 !important;
}

.nut-fixed-nav__btn {
	width: 60px !important;
}

.nut-checkbox__label {
	box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;
}

.noShow {
	visibility: hidden !important;
}
</style>
