"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[11],{3100:function(e,t,n){var l=n(1065),a=n(6944),i=n(8743),o=(n(3588),n(889)),c=(n(9932),n(6538)),u=(n(1808),n(3496)),r=(n(5279),n(3191)),s=n(3091),f=n(3221),d=n(6821),v=n(7011),p=n(5969),w=n(2827),k=n(6249),m=Object.defineProperty,b=Object.defineProperties,_=Object.getOwnPropertyDescriptors,h=Object.getOwnPropertySymbols,g=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,y=function(e,t,n){return t in e?m(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},W=function(e,t){for(var n in t||(t={}))g.call(t,n)&&y(e,n,t[n]);if(h){var l,a=(0,s.Z)(h(t));try{for(a.s();!(l=a.n()).done;){n=l.value;x.call(t,n)&&y(e,n,t[n])}}catch(e){a.e(e)}finally{a.f()}}return e},I=function(e,t){return b(e,_(t))},U=["id"],B={key:1,class:"nut-navbar__text"},C={class:"nut-navbar__title"},T={key:0,class:"nut-navbar__text"},O=(0,f.aZ)(I(W({},{name:"NutNavbar"}),{__name:"navbar.taro",props:{leftShow:{type:Boolean,default:!1},title:{default:""},titleIcon:{type:Boolean,default:!1},leftText:{default:""},desc:{default:""},fixed:{type:Boolean,default:!1},safeAreaInsetTop:{type:Boolean,default:!1},border:{type:Boolean,default:!1},placeholder:{type:Boolean,default:!0},zIndex:{default:10}},emits:["clickBack","clickTitle","clickIcon","clickRight","onClickBack","onClickTitle","onClickIcon","onClickRight"],setup:function(e,t){var n=t.emit,l=e,a=n,i=Math.random().toString(36).slice(-8),o=(0,d.iH)("auto"),c=(0,d.iH)(null),u=(0,f.Fl)((function(){var e="nut-navbar";return(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({},e,!0),"".concat(e,"--border"),l.border),"".concat(e,"--fixed"),l.fixed),"".concat(e,"--safe-area-inset-top"),l.safeAreaInsetTop)})),s=(0,f.Fl)((function(){return l.fixed&&l.placeholder?{height:o.value}:{}})),k=function(){(0,w.u)(c).then((function(e){o.value="".concat(e.height,"px")}),(function(){}))};(0,f.bv)((function(){l.fixed&&l.placeholder&&setTimeout((function(){k()}),100)}));var m=function(){a("clickBack"),a("onClickBack")},b=function(){a("clickTitle"),a("onClickTitle")},_=function(){a("clickIcon"),a("onClickIcon")},h=function(){a("clickRight"),a("onClickRight")};return function(e,t){return(0,f.wg)(),(0,f.iD)("view",{class:"nut-navbar--placeholder",style:(0,v.j5)(s.value)},[(0,f._)("view",{id:"navbarRef-"+(0,d.SU)(i),ref_key:"navbarRef",ref:c,class:(0,v.C_)(u.value),style:(0,v.j5)({zIndex:e.zIndex})},[(0,f._)("view",{class:"nut-navbar__left",onClick:m},[e.leftShow?(0,f.WI)(e.$slots,"left-show",{key:0},(function(){return[(0,f.Wm)((0,d.SU)(p.dv),{height:"12px",color:"#979797"})]})):(0,f.kq)("",!0),(0,f.Uk)(),e.leftText?((0,f.wg)(),(0,f.iD)("view",B,(0,v.zw)(e.leftText),1)):(0,f.kq)("",!0),(0,f.Uk)(),(0,f.WI)(e.$slots,"left")]),(0,f.Uk)(),(0,f._)("view",C,[e.title?((0,f.wg)(),(0,f.iD)("view",{key:0,class:"title",onClick:b},(0,v.zw)(e.title),1)):(0,f.kq)("",!0),(0,f.Uk)(),e.titleIcon?((0,f.wg)(),(0,f.iD)("view",{key:1,class:"icon",onClick:_},[(0,f.WI)(e.$slots,"title-icon",{onClick:_})])):(0,f.kq)("",!0),(0,f.Uk)(),(0,f.WI)(e.$slots,"content")]),(0,f.Uk)(),(0,f._)("view",{class:"nut-navbar__right",onClick:h},[e.desc?((0,f.wg)(),(0,f.iD)("view",T,(0,v.zw)(e.desc),1)):(0,f.kq)("",!0),(0,f.Uk)(),(0,f.WI)(e.$slots,"right")])],14,U)],4)}}}));(0,k.w)(O);n(3939);var Z=n(1959),j=n.n(Z),V=44,z=(0,f.aZ)({__name:"index",setup:function(e){var t=(0,Z.getEnv)(),n="WEB"===t?{statusBarHeight:0}:(0,Z.getWindowInfo)(),l=n.statusBarHeight||0,a=l+V;return function(e,t){var n=O;return(0,f.wg)(),(0,f.iD)(f.HY,null,[(0,f._)("div",{class:"w-full",style:(0,v.j5)({height:(0,d.SU)(Z.pxTransform)(a)})},null,4),(0,f._)("div",{style:(0,v.j5)({paddingTop:(0,d.SU)(Z.pxTransform)((0,d.SU)(l))}),class:"w-full fixed-lt z-10 bg-_wn_fff"},[(0,f.Wm)(n,(0,f.dG)({class:"custom-navbar"},e.$attrs,{onOnClickBack:t[0]||(t[0]=function(e){return(0,d.SU)(Z.navigateBack)()})}),(0,f.Nv)({"left-show":(0,f.w5)((function(){return[t[1]||(t[1]=(0,f._)("div",{class:"i-ph-caret-left-bold text-22px text-_wn_000"},null,-1))]})),_:2},[(0,f.Ko)(Object.keys(e.$slots),(function(t){return{name:t,fn:(0,f.w5)((function(n){return[(0,f.WI)(e.$slots,t,(0,v.vs)((0,f.F4)(n||{})))]}))}}))]),1040)],4)],64)}}});const P=z;var S=P,N={class:"verifi-box"},H={class:"verifi-titleBox relative"},$={class:"verifi-title ml-2 mr-2"},D={class:"verifi-desc ml-2 mr-2"},R={class:"verifi-form ml-2 mr-2"},q=(0,f.aZ)({__name:"index",setup:function(e){(0,f.bv)((function(){}));var t=(0,d.iH)(1),n=(0,d.iH)(new Map([[1,{title:"\u4fe1\u606f\u586b\u5199\u4e2d\u2026",desc:"\u8bf7\u586b\u5199\u6838\u9a8c\u4fe1\u606f,\u611f\u8c22\u60a8\u7684\u652f\u6301"}],[2,{title:"\u8d44\u8d28\u5ba1\u6838\u4e2d\u2026",desc:"\u60a8\u6240\u63d0\u4ea4\u7684\u7ecf\u9500\u5546\u8d44\u8d28\u6b63\u5728\u5ba1\u6838\u4e2d\uff0c\u8bf7\u8010\u5fc3\u7b49\u5f85"}],[3,{title:"\u8d44\u8d28\u5ba1\u6838\u901a\u8fc7",desc:"\u606d\u559c\u60a8\uff0c\u60a8\u7684\u7ecf\u9500\u5546\u8d44\u8d28\u5ba1\u6838\u901a\u8fc7"}]])),l=(0,d.iH)({corporateName:"",contactName:"",contactPhone:"",Qualifications:""}),r=function(){j().showLoading({title:"\u63d0\u4ea4\u4e2d"}),setTimeout((function(){j().hideLoading(),j().showToast({title:"\u63d0\u4ea4\u6210\u529f",icon:"success",duration:2e3}),t.value=2}),2e3)};return function(e,s){var d=S,p=u.Z,w=c.Z,k=o.Z,m=i.Z,b=a.Z;return(0,f.wg)(),(0,f.j4)(b,null,{default:(0,f.w5)((function(){return[(0,f.Wm)(d,{title:"\u6838\u9a8c\u4fe1\u606f","left-show":""}),(0,f._)("div",N,[(0,f._)("div",H,[(0,f._)("view",$,(0,v.zw)(n.value.get(t.value).title),1),(0,f._)("view",D,(0,v.zw)(n.value.get(t.value).desc),1)]),(0,f._)("div",R,[(0,f.Wm)(m,null,{default:(0,f.w5)((function(){return[(0,f.Wm)(w,{label:"\u7ecf\u9500\u5546\u5168\u79f0"},{default:(0,f.w5)((function(){return[(0,f.Wm)(p,{modelValue:l.value.corporateName,"onUpdate:modelValue":s[0]||(s[0]=function(e){return l.value.corporateName=e}),placeholder:"\u8bf7\u8f93\u5165\u5168\u79f0",type:"text",disabled:1!=t.value},null,8,["modelValue","disabled"])]})),_:1}),(0,f.Wm)(w,{label:"\u8054\u7cfb\u4eba"},{default:(0,f.w5)((function(){return[(0,f.Wm)(p,{modelValue:l.value.contactName,"onUpdate:modelValue":s[1]||(s[1]=function(e){return l.value.contactName=e}),placeholder:"\u8bf7\u8f93\u5165\u8054\u7cfb\u4eba",type:"text",disabled:1!=t.value},null,8,["modelValue","disabled"])]})),_:1}),(0,f.Wm)(w,{label:"\u8054\u7cfb\u7535\u8bdd"},{default:(0,f.w5)((function(){return[(0,f.Wm)(p,{modelValue:l.value.contactPhone,"onUpdate:modelValue":s[2]||(s[2]=function(e){return l.value.contactPhone=e}),placeholder:"\u8bf7\u8f93\u5165\u8054\u7cfb\u7535\u8bdd",type:"text",disabled:1!=t.value},null,8,["modelValue","disabled"])]})),_:1}),(0,f.Wm)(w,{label:"\u8054\u7cfb\u5730\u5740"},{default:(0,f.w5)((function(){return[(0,f.Wm)(p,{modelValue:l.value.Qualifications,"onUpdate:modelValue":s[3]||(s[3]=function(e){return l.value.Qualifications=e}),placeholder:"\u8bf7\u8f93\u5165\u5730\u5740",type:"text",disabled:1!=t.value},null,8,["modelValue","disabled"])]})),_:1}),(0,f.Wm)(k,{type:"info",size:"large",class:"verifi-button",block:"",onClick:r,disabled:1!=t.value},{default:(0,f.w5)((function(){return s[4]||(s[4]=[(0,f.Uk)((0,v.zw)("\u63d0\u4ea4"))])})),_:1},8,["disabled"])]})),_:1})])])]})),_:1})}}});const E=q;var F=E,Q={navigationBarTitleText:"\u6838\u9a8c\u4fe1\u606f"};Page((0,l.createPageConfig)(F,"package/package-a/verification/index",{root:{cn:[]}},Q||{}))}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[107,216,592],(function(){return t(3100)}));e.O()}]);