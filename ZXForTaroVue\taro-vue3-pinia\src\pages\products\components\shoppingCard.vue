<template>
    <template v-for="state in searchList" :key="state.itemNumber">
        <div class="product-card" @click="gotoFind(state.itemNumber, state.itemNumber)">
            <div class="card-content">
                <!-- 产品图片 -->
                <div class="image-container">
                    <image :src="state.imgUrl" mode="aspectFit" class="product-image" />
                    <!-- 销量标签 -->
                    <div class="rank-badge">
                        <i class="i-bx-crown"></i>
                        {{ state.shopDesc }}
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="product-info">
                    <!-- 产品标签 -->
                    <div class="tags-container">
                        <nut-tag
                            v-if="state.title.split('/')[0]"
                            color="#122F38"
                            text-color="#fff"
                            class="model-tag"
                        >
                            {{ state.title.split('/')[0] }}
                        </nut-tag>
                        <nut-tag
                            v-if="state.title.split('/')[1]"
                            color="rgba(18, 47, 56, 0.1)"
                            text-color="#122F38"
                            class="name-tag"
                        >
                            {{ state.title.split('/')[1] }}
                        </nut-tag>
                    </div>

                    <!-- 产品编号 -->
                    <div class="item-number">
                        <nut-tag color="#FF6B35" text-color="#fff" class="code-tag">
                            {{ state.itemNumber }}
                        </nut-tag>
                    </div>

                    <!-- 价格信息 -->
                    <div class="price-container">
                        <span class="price">{{ state.price }}</span>
                        <span class="unit">/PCS</span>
                    </div>
                </div>
            </div>
        </div>
    </template>
</template>
<script setup lang="ts">
import { onMounted, ref, watch, watchEffect } from 'vue'
import { defineProps } from 'vue';
import { getProduct, hotProduct } from '@/service/index'
import Taro from '@tarojs/taro';
const props = defineProps({
    state: Array
})
const gotoFind = async (i: any, name: string) => {
    //发起网络请求
    const { error, success }: { error: any, success: any } = await getProduct({ code: i })
    console.log(error, success);
    if (error == null) {
        //跳转产品详情页
        Taro.preload({ model: name })
        Taro.navigateTo({
            url: '/package/package-a/Details/index?model=' + name,
            fail: (res: any) => {
                console.log(res)
            }


        })
        console.log('当前分类数据')
    } else {
        Taro.showToast({
            title: '暂无数据',
            icon: 'error',
            duration: 2000
        })
    }

}
const name = ref('')
const searchList = ref([])
watch([props], async () => {
    console.log('变化了');

    await getProductF()
})
const getProductF = async () => {
    const { error, success } = await hotProduct()
    if (error == null && success) {
        console.log('success~~~~~~', success.items);

        searchList.value = success.items.map((i: { image: any; model: string; name: string; price_unit: string; code: any; }, index: number) => {
            return {
                imgUrl: i.image,
                title: i.model + '/' + i.name,
                price: '￥' + i.price_unit,
                name: i.name,
                state: i.code,
                shopDesc: `销量TOP` + Number(index + 1),
                tag: '',
                itemNumber: i.code
            }

        })
        // console.log('shopcard', success);
        // i.image=success.items[0].image
        // state.value.imgUrl = success.items[0].image
        // state.value.title = success.items[0].model + '/' + success.items[0].name
        // state.value.price = '￥' + success.items[0].price_unit
        // name.value = success.items[0].name
        // state.value.itemNumber = success.items[0].code

        // i.product_id=success.items[0].id
    }

}
onMounted(async () => {
    await getProductF()
})


const state = ref({
    imgUrl:
        'https://img01.71360.com/file/read/www/M00/41/57/rBwBHmTWAw-AFdtTAAEdAOpPUAo680.png?w=600',
    title: 'ZX1650A/单左/集电集控型消防应急标志灯具',
    price: '￥ 388',
    shopDesc: '销量TOP1',
    tag: '促销中',
    itemNumber: ''

})
</script>
<style lang="scss">
.product-card {
    width: 30%;
    padding: 1%;
    margin-bottom: 2%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(18, 47, 56, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 4px 20px rgba(18, 47, 56, 0.15);
    }
}

.card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.image-container {
    position: relative;
    width: 100%;
    margin-bottom: 8px;

    .product-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 4px;
    }

    .rank-badge {
        position: absolute;
        top: 0;
        left: 0;
        background: linear-gradient(135deg, #122F38 0%, #1a4249 100%);
        color: #fff;
        font-size: 10px;
        font-weight: bold;
        padding: 4px 6px;
        border-radius: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 2px;

        i {
            font-size: 12px;
            color: #FFD700;
        }
    }
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.tags-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 4px;

    .model-tag {
        font-size: 11px;
        font-weight: 600;
        border-radius: 3px;
        align-self: flex-start;
    }

    .name-tag {
        font-size: 10px;
        font-weight: 500;
        border-radius: 3px;
        align-self: flex-start;
        border: 1px solid #122F38;
    }
}

.item-number {
    margin-bottom: 4px;

    .code-tag {
        font-size: 10px;
        font-weight: 600;
        border-radius: 3px;
    }
}

.price-container {
    margin-top: auto;
    display: flex;
    align-items: baseline;
    gap: 2px;

    .price {
        color: #122F38;
        font-weight: bold;
        font-size: 16px;
    }

    .unit {
        color: #FF6B35;
        font-size: 10px;
        font-weight: 600;
    }
}

// 响应式调整
@media (max-width: 750px) {
    .product-card {
        width: 48%;
    }
}

@media (max-width: 480px) {
    .product-card {
        width: 100%;
        margin: 0 0 12px 0;
    }
}
</style>