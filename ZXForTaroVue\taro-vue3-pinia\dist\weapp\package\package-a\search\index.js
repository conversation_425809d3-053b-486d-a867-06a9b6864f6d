"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[457],{7701:function(e,n,t){var a=t(1065),s=t(2344),i=(t(2240),t(889)),l=(t(9932),t(8701)),c=(t(959),t(7369)),o=(t(9251),t(9841)),u=(t(30),t(3496)),r=(t(5279),t(5841)),d=t(8751),p=(t(2148),t(8187)),v=(t(3796),t(8400)),f=(t(6e3),t(9328)),_=(t(8277),t(3193)),m=(t(9157),t(9909)),g=(t(3362),t(2419)),h=t(8140),w=t(2810),b=t(3221),x=t(6821),k=t(7011),y=t(2e3),Z=t(4733),z=t(1959),C=t.n(z),V=t(5969),q={class:"search-page"},H={class:"search-header"},W={class:"search-container"},U={class:"search-wrapper"},S={class:"search-container"},T={class:"search-results-container"},D={class:"search-results"},L={key:0,class:"results-list"},P=["onClick"],O={class:"floor-info"},A={class:"floor-name"},I={key:1,class:"empty-search"},B={class:"products-section"},N={key:0,class:"section-header"},R={class:"product-count"},F={class:"products-content"},J={class:"product-card"},j={class:"product-main-info"},E={class:"product-checkbox-section"},K={class:"product-image-section"},Q={class:"image-container"},M=["src"],Y={key:0,class:"image-overlay"},G={class:"product-info-section"},X={class:"product-title-area"},$={class:"model-tag"},ee={class:"name-tag"},ne={class:"product-spec"},te={class:"spec-text"},ae={class:"price-code-section"},se={class:"price-area"},ie={class:"price"},le={class:"code-area"},ce={class:"code-tag"},oe={class:"product-note-section"},ue={class:"quantity-section"},re={class:"quantity-control"},de={key:1,class:"empty-products"},pe={class:"bottom-action-bar"},ve={class:"action-bar-content"},fe={class:"price-summary"},_e={class:"total-price-section"},me={class:"total-price"},ge={class:"submit-section"},he={__name:"index",setup:function(e){var n=(0,x.iH)(""),t=(0,x.iH)(1),a=(0,x.iH)(1),z=function(){var e=(0,w.Z)((0,g.Z)().mark((function e(){var s,i,l,c,o,u=arguments;return(0,g.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return s=u.length>0&&void 0!==u[0]&&u[0],s&&(he.value=[]),C().showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0}),e.next=5,(0,Z.wv)({name:n.value,page:t.value});case 5:i=e.sent,l=i.error,c=i.success,null===l&&(a.value=c.psize,t.value=c.cur_page,(o=he.value).push.apply(o,(0,h.Z)(c.items)),he.value=he.value.map((function(e){return e.product_qty=1,e.zx_line_notes="",e.design="",e.fanhao_id="",e.icon="new",e}))),C().hideLoading(),console.log("SearchList",he.value);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),he=(0,x.iH)([]),we=((0,x.iH)(0),function(e,n){var t=he.value.map((function(e){return e.checkbox})).filter((function(e){return e})).length;Le.value=t===he.value.length,Ze()}),be=null,xe=function(e){console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:id",e.id),console.log("\u5b50\u7ec4\u4ef6\u8fd4\u56de\u503c:index",e.index),be=e.id,ye()},ke=(0,x.iH)(!1),ye=function(){ke.value=!0},Ze=function(){Pe.value=he.value.filter((function(e){return e.checkbox})).reduce((function(e,n){return e+n.price_unit*n.product_qty}),0)},ze=function(e){console.log("minus",e),he.value.forEach((function(n){e===n.id&&(1!==n.product_qty?n.product_qty-=1:C().showToast({title:"\u6700\u5c11\u8d2d\u4e70\u4e00\u4e2a\u5546\u54c1~",icon:"none",duration:2e3}))})),Ze()},Ce=function(e){console.log("plus",e),he.value.forEach((function(n){e===n.id&&(999!==n.product_qty?n.product_qty=Number(n.product_qty)+1:C().showToast({title:"\u5f53\u524d\u6700\u591a\u4ec5\u80fd\u8d2d\u4e70999\u4efd\u8be5\u5546\u54c1~",icon:"none",duration:2e3}))})),Ze()},Ve=(0,x.iH)(""),qe=((0,x.iH)(!1),(0,x.iH)([])),He=(0,x.iH)(1),We=(0,x.iH)(1),Ue=function(){var e=(0,w.Z)((0,g.Z)().mark((function e(n){var t,a,s,i;return(0,g.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,Z.yS)({name:n,per_page:"20"});case 2:t=e.sent,t.error,a=t.success,console.log("success                  ~~~",a),s=a.items,i=a.psize,a&&s&&(qe.value=s,We.value=i);case 8:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Se=function(){var e=(0,w.Z)((0,g.Z)().mark((function e(){var n,t,a;return(0,g.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("\u89e6\u5e95\u4e86"),console.log("searchListIndex",He.value),console.log("DQsearchListIndex",We.value),!(Number(He.value)<Number(We.value))){e.next=13;break}return He.value++,e.next=7,(0,Z.yS)({name:qe.value,page:He.value.toString(),per_page:"20"});case 7:n=e.sent,t=n.error,a=n.success,null===t&&a&&items&&(qe.value=qe.value.concat(items),We.value=psize),e.next=14;break;case 13:C().showToast({title:"\u52a0\u8f7d\u5b8c\u6bd5\u4e86~",icon:"error",duration:2e3});case 14:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Te=function(){qe.value.length>=0?(ke.value=!0,Ve.value.length>0?Ue(Ve.value):(qe.value=[],We.value=1)):ke.value=!1},De=function(e){var n=e.name,t=e.id;be?(he.value.find((function(e){e.id===be&&(e.design=n,e.fanhao_id=t)})),be=null,Ve.value="",console.log("cartCommodities",he.value),ke.value=!1,qe.value=[]):C().showToast({title:"\u64cd\u4f5c\u5931\u8d25",icon:"error",duration:2e3})},Le=(0,x.iH)(!1),Pe=(0,x.iH)(0),Oe=function(){console.log("event cancel")},Ae=function(){var e=(0,w.Z)((0,g.Z)().mark((function e(){var n,t,a,s,i;return(0,g.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("event ok"),n=he.value.filter((function(e){return!0===e.checkbox})),console.log("checkList",n),t=n.find((function(e){return e.name.includes("\u7279\u6b8a\u56fe\u6848")&&e.design.length<=0})),!t){e.next=8;break}C().showModal({title:"\u63d0\u793a",content:"\u7279\u6b8a\u56fe\u6848\u4ea7\u54c1\u9700\u9009\u62e9\u697c\u5c42\u56fe\u6848\u540e\u624d\u53ef\u4e0b\u5355",success:function(e){}}),e.next=15;break;case 8:return n=n.map((function(e){return{product_id:e.id,product_qty:e.product_qty,price_unit:e.price_unit,zx_line_notes:e.zx_line_notes,fanhao_id:e.fanhao_id}})),e.next=11,(0,Z.Cs)({add_lines:JSON.stringify(n)},{id:C().getCurrentInstance().preloadData.OrderId});case 11:a=e.sent,s=a.error,i=a.success,null===s&&i?(console.log("success---",i),C().showToast({title:"\u63d0\u4ea4\u6210\u529f",success:function(){setTimeout((function(){var e=C().getCurrentPages(),t=e[e.length-2];t.setData({checkList:n}),C().navigateBack({delta:1})}),2e3)},duration:2e3})):C().showToast({title:s.message,icon:"error",duration:2e3});case 15:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ie=(0,x.iH)(!1),Be=(0,x.iH)(!1);C().useReachBottom((0,w.Z)((0,g.Z)().mark((function e(){return(0,g.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:Ne();case 1:case"end":return e.stop()}}),e)}))));var Ne=function(){console.log(t.value,a.value),t.value<a.value?(t.value=Number(t.value)+1,z()):C().showToast({title:"\u5168\u90e8\u52a0\u8f7d\u5b8c\u6bd5~",icon:"success",duration:2e3})};C().usePageScroll((function(e){e.scrollTop>=528?Re.value=!0:Re.value=!1}));var Re=(0,x.iH)(!1),Fe=function(e){var n=C().getCurrentInstance().preloadData.existingProductList,t=n.find((function(n){return n.id===e}))||null;console.log(t,"exist"),null!=t&&(Be.value=!0)};return function(e,t){var a=m.Z,g=_.Z,h=f.Z,w=v.Z,Z=p.Z,be=d.Z,ye=r.Z,He=u.Z,We=o.Z,Ue=c.Z,Le=l.Z,Ne=i.Z,Je=s.Z;return(0,b.wg)(),(0,b.iD)("div",q,[(0,b.wy)((0,b.Wm)(a,{type:"left",position:{top:"140px"},onClick:t[0]||(t[0]=function(e){return(0,x.SU)(C()).pageScrollTo({scrollTop:0,duration:300})})},{btn:(0,b.w5)((function(){return t[9]||(t[9]=[(0,b._)("span",{class:"text"},(0,k.zw)("\u8fd4\u56de\u9876\u90e8"),-1)])})),_:1},512),[[y.F8,Re.value]]),(0,b._)("div",H,[(0,b.Wm)(h,{top:"0"},{default:(0,b.w5)((function(){return[(0,b._)("div",W,[(0,b._)("div",U,[(0,b.Wm)(g,{modelValue:n.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return n.value=e}),"confirm-type":"search",placeholder:"\u8bf7\u8f93\u5165\u4ea7\u54c1\u578b\u53f7/\u540d\u79f0/\u89c4\u683c/\u6599\u53f7",class:"custom-searchbar"},{rightout:(0,b.w5)((function(){return[(0,b._)("div",{class:"search-btn",onClick:t[1]||(t[1]=function(e){return z(!0)})},t[10]||(t[10]=[(0,b._)("i",{class:"i-bx-search"},null,-1),(0,b._)("span",null,"\u67e5\u8be2",-1)]))]})),rightin:(0,b.w5)((function(){return[(0,b.Wm)((0,x.SU)(V.LP))]})),_:1},8,["modelValue"])])])]})),_:1})]),(0,b.Wm)(Z,{visible:ke.value,"onUpdate:visible":t[4]||(t[4]=function(e){return ke.value=e}),class:"floor-search-sheet"},{default:(0,b.w5)((function(){return[(0,b._)("div",S,[t[14]||(t[14]=(0,b._)("div",{class:"search-header"},[(0,b._)("h3",{class:"search-title"},"\u9009\u62e9\u697c\u5c42\u56fe\u6848"),(0,b._)("p",{class:"search-subtitle"},"\u8bf7\u8f93\u5165\u697c\u5c42\u7f16\u53f7\u8fdb\u884c\u641c\u7d22")],-1)),(0,b.Wm)(g,{modelValue:Ve.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return Ve.value=e}),shape:"round",placeholder:"\u8bf7\u8f93\u5165\u697c\u5c42\u4ee5\u641c\u7d22","input-background":"#f8f9fa",onChange:Te,id:"pop-target",class:"custom-searchbar"},{leftin:(0,b.w5)((function(){return[(0,b.Wm)((0,x.SU)(V.LP))]})),_:1},8,["modelValue"]),(0,b._)("div",T,[(0,b._)("div",D,[qe.value.length>0?((0,b.wg)(),(0,b.iD)("div",L,[(0,b.Wm)(w,{"list-data":qe.value,"container-height":280,onScrollBottom:Se},{default:(0,b.w5)((function(e){var n=e.item;return[(0,b._)("div",{class:"floor-item",onClick:function(e){return De(n)}},[(0,b._)("div",O,[t[11]||(t[11]=(0,b._)("i",{class:"i-bx-building-house floor-icon"},null,-1)),(0,b._)("span",A,(0,k.zw)(n.name),1)]),t[12]||(t[12]=(0,b._)("i",{class:"i-bx-chevron-right select-icon"},null,-1))],8,P)]})),_:1},8,["list-data"])])):((0,b.wg)(),(0,b.iD)("div",I,t[13]||(t[13]=[(0,b._)("div",{class:"empty-icon"},[(0,b._)("i",{class:"i-bx-search-alt"})],-1),(0,b._)("p",{class:"empty-text"},"\u8bf7\u8f93\u5165\u76f8\u5e94\u6b63\u786e\u697c\u5c42\u7f16\u53f7\u540e\u8fdb\u884c\u67e5\u8be2",-1)])))])])])]})),_:1},8,["visible"]),(0,b._)("div",B,[he.value.length>0?((0,b.wg)(),(0,b.iD)("div",N,[t[15]||(t[15]=(0,b._)("i",{class:"i-bx-package section-icon"},null,-1)),t[16]||(t[16]=(0,b._)("span",{class:"section-title"},"\u641c\u7d22\u7ed3\u679c",-1)),(0,b._)("span",R,"("+(0,k.zw)(he.value.length)+"\u4ef6)",1)])):(0,b.kq)("",!0),(0,b._)("div",F,[he.value.length>0?((0,b.wg)(),(0,b.j4)(Le,{key:0,lock:"",class:"product-swipe-group"},{default:(0,b.w5)((function(){return[((0,b.wg)(!0),(0,b.iD)(b.HY,null,(0,b.Ko)(he.value,(function(e,n){return(0,b.wg)(),(0,b.j4)(Ue,{ref_for:!0,ref:"swipeRefs",class:"product-swipe",name:e.id.toString(),key:e.id},{default:(0,b.w5)((function(){return[(0,b._)("div",J,[(0,b._)("div",j,[(0,b._)("div",E,[(0,b.Wm)(be,{onChange:we,modelValue:e.checkbox,"onUpdate:modelValue":function(n){return e.checkbox=n},"icon-size":"18",onClickOnce:function(n){return Fe(e.id)},class:"custom-checkbox"},null,8,["modelValue","onUpdate:modelValue","onClickOnce"])]),(0,b._)("div",K,[(0,b._)("div",Q,[(0,b._)("image",{src:e.image||"https://tse2-mm.cn.bing.net/th/id/OIP-C.exoIucrWcex80_QKk3z5DAAAAA?w=181&h=182&c=7&r=0&o=5&dpr=1.3&pid=1.7",class:"product-image"},null,8,M),e.image?(0,b.kq)("",!0):((0,b.wg)(),(0,b.iD)("div",Y,t[17]||(t[17]=[(0,b._)("i",{class:"i-bx-image-alt"},null,-1)])))])]),(0,b._)("div",G,[(0,b._)("div",X,[(0,b._)("div",$,[t[18]||(t[18]=(0,b._)("i",{class:"i-bx-crown"},null,-1)),(0,b._)("span",null,(0,k.zw)(e.model),1)]),(0,b._)("div",ee,(0,k.zw)(e.name.split("/")[0]),1),(0,b.wy)((0,b._)("div",{class:"sub-name-tag"},(0,k.zw)(e.name.split("/")[1]),513),[[y.F8,e.name.split("/")[1]]])]),(0,b._)("div",ne,[(0,b._)("span",te,(0,k.zw)(e.spec),1)]),(0,b._)("div",ae,[(0,b._)("div",se,[t[19]||(t[19]=(0,b._)("span",{class:"currency"},"\xa5",-1)),(0,b._)("span",ie,(0,k.zw)(e.price_unit),1),t[20]||(t[20]=(0,b._)("span",{class:"unit"},"/PCS",-1))]),(0,b._)("div",le,[(0,b._)("span",ce,(0,k.zw)(e.code),1)])])])]),(0,b.Wm)(ye,{design:e.design,id:e.id,index:n,name:e.name.split("/")[0],class:(0,k.C_)([{noShow:!e.name.split("/")[0].includes("\u7279\u6b8a\u56fe\u6848")},"design-cell"]),"onUpdate:design":xe},null,8,["design","id","index","name","class"]),(0,b._)("div",oe,[t[22]||(t[22]=(0,b._)("div",{class:"note-label"},[(0,b._)("i",{class:"i-bx-message-square-detail"}),(0,b._)("span",null,"\u5546\u54c1\u5907\u6ce8")],-1)),(0,b.Wm)(He,{modelValue:e.zx_line_notes,"onUpdate:modelValue":function(n){return e.zx_line_notes=n},"max-length":50,placeholder:"\u8bf7\u8f93\u5165\u5546\u54c1\u5907\u6ce8\uff08\u4e0b\u5355\u540e\u65e0\u6cd5\u8ffd\u52a0\uff09",class:"note-input"},{right:(0,b.w5)((function(){return t[21]||(t[21]=[(0,b._)("i",{class:"i-bx-edit-alt note-edit-icon"},null,-1)])})),_:2},1032,["modelValue","onUpdate:modelValue"])]),(0,b._)("div",ue,[t[23]||(t[23]=(0,b._)("div",{class:"quantity-label"},"\u6570\u91cf",-1)),(0,b._)("div",re,[(0,b.Wm)(We,{modelValue:e.product_qty,"onUpdate:modelValue":function(n){return e.product_qty=n},min:1,max:2e5,onBlur:t[5]||(t[5]=function(e){return Ze()}),onAdd:function(){return Ce(e.id)},onReduce:function(){return ze(e.id)},class:"custom-input-number"},null,8,["modelValue","onUpdate:modelValue","onAdd","onReduce"])])])])]})),_:2},1032,["name"])})),128))]})),_:1})):((0,b.wg)(),(0,b.iD)("div",de,t[24]||(t[24]=[(0,b._)("div",{class:"empty-icon"},[(0,b._)("i",{class:"i-bx-package"})],-1),(0,b._)("p",{class:"empty-text"},"\u6682\u65e0\u67e5\u8be2\u7ed3\u679c",-1)])))])]),t[27]||(t[27]=(0,b._)("div",{class:"bottom-spacer"},null,-1)),(0,b._)("div",pe,[(0,b._)("div",ve,[(0,b._)("div",fe,[(0,b._)("div",_e,[t[25]||(t[25]=(0,b._)("span",{class:"total-label"},"\u5408\u8ba1:",-1)),(0,b._)("span",me,"\xa5"+(0,k.zw)(Pe.value.toFixed(2)),1)])]),(0,b._)("div",ge,[(0,b.Wm)(Ne,{type:"primary",onClick:t[6]||(t[6]=function(e){return Ie.value=!0}),disabled:0===he.value.length||0===he.value.filter((function(e){return e.checkbox})).length,class:"submit-btn"},{default:(0,b.w5)((function(){return t[26]||(t[26]=[(0,b._)("i",{class:"i-bx-plus"},null,-1),(0,b._)("span",null,"\u65b0\u589e\u8ba2\u5355",-1)])})),_:1},8,["disabled"])])])]),(0,b.Wm)(Je,{title:"\u65b0\u589e\u8ba2\u5355",content:"\u8bf7\u786e\u5b9a\u662f\u5426\u65b0\u589e\u76f8\u5173\u4ea7\u54c1\u5230\u8ba2\u5355",visible:Ie.value,"onUpdate:visible":t[7]||(t[7]=function(e){return Ie.value=e}),onCancel:Oe,onOk:Ae,class:"custom-dialog"},null,8,["visible"]),(0,b.Wm)(Je,{"no-cancel-btn":"",title:"\u6e29\u99a8\u63d0\u793a",visible:Be.value,"onUpdate:visible":t[8]||(t[8]=function(e){return Be.value=e}),"ok-text":"\u6211\u77e5\u9053\u4e86",content:"\u60a8\u6240\u52fe\u9009\u7684\u4ea7\u54c1\u5728\u5f53\u524d\u8ba2\u5355\u4e2d\u5df2\u5df2\u5b58\u5728\uff0c\u8bf7\u786e\u8ba4\u6838\u5b9e\u8ba2\u5355\uff1f",class:"custom-dialog"},null,8,["visible"])])}}};const we=he;var be=we,xe={navigationBarTitleText:"\u65b0\u589e\u67e5\u8be2"};Page((0,a.createPageConfig)(be,"package/package-a/search/index",{root:{cn:[]}},xe||{}))}},function(e){var n=function(n){return e(e.s=n)};e.O(0,[91,107,216,592],(function(){return n(7701)}));e.O()}]);