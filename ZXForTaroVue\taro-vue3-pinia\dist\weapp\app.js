"use strict";require("./common"),require("./vendors"),require("./taro"),require("./runtime"),(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[143],{5466:function(e,a,n){n(4322),n(2051);var t=n(1065),i=n(27),o=n(1959),r=n.n(o),s=n(2e3),c=n(4081);function d(){}var p=(0,s.ri)({onShow:function(){r().cloud.init({env:"success1015-1gn83n2ka7f9103e"}),r().getStorage({key:"cart",fail:function(){console.log("cart\u4e0d\u5b58\u5728"),r().setStorage({key:"cart",data:{}})}});var e=function(){if(r().canIUse("getUpdateManager")){var e=r().getUpdateManager();console.log(e,"updateManager"),e.onCheckForUpdate((function(a){console.log("onCheckForUpdate====",a),a.hasUpdate&&(e.onUpdateReady((function(){r().showModal({title:"\u66f4\u65b0\u63d0\u793a",content:"\u65b0\u7248\u672c\u5df2\u7ecf\u51c6\u5907\u597d\uff0c\u662f\u5426\u91cd\u542f\u5e94\u7528\uff1f",success:function(a){console.log("success====",a),a.confirm&&e.applyUpdate()}})})),e.onUpdateFailed((function(){r().showModal({title:"\u5df2\u7ecf\u6709\u65b0\u7248\u672c\u4e86\u54df~",content:"\u65b0\u7248\u672c\u5df2\u7ecf\u4e0a\u7ebf\u5566~\uff0c\u8bf7\u60a8\u5220\u9664\u5f53\u524d\u5c0f\u7a0b\u5e8f\uff0c\u91cd\u65b0\u641c\u7d22\u6253\u5f00\u54df~"})})))}))}};e()}});function g(){d(),(0,c.zn)(p)}g();var u=p,l=n(3221),x={pages:["pages/products/index","pages/my/index","pages/login/index","pages/shoppingCart/index","pages/orders/index","pages/classification/index"],window:{backgroundColor:"#fff",backgroundTextStyle:"light",navigationBarBackgroundColor:"#fff",navigationBarTitleText:"Taro3",navigationBarTextStyle:"black"},permission:{"scope.userLocation":{desc:"\u83b7\u53d6\u7528\u6237\u5730\u7406\u4f4d\u7f6e\u7528\u4e8e\u586b\u5199\u8be6\u7ec6\u5730\u5740"}},subPackages:[{root:"package",pages:["package-a/termsUse/index","package-a/edit/index","package-a/confirmOrder/index","package-a/Details/index","package-a/verification/index","package-a/harvestAddress/index","package-a/harvestAddress/setAddress/index","package-a/updateOrder/index","package-a/search/index","package-a/searchOrder/index","package-a/DragView/index"]}],tabBar:{custom:!0,color:"#000000",selectedColor:"#FF0000",list:[{pagePath:"pages/products/index",text:"\u9996\u9875"},{pagePath:"pages/shoppingCart/index",text:"\u8d2d\u7269\u8f66"},{pagePath:"pages/orders/index",text:"\u8ba2\u5355"},{pagePath:"pages/my/index",text:"\u4e2a\u4eba\u4e2d\u5fc3"},{pagePath:"pages/classification/index",text:"\u5206\u7c7b"}]}};t.window.__taroAppConfig=x;App((0,i.rj)(u,l.h,x));(0,o.initPxTransform)({designWidth:function(e){var a;return null===e||void 0===e||null===(a=e.file)||void 0===a||a.replace(/\\+/g,"/").indexOf("@nutui"),375},deviceRatio:{375:2,640:1.17,750:1,828:.905},baseFontSize:20,unitPrecision:void 0,targetUnit:void 0})}},function(e){var a=function(a){return e(e.s=a)};e.O(0,[107,216,592],(function(){return a(5466)}));e.O()}]);