"use strict";require("../../sub-vendors.js");(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[19],{5150:function(e,t,n){var o={};n.r(o);var r=n(1065),s=n(2344),i=(n(2240),n(3496)),a=(n(5279),n(889)),c=(n(9932),n(3221)),l=n(7011),u=n(2e3),h={class:"p-5px"},p={class:"drag-grid m-5px"},d=["onTouchstart","onTouchmove","onTouchend","data-index"],f=["onLongpress"],x=["src"],g={style:{color:"#757575","font-family":"serif","text-align":"center"}},w=["src"],m={class:"text-end"};function v(e,t,n,o,r,v){var b=a.Z,k=i.Z,T=s.Z;return(0,c.wg)(),(0,c.iD)(c.HY,null,[(0,c._)("div",h,[t[6]||(t[6]=(0,c._)("div",{class:"mt-5px mb-5px ml-7px font-size-16px font-600"},(0,l.zw)("\u5305\u542b\u5173\u952e\u8bcd"),-1)),(0,c._)("view",p,[(0,c.Wm)(u.W3,{name:"drag"},{default:(0,c.w5)((function(){return[((0,c.wg)(!0),(0,c.iD)(c.HY,null,(0,c.Ko)(r.list1,(function(e,t){return(0,c.wg)(),(0,c.iD)("view",{key:t,class:"drag-item flex flex-col justify-center items-center",onTouchstart:function(e){return v.handleTouchStart(t,e)},onTouchmove:function(e){return v.handleTouchMove(t,e)},onTouchend:function(e){return v.handleTouchEnd(t,e)},"data-index":t},[(0,c._)("view",{class:"w-75px h-75px m-3px",onLongpress:function(n){return v.longpressImage(t,e.id)},style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center","box-shadow":"rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px"}},[(0,c._)("image",{src:e.href,class:"w-40px h-40px"},null,8,x),(0,c._)("div",g,(0,l.zw)(e.text),1)],40,f),r.hidden||r.currentIndex!=t?(0,c.kq)("",!0):((0,c.wg)(),(0,c.iD)("div",{key:0,class:"flex flex-col justify-center items-center",style:(0,l.j5)([{width:"100%",height:"100%",position:"absolute",top:"0",left:"0",opacity:"0.8"},{transform:"translate(".concat(r.positionsX[t]||0,"px, ").concat(r.positionsY[t]||0,"px)"),zIndex:r.currentIndex===t?1e3:1}])},[(0,c._)("image",{src:e.href,class:"w-40px h-40px"},null,8,w)],4))],40,d)})),128))]})),_:1}),(0,c.wy)((0,c._)("view",{class:"w-69px h-69px m-3px clickPng",onClick:t[0]||(t[0]=function(){return v.noTitleClick&&v.noTitleClick.apply(v,arguments)}),style:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center",filter:"brightness(96%)",border:"3px dashed #DDD","border-spacing":"2px"}},t[5]||(t[5]=[(0,c._)("i",{class:"i-bx-plus-circle font-size-30px color-_wn_DDDDDD"},null,-1)]),512),[[u.F8,r.list1.length<8]])])]),(0,c._)("view",m,[(0,c.Wm)(b,{color:"#FF6765",class:"mt-5_pes__el_ mr-5_pes__el_ mb-5_pes__el_ font-600",style:{"border-radius":"5px !important"},shape:"square",onClick:t[1]||(t[1]=function(e){return v.putHomeCategoryF()})},{default:(0,c.w5)((function(){return t[7]||(t[7]=[(0,c.Uk)((0,l.zw)("\u4fdd\u5b58\u66f4\u6539"))])})),_:1})]),(0,c.Wm)(T,{visible:r.visible2,"onUpdate:visible":t[3]||(t[3]=function(e){return r.visible2=e}),onCancel:v.onCancel,onOk:v.onOk},{default:(0,c.w5)((function(){return[(0,c.Wm)(k,{modelValue:r.inputValue,"onUpdate:modelValue":t[2]||(t[2]=function(e){return r.inputValue=e}),placeholder:"\u8bf7\u8f93\u5165\u81ea\u5b9a\u4e49\u5173\u952e\u8bcd","max-length":10,"show-word-limit":""},null,8,["modelValue"])]})),_:1},8,["visible","onCancel","onOk"]),(0,c.Wm)(T,{title:"\u6e29\u99a8\u63d0\u793a",content:"\u662f\u5426\u786e\u8ba4\u5220\u9664",visible:r.visible3,"onUpdate:visible":t[4]||(t[4]=function(e){return r.visible3=e}),onOk:v.ontipsOk},null,8,["visible","onOk"])],64)}var b=n(2419),k=n(2810),T=n(1959),_=n.n(T),y=n(6144),I=n(6821),S=(0,T.getCurrentInstance)(),Z={name:"DraggableGrid",data:function(){return{imageWitdh:0,x:0,y:0,areaHeight:0,hidden:!0,currentImg:"",currentIndex:0,pointsArr:[],flag:!0,scrollTop:0,startX:0,moveX:0,startY:0,moveY:0,dragging:null,positionsX:new Array(9).fill(0),positionsY:new Array(9).fill(0),visible2:!1,visible3:!1,inputValue:"",nowId:"",clickNowValue:"",list1:[]}},props:{},mounted:function(){S&&S.router&&S.router.onShow?T.eventCenter.on(S.router.onShow,this.onShowPage):(console.error("\u65e0\u6cd5\u83b7\u53d6router.onShow\u4e8b\u4ef6\u5904\u7406\u51fd\u6570"),_().showToast({title:"\u65e0\u6cd5\u83b7\u53d6\u4e8b\u4ef6",icon:"none",duration:2e3}))},unmounted:function(){S&&S.router&&S.router.onShow?T.eventCenter.off(S.router.onShow,this.onShowPage):(console.error("\u65e0\u6cd5\u83b7\u53d6router.onShow\u4e8b\u4ef6\u5904\u7406\u51fd\u6570"),_().showToast({title:"\u65e0\u6cd5\u83b7\u53d6\u4e8b\u4ef6",icon:"none",duration:2e3}))},methods:{handleTouchStart:function(e,t){this._handleComputedPoints(),this.currentImg=t.currentTarget.dataset.url,this.currentIndex=t.currentTarget.dataset.index,this.hidden=!1,this.flag=!0,this.x=t.currentTarget.offsetLeft,this.y=t.currentTarget.offsetTop,this.startX=t.touches[0].clientX,this.startY=t.touches[0].clientY},_handleComputedPoints:function(e){var t=this,n=_().createSelectorQuery(),o=n.selectAll(".drag-item");o.fields({dataset:!0,rect:!0},(function(e){t.pointsArr=e,t.pointsArr.forEach((function(e,t){e.dataset.index=t}))})).exec()},handleTouchEnd:function(e,t){for(var n=t.changedTouches[0].pageX,o=t.changedTouches[0].pageY-this.scrollTop,r=this.pointsArr,s=this.list1,i=0;i<r.length;i++){var a=r[i];if(n>a.left&&n<a.right&&o>a.top&&o<a.bottom){var c=a.dataset.index,l=this.currentIndex,u=s[l];s[l]=s[c],s[c]=u}}this.images=s,this.hidden=!0,this.flag=!1,this.currentImg="",this.positionsX[e]=0,this.positionsY[e]=0},handleTouchMove:function(e,t){var n=t.touches[0].pageX,o=t.touches[0].pageY,r=this;_().createSelectorQuery().selectAll(".drag-grid").boundingClientRect((function(e){var t=e[0].top;o=o-r.scrollTop-t,r.x=n,r.y=o})).exec();var s=t.touches[0].clientX;this.moveX=s-this.startX,this.positionsX[e]=this.moveX;var i=t.touches[0].clientY;this.moveY=i-this.startY,this.positionsY[e]=this.moveY},putHomeCategoryF:function(){var e=this;return(0,k.Z)((0,b.Z)().mark((function t(){var n,o;return(0,b.Z)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return(0,I.IU)(e.list1).map((function(e,t){e.sort=parseInt(t+1)})),console.log((0,I.IU)(e.list1)),t.next=4,(0,y.rG)({home_category:JSON.stringify((0,I.IU)(e.list1))});case 4:if(n=t.sent,o=n.error,n.success,null!=o){t.next=12;break}return t.next=10,_().showToast({title:"\u4fdd\u5b58\u6210\u529f",icon:"success",duration:2e3,success:function(){setTimeout((function(){_().navigateBack({delta:1})}),2e3)}});case 10:t.next=13;break;case 12:_().showToast({title:"\u4fdd\u5b58\u5931\u8d25",icon:"none",duration:2e3});case 13:case"end":return t.stop()}}),t)})))()},onCancel:function(){},onOk:function(){var e=this;return(0,k.Z)((0,b.Z)().mark((function t(){var n,o;return(0,b.Z)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.list1.push({id:(0,I.IU)(e.list1)[(0,I.IU)(e.list1).length-1].id+1,text:e.inputValue,sort:(0,I.IU)(e.list1).length+1}),console.log((0,I.IU)(e.list1)),t.next=4,(0,y.x9)({text:e.inputValue,sort:(0,I.IU)(e.list1).length+1});case 4:if(n=t.sent,o=n.error,n.success,null!=o){t.next=13;break}return t.next=10,_().showToast({title:"\u6dfb\u52a0\u6210\u529f",icon:"success",duration:2e3});case 10:e.inputValue="",t.next=15;break;case 13:return t.next=15,_().showToast({title:"\u6dfb\u52a0\u5931\u8d25",icon:"none",duration:2e3});case 15:case"end":return t.stop()}}),t)})))()},noTitleClick:function(){this.visible2=!0},longpressImage:function(e,t){var n=this;return(0,k.Z)((0,b.Z)().mark((function o(){return(0,b.Z)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:n.visible3=!0,n.nowId=e,n.clickNowValue=t;case 3:case"end":return o.stop()}}),o)})))()},ontipsOk:function(){var e=this;return(0,k.Z)((0,b.Z)().mark((function t(){var n,o;return(0,b.Z)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log(e.nowId),e.list1.splice(e.nowId,1),(0,I.IU)(e.list1).map((function(e,t){e.sort=parseInt(t+1)})),t.next=5,(0,y._H)({id:e.clickNowValue});case 5:if(n=t.sent,o=n.error,n.success,null!=o){t.next=13;break}return t.next=11,_().showToast({title:"\u5220\u9664\u6210\u529f",icon:"success",duration:2e3});case 11:t.next=15;break;case 13:return t.next=15,_().showToast({title:"\u5220\u9664\u5931\u8d25",icon:"none",duration:2e3});case 15:case"end":return t.stop()}}),t)})))()},onShowPage:function(){var e=this;return(0,k.Z)((0,b.Z)().mark((function t(){var n,o,r;return(0,b.Z)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(""===_().getStorageSync("token")){t.next=9;break}return t.next=3,(0,y.SX)();case 3:n=t.sent,o=n.error,r=n.success,console.log(o,"error"),console.log(r,"success"),null===o?(e.list1=r.items.sort((function(e,t){return e.sort-t.sort})),_().showToast({title:"\u83b7\u53d6\u6210\u529f",icon:"success",duration:2e3})):_().showToast({title:"\u83b7\u53d6\u5931\u8d25",icon:"none",duration:2e3});case 9:case"end":return t.stop()}}),t)})))()}}},C=n(7854);const Y={};Y["$style"]=o["default"];const D=(0,C.Z)(Z,[["render",v],["__cssModules",Y]]);var X=D,U={navigationBarTitleText:"\u7f16\u8f91\u5e38\u7528\u5173\u952e\u8bcd",enableShareAppMessage:!0};X.enableShareAppMessage=!0;Page((0,r.createPageConfig)(X,"package/package-a/DragView/index",{root:{cn:[]}},U||{}))}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[91,107,216,592],(function(){return t(5150)}));e.O()}]);