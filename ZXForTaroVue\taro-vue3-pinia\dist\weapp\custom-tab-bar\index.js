"use strict";(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[23],{3823:function(e,t,n){var r=n(1065),a=n(9891),o=(n(8948),n(2419)),i=n(3091),u=n(3221),c=n(6821),l=n(7011),s=n(1959),b=n.n(s),f=n(2827),p=n(7923),v=Symbol("nut-tabbar"),d=n(6249),m=Object.defineProperty,h=Object.defineProperties,w=Object.getOwnPropertyDescriptors,x=Object.getOwnPropertySymbols,g=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable,_=function(e,t,n){return t in e?m(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},P=function(e,t){for(var n in t||(t={}))g.call(t,n)&&_(e,n,t[n]);if(x){var r,a=(0,i.Z)(x(t));try{for(a.s();!(r=a.n()).done;){n=r.value;y.call(t,n)&&_(e,n,t[n])}}catch(e){a.e(e)}finally{a.f()}}return e},k=function(e,t){return h(e,w(t))},O=function(e,t,n){return new Promise((function(r,a){var o=function(e){try{u(n.next(e))}catch(e){a(e)}},i=function(e){try{u(n.throw(e))}catch(e){a(e)}},u=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(o,i)};u((n=n.apply(e,t)).next())}))},j=["id"],C=(0,u.aZ)(k(P({},{name:"NutTabbar"}),{__name:"tabbar.taro",props:{modelValue:{default:0},bottom:{type:Boolean,default:!1},unactiveColor:{default:""},activeColor:{default:""},safeAreaInsetBottom:{type:Boolean,default:!1},placeholder:{type:Boolean,default:!1},beforeSwitch:{type:Function,default:function(){return!0}}},emits:["tabSwitch","update:modelValue"],setup:function(e,t){var n=this,r=t.emit,a=e,i=r,s=Math.random().toString(36).slice(-8),d=(0,c.iH)("auto"),m=(0,c.iH)(null),h=(0,c.iH)(a.modelValue),w=(0,p.u)(v),x=w.children,g=w.linkChildren,y=function(e,t){return O(n,null,(0,o.Z)().mark((function n(){var r;return(0,o.Z)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,a.beforeSwitch(x[e],t);case 2:if(r=n.sent,!1!==r){n.next=5;break}return n.abrupt("return",Promise.reject());case 5:h.value=t,i("update:modelValue",t),i("tabSwitch",x[e],t);case 8:case"end":return n.stop()}}),n)})))};g({props:a,activeIndex:h,changeIndex:y}),(0,u.YP)((function(){return a.modelValue}),(function(e){h.value=e}));var _=function(){a.bottom&&a.placeholder&&b().nextTick((function(){(0,f.u)(m).then((function(e){d.value="".concat(e.height,"px")}),(function(){}))}))};return(0,u.bv)(_),function(e,t){return(0,u.wg)(),(0,u.iD)("view",{class:(0,l.C_)({"nut-tabbar__placeholder":e.bottom&&e.placeholder}),style:(0,l.j5)({height:d.value})},[(0,u._)("view",{id:"nut-tabbar-".concat((0,c.SU)(s)),ref_key:"nutTabbarRef",ref:m,class:(0,l.C_)(["nut-tabbar",{"nut-tabbar-bottom":e.bottom,"nut-tabbar-safebottom":e.safeAreaInsetBottom}])},[(0,u.WI)(e.$slots,"default")],10,j)],6)}}}));(0,d.w)(C);n(3939);var F=n(1397),I=n(6797),S=n(7643),Z=n(1065)["window"],T=n(1065)["location"],D=Object.defineProperty,$=Object.defineProperties,V=Object.getOwnPropertyDescriptors,B=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable,q=function(e,t,n){return t in e?D(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},A=function(e,t){for(var n in t||(t={}))U.call(t,n)&&q(e,n,t[n]);if(B){var r,a=(0,i.Z)(B(t));try{for(a.s();!(r=a.n()).done;){n=r.value;W.call(t,n)&&q(e,n,t[n])}}catch(e){a.e(e)}finally{a.f()}}return e},H=function(e,t){return $(e,V(t))},N=function(e,t,n){return new Promise((function(r,a){var o=function(e){try{u(n.next(e))}catch(e){a(e)}},i=function(e){try{u(n.throw(e))}catch(e){a(e)}},u=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(o,i)};u((n=n.apply(e,t)).next())}))};function E(){var e=(0,u.FN)().proxy;return e.$router||null}var J={class:"nut-tabbar-item_icon-box"},L={key:0,class:"nut-tabbar-item_icon-box_icon"},Y={key:1},z={key:0},G=(0,u.aZ)(H(A({},{name:"NutTabbarItem"}),{__name:"tabbar-item.taro",props:{tabTitle:{default:""},name:{},icon:{},href:{default:""},to:{}},setup:function(e){var t=this,n=e,r=E(),a=(0,I.u)(v),i=a.parent,s=a.index,b=(0,u.Fl)((function(){var e;return(null!=(e=n.name)?e:s.value)===i.activeIndex.value})),f=(0,u.Fl)((function(){return b.value?i.props.activeColor:i.props.unactiveColor})),p=function(){return N(t,null,(0,o.Z)().mark((function e(){var t,a,u,c,l;return(0,o.Z)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,c=null!=(t=n.name)?t:s.value,e.next=4,i.changeIndex(s.value,c);case 4:if(!(null==(a=i.children[s.value])?void 0:a.href)){e.next=7;break}return Z.location.href=i.children[s.value].href,e.abrupt("return");case 7:(null==(u=i.children[s.value])?void 0:u.to)&&(l=i.children[s.value].to,l&&r?r.push(l):T.replace(l)),e.next=12;break;case 10:e.prev=10,e.t0=e["catch"](0);case 12:case"end":return e.stop()}}),e,null,[[0,10]])})))};return function(e,t){return(0,u.wg)(),(0,u.iD)("div",{class:(0,l.C_)(["nut-tabbar-item",{"nut-tabbar-item__icon--unactive":!b.value}]),style:(0,l.j5)({color:f.value}),onClick:p},[(0,u.Wm)((0,c.SU)(S.C),(0,l.vs)((0,u.F4)(e.$attrs)),{default:(0,u.w5)((function(){return[(0,u._)("view",J,[e.$slots.icon?((0,u.wg)(),(0,u.iD)("div",L,[(0,u.WI)(e.$slots,"icon",{active:b.value})])):(0,u.kq)("",!0),(0,u.Uk)(),e.icon&&!e.$slots.icon?((0,u.wg)(),(0,u.iD)("view",Y,[((0,u.wg)(),(0,u.j4)((0,u.LL)((0,c.SU)(F.r)(e.icon)),{class:"nut-popover-item-img"}))])):(0,u.kq)("",!0),(0,u.Uk)(),(0,u._)("view",{class:(0,l.C_)(["nut-tabbar-item_icon-box_nav-word",{"nut-tabbar-item_icon-box_big-word":!e.icon&&!e.$slots.icon}])},[(0,u.WI)(e.$slots,"default",{},(function(){return[e.tabTitle?((0,u.wg)(),(0,u.iD)("view",z,(0,l.zw)(e.tabTitle),1)):(0,u.kq)("",!0)]}))],2)])]})),_:3},16)],6)}}}));(0,d.w)(G);var K=n(2018),M=n(4081),R={options:{addGlobalClass:!0}},Q=(0,u.aZ)((0,K.Z)((0,K.Z)({},R),{},{__name:"index",setup:function(e){var t={custom:!0,color:"#000000",selectedColor:"FF0000",list:[{pagePath:"/pages/products/index",text:"\u9996\u9875",icon:"i-bx-bxl-product-hunt"},{pagePath:"/pages/classification/index",text:"\u5206\u7c7b",icon:"i-bx-grid-alt"},{pagePath:"/pages/shoppingCart/index",text:"\u8d2d\u7269\u8f66",icon:"i-bx-shopping-bag"},{pagePath:"/pages/orders/index",text:"\u8ba2\u5355",icon:"i-bx-calendar-alt"},{pagePath:"/pages/my/index",text:"\u4e2a\u4eba\u4e2d\u5fc3",icon:"i-bx-user"}]},n=(0,M.fl)(),r=(0,u.Fl)((function(){return n.theme})),o=(0,u.Fl)((function(){return n.themeVars})),i=(0,M.qr)(),c=(0,u.Fl)((function(){return i.getActiveTab}));function b(e,t){i.setActiveTab(t),(0,s.switchTab)({url:t})}return function(e,n){var i=G,s=C,f=a.Z;return(0,u.wg)(),(0,u.j4)(f,{theme:r.value,"theme-vars":o.value},{default:(0,u.w5)((function(){return[(0,u.Wm)(s,{"model-value":c.value,bottom:"","safe-area-inset-bottom":"",onTabSwitch:b,"active-color":"#FF0000"},{default:(0,u.w5)((function(){return[((0,u.wg)(!0),(0,u.iD)(u.HY,null,(0,u.Ko)(t.list,(function(e){return(0,u.wg)(),(0,u.j4)(i,{key:e.pagePath,name:e.pagePath,"tab-title":e.text},{icon:(0,u.w5)((function(){return[(0,u._)("div",{class:(0,l.C_)(["text-25px",e.icon])},null,2)]})),_:2},1032,["name","tab-title"])})),128))]})),_:1},8,["model-value"])]})),_:1},8,["theme","theme-vars"])}}}));const X=Q;var ee=X;Component((0,r.createComponentConfig)(ee,"custom-tab-bar/index"))}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[107,216,592],(function(){return t(3823)}));e.O()}]);