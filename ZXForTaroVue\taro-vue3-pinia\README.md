- 首先，cd到taro-vue3-pinia目录下

- 其次，pnpm install 安装最新依赖

- 最后

  ```cmd
  pnpm run dev:weapp   //调试
  pnpm run build:weapp  //打包 
  ```

##### pages目录  页面文件存放处

##### package目录 代码分包

##### service目录 网络层、接口层的文件存放处

- helpers.ts   请求头配置
- instansce.ts 请求值、返回值预处理
- request.ts  接口通用配置

##### store目录 全局状态管理

##### components目录 可复用组件

##### assets目录 静态资源

##### typings目录 .d.ts文件 ts的声明文件 防止代码编译器报错





```
左向商城-TaroVue
├─ .sql
├─ ZXForTaroVue
│  ├─ taro-vue3-pinia
│  │  ├─ .editorconfig
│  │  ├─ .eslintignore
│  │  ├─ .eslintrc.js
│  │  ├─ .npmrc
│  │  ├─ .swc
│  │  │  └─ plugins
│  │  │     └─ v7_windows_x86_64_0.104.8
│  │  │        └─ 90faf4f7c8a00ffb0fa720cec7dab342ff5ad41d654795c6b4b8c17d335731d7
│  │  ├─ babel.config.js
│  │  ├─ CHANGELOG.md
│  │  ├─ commitlint.config.js
│  │  ├─ config
│  │  │  ├─ dev.js
│  │  │  ├─ index.js
│  │  │  └─ prod.js
│  │  ├─ dist
│  │  │  ├─ alipay
│  │  │  │  ├─ app-origin.acss
│  │  │  │  ├─ app.acss
│  │  │  │  ├─ app.js
│  │  │  │  ├─ app.js.map
│  │  │  │  ├─ app.json
│  │  │  │  ├─ base.axml
│  │  │  │  ├─ common.acss
│  │  │  │  ├─ common.js
│  │  │  │  ├─ common.js.map
│  │  │  │  ├─ mini.project.json
│  │  │  │  ├─ package
│  │  │  │  │  ├─ package-a
│  │  │  │  │  │  ├─ confirmOrder
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  └─ index.json
│  │  │  │  │  │  ├─ Details
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  └─ index.json
│  │  │  │  │  │  ├─ edit
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  └─ index.json
│  │  │  │  │  │  ├─ fillOut
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  └─ index.json
│  │  │  │  │  │  ├─ harvestAddress
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  ├─ index.json
│  │  │  │  │  │  │  └─ setAddress
│  │  │  │  │  │  │     ├─ index.acss
│  │  │  │  │  │  │     ├─ index.axml
│  │  │  │  │  │  │     ├─ index.js
│  │  │  │  │  │  │     ├─ index.js.map
│  │  │  │  │  │  │     └─ index.json
│  │  │  │  │  │  ├─ termsUse
│  │  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  │  └─ index.json
│  │  │  │  │  │  └─ verification
│  │  │  │  │  │     ├─ index.acss
│  │  │  │  │  │     ├─ index.axml
│  │  │  │  │  │     ├─ index.js
│  │  │  │  │  │     ├─ index.js.map
│  │  │  │  │  │     └─ index.json
│  │  │  │  │  ├─ sub-vendors.acss
│  │  │  │  │  ├─ sub-vendors.js
│  │  │  │  │  └─ sub-vendors.js.map
│  │  │  │  ├─ pages
│  │  │  │  │  ├─ inventory
│  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  └─ index.json
│  │  │  │  │  ├─ login
│  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  └─ index.json
│  │  │  │  │  ├─ my
│  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  └─ index.json
│  │  │  │  │  ├─ orders
│  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  └─ index.json
│  │  │  │  │  ├─ products
│  │  │  │  │  │  ├─ index.acss
│  │  │  │  │  │  ├─ index.axml
│  │  │  │  │  │  ├─ index.js
│  │  │  │  │  │  ├─ index.js.map
│  │  │  │  │  │  └─ index.json
│  │  │  │  │  └─ shoppingCart
│  │  │  │  │     ├─ index.acss
│  │  │  │  │     ├─ index.axml
│  │  │  │  │     ├─ index.js
│  │  │  │  │     ├─ index.js.map
│  │  │  │  │     └─ index.json
│  │  │  │  ├─ prebundle
│  │  │  │  │  └─ remoteEntry.js
│  │  │  │  ├─ runtime.js
│  │  │  │  ├─ runtime.js.map
│  │  │  │  ├─ taro.js
│  │  │  │  ├─ taro.js.map
│  │  │  │  ├─ utils.sjs
│  │  │  │  ├─ vendors.js
│  │  │  │  └─ vendors.js.map
│  │  │  ├─ h5
│  │  │  │  ├─ chunk
│  │  │  │  │  ├─ 137.js
│  │  │  │  │  ├─ 16.js
│  │  │  │  │  ├─ 16.js.LICENSE.txt
│  │  │  │  │  ├─ 232.js
│  │  │  │  │  ├─ 244.js
│  │  │  │  │  ├─ 333.js
│  │  │  │  │  ├─ 333.js.LICENSE.txt
│  │  │  │  │  ├─ 343.js
│  │  │  │  │  ├─ 416.js
│  │  │  │  │  ├─ 420.js
│  │  │  │  │  ├─ 424.js
│  │  │  │  │  ├─ 446.js
│  │  │  │  │  ├─ 537.js
│  │  │  │  │  ├─ 571.js
│  │  │  │  │  ├─ 685.js
│  │  │  │  │  ├─ 759.js
│  │  │  │  │  ├─ 799.js
│  │  │  │  │  ├─ 811.js
│  │  │  │  │  ├─ 865.js
│  │  │  │  │  ├─ 887.js
│  │  │  │  │  ├─ 887.js.LICENSE.txt
│  │  │  │  │  ├─ 893.js
│  │  │  │  │  └─ 940.js
│  │  │  │  ├─ css
│  │  │  │  │  ├─ 137.css
│  │  │  │  │  ├─ 244.css
│  │  │  │  │  ├─ 333.css
│  │  │  │  │  ├─ 343.css
│  │  │  │  │  ├─ 416.css
│  │  │  │  │  ├─ 424.css
│  │  │  │  │  ├─ 537.css
│  │  │  │  │  ├─ 571.css
│  │  │  │  │  ├─ 685.css
│  │  │  │  │  ├─ 759.css
│  │  │  │  │  ├─ 799.css
│  │  │  │  │  ├─ 811.css
│  │  │  │  │  ├─ 865.css
│  │  │  │  │  ├─ 887.css
│  │  │  │  │  ├─ 893.css
│  │  │  │  │  ├─ 940.css
│  │  │  │  │  └─ app.css
│  │  │  │  ├─ index.html
│  │  │  │  └─ js
│  │  │  │     ├─ 454.js
│  │  │  │     ├─ 454.js.LICENSE.txt
│  │  │  │     ├─ app.js
│  │  │  │     └─ app.js.LICENSE.txt
│  │  │  ├─ mini.project.json
│  │  │  ├─ project.config.json
│  │  │  ├─ project.private.config.json
│  │  │  └─ weapp
│  │  │     ├─ app-origin.wxss
│  │  │     ├─ app.js
│  │  │     ├─ app.json
│  │  │     ├─ app.wxss
│  │  │     ├─ base.wxml
│  │  │     ├─ common.js
│  │  │     ├─ common.wxss
│  │  │     ├─ comp.js
│  │  │     ├─ comp.json
│  │  │     ├─ comp.wxml
│  │  │     ├─ custom-tab-bar
│  │  │     │  ├─ index.js
│  │  │     │  ├─ index.json
│  │  │     │  ├─ index.wxml
│  │  │     │  └─ index.wxss
│  │  │     ├─ package
│  │  │     │  ├─ package-a
│  │  │     │  │  ├─ confirmOrder
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ Details
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ DragView
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ edit
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ harvestAddress
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  ├─ index.wxss
│  │  │     │  │  │  └─ setAddress
│  │  │     │  │  │     ├─ index.js
│  │  │     │  │  │     ├─ index.json
│  │  │     │  │  │     ├─ index.wxml
│  │  │     │  │  │     └─ index.wxss
│  │  │     │  │  ├─ search
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ searchOrder
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ termsUse
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  ├─ updateOrder
│  │  │     │  │  │  ├─ index.js
│  │  │     │  │  │  ├─ index.json
│  │  │     │  │  │  ├─ index.wxml
│  │  │     │  │  │  └─ index.wxss
│  │  │     │  │  └─ verification
│  │  │     │  │     ├─ index.js
│  │  │     │  │     ├─ index.json
│  │  │     │  │     ├─ index.wxml
│  │  │     │  │     └─ index.wxss
│  │  │     │  ├─ sub-vendors.js
│  │  │     │  └─ sub-vendors.wxss
│  │  │     ├─ pages
│  │  │     │  ├─ classification
│  │  │     │  │  ├─ index.js
│  │  │     │  │  ├─ index.json
│  │  │     │  │  ├─ index.wxml
│  │  │     │  │  └─ index.wxss
│  │  │     │  ├─ login
│  │  │     │  │  ├─ index.js
│  │  │     │  │  ├─ index.json
│  │  │     │  │  ├─ index.wxml
│  │  │     │  │  └─ index.wxss
│  │  │     │  ├─ my
│  │  │     │  │  ├─ index.js
│  │  │     │  │  ├─ index.json
│  │  │     │  │  ├─ index.wxml
│  │  │     │  │  └─ index.wxss
│  │  │     │  ├─ orders
│  │  │     │  │  ├─ index.js
│  │  │     │  │  ├─ index.json
│  │  │     │  │  ├─ index.wxml
│  │  │     │  │  └─ index.wxss
│  │  │     │  ├─ products
│  │  │     │  │  ├─ index.js
│  │  │     │  │  ├─ index.json
│  │  │     │  │  ├─ index.wxml
│  │  │     │  │  └─ index.wxss
│  │  │     │  └─ shoppingCart
│  │  │     │     ├─ index.js
│  │  │     │     ├─ index.json
│  │  │     │     ├─ index.wxml
│  │  │     │     └─ index.wxss
│  │  │     ├─ project.config.json
│  │  │     ├─ runtime.js
│  │  │     ├─ taro.js
│  │  │     ├─ utils.wxs
│  │  │     ├─ vendors.js
│  │  │     └─ vendors.js.LICENSE.txt
│  │  ├─ LICENSE
│  │  ├─ package.json
│  │  ├─ pnpm-lock.yaml
│  │  ├─ project.config.json
│  │  ├─ project.private.config.json
│  │  ├─ project.tt.json
│  │  ├─ README.md
│  │  ├─ src
│  │  │  ├─ app.config.ts
│  │  │  ├─ app.scss
│  │  │  ├─ app.ts
│  │  │  ├─ assets
│  │  │  │  └─ svg
│  │  │  │     ├─ custom-icon.svg
│  │  │  │     └─ wind.svg
│  │  │  ├─ components
│  │  │  │  ├─ address
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ basic-layout
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ Card
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ custom-navbar
│  │  │  │  │  └─ index.vue
│  │  │  │  └─ design-cell
│  │  │  │     └─ index.vue
│  │  │  ├─ composables
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ route.ts
│  │  │  │  └─ router.ts
│  │  │  ├─ constants
│  │  │  │  ├─ common.ts
│  │  │  │  ├─ index.ts
│  │  │  │  └─ service.ts
│  │  │  ├─ custom-tab-bar
│  │  │  │  └─ index.vue
│  │  │  ├─ hooks
│  │  │  │  ├─ business
│  │  │  │  │  ├─ index.ts
│  │  │  │  │  ├─ use-count-down.ts
│  │  │  │  │  └─ use-sms-code.ts
│  │  │  │  ├─ common
│  │  │  │  │  ├─ index.ts
│  │  │  │  │  ├─ use-boolean.ts
│  │  │  │  │  ├─ use-context.ts
│  │  │  │  │  └─ use-loading.ts
│  │  │  │  └─ index.ts
│  │  │  ├─ index.html
│  │  │  ├─ interface
│  │  │  │  ├─ business
│  │  │  │  │  ├─ auth.ts
│  │  │  │  │  └─ index.ts
│  │  │  │  └─ index.ts
│  │  │  ├─ mock
│  │  │  ├─ package
│  │  │  │  └─ package-a
│  │  │  │     ├─ confirmOrder
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ context
│  │  │  │     │  └─ user.ts
│  │  │  │     ├─ Details
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ DragView
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ edit
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ fillOut
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ harvestAddress
│  │  │  │     │  ├─ dist
│  │  │  │     │  │  └─ zh-address-parse.min.js
│  │  │  │     │  ├─ index.vue
│  │  │  │     │  └─ setAddress
│  │  │  │     │     └─ index.vue
│  │  │  │     ├─ search
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ searchOrder
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ termsUse
│  │  │  │     │  └─ index.vue
│  │  │  │     ├─ updateOrder
│  │  │  │     │  └─ index.vue
│  │  │  │     └─ verification
│  │  │  │        └─ index.vue
│  │  │  ├─ pages
│  │  │  │  ├─ classification
│  │  │  │  │  ├─ components
│  │  │  │  │  │  └─ card.vue
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ inventory
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ login
│  │  │  │  │  └─ index.vue
│  │  │  │  ├─ my
│  │  │  │  │  ├─ index.vue
│  │  │  │  │  └─ myImgCanvas.vue
│  │  │  │  ├─ orders
│  │  │  │  │  ├─ index.vue
│  │  │  │  │  ├─ index.vue.txt
│  │  │  │  │  ├─ orders.vue
│  │  │  │  │  └─ orders.vue.txt
│  │  │  │  ├─ products
│  │  │  │  │  ├─ components
│  │  │  │  │  │  └─ shoppingCard.vue
│  │  │  │  │  ├─ index.vue
│  │  │  │  │  └─ js.vue
│  │  │  │  └─ shoppingCart
│  │  │  │     └─ index.vue
│  │  │  ├─ plugins
│  │  │  │  ├─ assets.ts
│  │  │  │  └─ index.ts
│  │  │  ├─ service
│  │  │  │  ├─ api
│  │  │  │  │  ├─ demo.ts
│  │  │  │  │  └─ index.ts
│  │  │  │  ├─ index.ts
│  │  │  │  └─ request
│  │  │  │     ├─ helpers.ts
│  │  │  │     ├─ index.ts
│  │  │  │     ├─ instansce.ts
│  │  │  │     └─ request.ts
│  │  │  ├─ service.zip
│  │  │  ├─ store
│  │  │  │  ├─ index.ts
│  │  │  │  └─ modules
│  │  │  │     ├─ app
│  │  │  │     │  └─ index.ts
│  │  │  │     ├─ auth
│  │  │  │     │  ├─ helpers.ts
│  │  │  │     │  └─ index.ts
│  │  │  │     ├─ index.ts
│  │  │  │     └─ theme
│  │  │  │        └─ index.ts
│  │  │  ├─ styles
│  │  │  │  ├─ common.scss
│  │  │  │  ├─ h5.scss
│  │  │  │  ├─ index.scss
│  │  │  │  └─ reset.scss
│  │  │  ├─ typings
│  │  │  │  ├─ auth.d.ts
│  │  │  │  ├─ common.d.ts
│  │  │  │  ├─ components.d.ts
│  │  │  │  ├─ global.d.ts
│  │  │  │  ├─ service.d.ts
│  │  │  │  ├─ storage.d.ts
│  │  │  │  └─ utils.d.ts
│  │  │  └─ utils
│  │  │     ├─ common
│  │  │     │  ├─ index.ts
│  │  │     │  ├─ number.ts
│  │  │     │  ├─ pattern.ts
│  │  │     │  └─ typeof.ts
│  │  │     ├─ index.ts
│  │  │     └─ storage
│  │  │        ├─ index.ts
│  │  │        └─ local.ts
│  │  ├─ taro-vue3-pinia.zip
│  │  ├─ tsconfig.json
│  │  ├─ unocss.config.ts
│  │  └─ weapp.zip
│  ├─ taro-vue3-pinia.rar
│  ├─ 学生.json
│  ├─ 教师.json
│  └─ 管理员.json
└─ 旧版本
   └─ ZXForTaroVue
      ├─ taro-vue3-pinia
      │  └─ dist
      │     ├─ alipay
      │     │  ├─ app-origin.acss
      │     │  ├─ app.acss
      │     │  ├─ app.js
      │     │  ├─ app.js.map
      │     │  ├─ app.json
      │     │  ├─ base.axml
      │     │  ├─ common.acss
      │     │  ├─ common.js
      │     │  ├─ common.js.map
      │     │  ├─ mini.project.json
      │     │  ├─ package
      │     │  │  ├─ package-a
      │     │  │  │  ├─ confirmOrder
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  └─ index.json
      │     │  │  │  ├─ Details
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  └─ index.json
      │     │  │  │  ├─ edit
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  └─ index.json
      │     │  │  │  ├─ fillOut
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  └─ index.json
      │     │  │  │  ├─ harvestAddress
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  ├─ index.json
      │     │  │  │  │  └─ setAddress
      │     │  │  │  │     ├─ index.acss
      │     │  │  │  │     ├─ index.axml
      │     │  │  │  │     ├─ index.js
      │     │  │  │  │     ├─ index.js.map
      │     │  │  │  │     └─ index.json
      │     │  │  │  ├─ termsUse
      │     │  │  │  │  ├─ index.acss
      │     │  │  │  │  ├─ index.axml
      │     │  │  │  │  ├─ index.js
      │     │  │  │  │  ├─ index.js.map
      │     │  │  │  │  └─ index.json
      │     │  │  │  └─ verification
      │     │  │  │     ├─ index.acss
      │     │  │  │     ├─ index.axml
      │     │  │  │     ├─ index.js
      │     │  │  │     ├─ index.js.map
      │     │  │  │     └─ index.json
      │     │  │  ├─ sub-vendors.acss
      │     │  │  ├─ sub-vendors.js
      │     │  │  └─ sub-vendors.js.map
      │     │  ├─ pages
      │     │  │  ├─ inventory
      │     │  │  │  ├─ index.acss
      │     │  │  │  ├─ index.axml
      │     │  │  │  ├─ index.js
      │     │  │  │  ├─ index.js.map
      │     │  │  │  └─ index.json
      │     │  │  ├─ login
      │     │  │  │  ├─ index.acss
      │     │  │  │  ├─ index.axml
      │     │  │  │  ├─ index.js
      │     │  │  │  ├─ index.js.map
      │     │  │  │  └─ index.json
      │     │  │  ├─ my
      │     │  │  │  ├─ index.acss
      │     │  │  │  ├─ index.axml
      │     │  │  │  ├─ index.js
      │     │  │  │  ├─ index.js.map
      │     │  │  │  └─ index.json
      │     │  │  ├─ orders
      │     │  │  │  ├─ index.acss
      │     │  │  │  ├─ index.axml
      │     │  │  │  ├─ index.js
      │     │  │  │  ├─ index.js.map
      │     │  │  │  └─ index.json
      │     │  │  ├─ products
      │     │  │  │  ├─ index.acss
      │     │  │  │  ├─ index.axml
      │     │  │  │  ├─ index.js
      │     │  │  │  ├─ index.js.map
      │     │  │  │  └─ index.json
      │     │  │  └─ shoppingCart
      │     │  │     ├─ index.acss
      │     │  │     ├─ index.axml
      │     │  │     ├─ index.js
      │     │  │     ├─ index.js.map
      │     │  │     └─ index.json
      │     │  ├─ prebundle
      │     │  │  └─ remoteEntry.js
      │     │  ├─ runtime.js
      │     │  ├─ runtime.js.map
      │     │  ├─ taro.js
      │     │  ├─ taro.js.map
      │     │  ├─ utils.sjs
      │     │  ├─ vendors.js
      │     │  └─ vendors.js.map
      │     ├─ mini.project.json
      │     ├─ project.config.json
      │     ├─ project.private.config.json
      │     └─ weapp
      │        ├─ app-origin.wxss
      │        ├─ app.js
      │        ├─ app.json
      │        ├─ app.wxss
      │        ├─ base.wxml
      │        ├─ common.js
      │        ├─ common.wxss
      │        ├─ comp.js
      │        ├─ comp.json
      │        ├─ comp.wxml
      │        ├─ custom-tab-bar
      │        │  ├─ index.js
      │        │  ├─ index.json
      │        │  ├─ index.wxml
      │        │  └─ index.wxss
      │        ├─ package
      │        │  ├─ package-a
      │        │  │  ├─ confirmOrder
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ Details
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ DragView
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ edit
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ harvestAddress
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  ├─ index.wxss
      │        │  │  │  └─ setAddress
      │        │  │  │     ├─ index.js
      │        │  │  │     ├─ index.json
      │        │  │  │     ├─ index.wxml
      │        │  │  │     └─ index.wxss
      │        │  │  ├─ search
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ searchOrder
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ termsUse
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  ├─ updateOrder
      │        │  │  │  ├─ index.js
      │        │  │  │  ├─ index.json
      │        │  │  │  ├─ index.wxml
      │        │  │  │  └─ index.wxss
      │        │  │  └─ verification
      │        │  │     ├─ index.js
      │        │  │     ├─ index.json
      │        │  │     ├─ index.wxml
      │        │  │     └─ index.wxss
      │        │  ├─ sub-vendors.js
      │        │  └─ sub-vendors.wxss
      │        ├─ pages
      │        │  ├─ classification
      │        │  │  ├─ index.js
      │        │  │  ├─ index.json
      │        │  │  ├─ index.wxml
      │        │  │  └─ index.wxss
      │        │  ├─ login
      │        │  │  ├─ index.js
      │        │  │  ├─ index.json
      │        │  │  ├─ index.wxml
      │        │  │  └─ index.wxss
      │        │  ├─ my
      │        │  │  ├─ index.js
      │        │  │  ├─ index.json
      │        │  │  ├─ index.wxml
      │        │  │  └─ index.wxss
      │        │  ├─ orders
      │        │  │  ├─ index.js
      │        │  │  ├─ index.json
      │        │  │  ├─ index.wxml
      │        │  │  └─ index.wxss
      │        │  ├─ products
      │        │  │  ├─ index.js
      │        │  │  ├─ index.json
      │        │  │  ├─ index.wxml
      │        │  │  └─ index.wxss
      │        │  └─ shoppingCart
      │        │     ├─ index.js
      │        │     ├─ index.json
      │        │     ├─ index.wxml
      │        │     └─ index.wxss
      │        ├─ project.config.json
      │        ├─ project.private.config.json
      │        ├─ runtime.js
      │        ├─ taro.js
      │        ├─ utils.wxs
      │        ├─ vendors.js
      │        └─ vendors.js.LICENSE.txt
      ├─ taro-vue3-pinia.rar
      └─ taro-vue3-pinia1
         ├─ .editorconfig
         ├─ .eslintignore
         ├─ .eslintrc.js
         ├─ .npmrc
         ├─ .swc
         │  └─ plugins
         │     └─ v7_windows_x86_64_0.104.8
         │        └─ 90faf4f7c8a00ffb0fa720cec7dab342ff5ad41d654795c6b4b8c17d335731d7
         ├─ babel.config.js
         ├─ CHANGELOG.md
         ├─ commitlint.config.js
         ├─ config
         │  ├─ dev.js
         │  ├─ index.js
         │  └─ prod.js
         ├─ LICENSE
         ├─ package.json
         ├─ pnpm-lock.yaml
         ├─ project.config.json
         ├─ project.private.config.json
         ├─ project.tt.json
         ├─ README.md
         ├─ src
         │  ├─ app.config.ts
         │  ├─ app.scss
         │  ├─ app.ts
         │  ├─ assets
         │  │  └─ svg
         │  │     ├─ custom-icon.svg
         │  │     └─ wind.svg
         │  ├─ components
         │  │  ├─ address
         │  │  │  └─ index.vue
         │  │  ├─ basic-layout
         │  │  │  └─ index.vue
         │  │  ├─ Card
         │  │  │  └─ index.vue
         │  │  ├─ custom-navbar
         │  │  │  └─ index.vue
         │  │  └─ design-cell
         │  │     └─ index.vue
         │  ├─ composables
         │  │  ├─ index.ts
         │  │  ├─ route.ts
         │  │  └─ router.ts
         │  ├─ constants
         │  │  ├─ common.ts
         │  │  ├─ index.ts
         │  │  └─ service.ts
         │  ├─ custom-tab-bar
         │  │  └─ index.vue
         │  ├─ hooks
         │  │  ├─ business
         │  │  │  ├─ index.ts
         │  │  │  ├─ use-count-down.ts
         │  │  │  └─ use-sms-code.ts
         │  │  ├─ common
         │  │  │  ├─ index.ts
         │  │  │  ├─ use-boolean.ts
         │  │  │  ├─ use-context.ts
         │  │  │  └─ use-loading.ts
         │  │  └─ index.ts
         │  ├─ index.html
         │  ├─ interface
         │  │  ├─ business
         │  │  │  ├─ auth.ts
         │  │  │  └─ index.ts
         │  │  └─ index.ts
         │  ├─ mock
         │  ├─ package
         │  │  └─ package-a
         │  │     ├─ confirmOrder
         │  │     │  └─ index.vue
         │  │     ├─ context
         │  │     │  └─ user.ts
         │  │     ├─ Details
         │  │     │  └─ index.vue
         │  │     ├─ DragView
         │  │     │  └─ index.vue
         │  │     ├─ edit
         │  │     │  └─ index.vue
         │  │     ├─ fillOut
         │  │     │  └─ index.vue
         │  │     ├─ harvestAddress
         │  │     │  ├─ dist
         │  │     │  │  └─ zh-address-parse.min.js
         │  │     │  ├─ index.vue
         │  │     │  └─ setAddress
         │  │     │     └─ index.vue
         │  │     ├─ search
         │  │     │  └─ index.vue
         │  │     ├─ searchOrder
         │  │     │  └─ index.vue
         │  │     ├─ termsUse
         │  │     │  └─ index.vue
         │  │     ├─ updateOrder
         │  │     │  └─ index.vue
         │  │     └─ verification
         │  │        └─ index.vue
         │  ├─ pages
         │  │  ├─ classification
         │  │  │  ├─ components
         │  │  │  │  └─ card.vue
         │  │  │  └─ index.vue
         │  │  ├─ inventory
         │  │  │  └─ index.vue
         │  │  ├─ login
         │  │  │  └─ index.vue
         │  │  ├─ my
         │  │  │  ├─ index.vue
         │  │  │  └─ myImgCanvas.vue
         │  │  ├─ orders
         │  │  │  ├─ index.vue
         │  │  │  ├─ index.vue.txt
         │  │  │  ├─ orders.vue
         │  │  │  └─ orders.vue.txt
         │  │  ├─ products
         │  │  │  ├─ components
         │  │  │  │  └─ shoppingCard.vue
         │  │  │  ├─ index.vue
         │  │  │  └─ js.vue
         │  │  └─ shoppingCart
         │  │     └─ index.vue
         │  ├─ plugins
         │  │  ├─ assets.ts
         │  │  └─ index.ts
         │  ├─ service
         │  │  ├─ api
         │  │  │  ├─ demo.ts
         │  │  │  └─ index.ts
         │  │  ├─ index.ts
         │  │  └─ request
         │  │     ├─ helpers.ts
         │  │     ├─ index.ts
         │  │     ├─ instansce.ts
         │  │     └─ request.ts
         │  ├─ service.zip
         │  ├─ store
         │  │  ├─ index.ts
         │  │  └─ modules
         │  │     ├─ app
         │  │     │  └─ index.ts
         │  │     ├─ auth
         │  │     │  ├─ helpers.ts
         │  │     │  └─ index.ts
         │  │     ├─ index.ts
         │  │     └─ theme
         │  │        └─ index.ts
         │  ├─ styles
         │  │  ├─ common.scss
         │  │  ├─ h5.scss
         │  │  ├─ index.scss
         │  │  └─ reset.scss
         │  ├─ typings
         │  │  ├─ auth.d.ts
         │  │  ├─ common.d.ts
         │  │  ├─ components.d.ts
         │  │  ├─ global.d.ts
         │  │  ├─ service.d.ts
         │  │  ├─ storage.d.ts
         │  │  └─ utils.d.ts
         │  └─ utils
         │     ├─ common
         │     │  ├─ index.ts
         │     │  ├─ number.ts
         │     │  ├─ pattern.ts
         │     │  └─ typeof.ts
         │     ├─ index.ts
         │     └─ storage
         │        ├─ index.ts
         │        └─ local.ts
         ├─ tsconfig.json
         └─ unocss.config.ts

```





















